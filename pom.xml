<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/>
    </parent>

    <groupId>com.yyigou</groupId>
    <artifactId>service-ddc-dmp</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>service-ddc-dmp</name>
    <description>Parent pom for ddc-dmp project</description>

    <modules>
        <module>ddc-dmp-common</module>
        <module>ddc-dmp-dao</module>
        <module>ddc-dmp-service</module>
        <module>ddc-dmp-web</module>
        <module>ddc-dmp-model</module>
        <module>ddc-dmp-manager</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <spring-boot.version>3.5.3</spring-boot.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <hutool.version>5.8.25</hutool.version>
        <lombok.version>1.18.30</lombok.version>
        <fastjson.version>1.2.83</fastjson.version>
        <mybatis-plus-generator.version>3.5.5</mybatis-plus-generator.version>
        <velocity.version>2.3</velocity.version>
        <spring-boot-admin.version>3.3.4</spring-boot-admin.version>
        <druid.version>1.2.21</druid.version>
        <logstash-logback-encoder.version>7.4</logstash-logback-encoder.version>
        <common-dubbo-registry-zookeeper.version>2.0.0-SNAPSHOT</common-dubbo-registry-zookeeper.version>
        <common-meta-base.version>2.0.0-SNAPSHOT</common-meta-base.version>
        <common_base_version>2.0.0-SNAPSHOT</common_base_version>
        <pagehelper-spring-boot-starter.version>2.1.1</pagehelper-spring-boot-starter.version>
        <version.calcite>1.40.0</version.calcite>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Common Module -->
            <dependency>
                <groupId>com.yyigou</groupId>
                <artifactId>ddc-dmp-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- DAO Module -->
            <dependency>
                <groupId>com.yyigou</groupId>
                <artifactId>ddc-dmp-dao</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Service Module -->
            <dependency>
                <groupId>com.yyigou</groupId>
                <artifactId>ddc-dmp-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yyigou</groupId>
                <artifactId>ddc-dmp-model</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yyigou</groupId>
                <artifactId>ddc-dmp-manager</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- MyBatis-Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- Hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>


            <!-- Druid数据源 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- Fastjson -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- MyBatis-Plus Generator -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>

            <!-- Velocity Template Engine -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Spring Boot Admin Client -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Spring Boot Admin Server -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Logstash Logback Encoder for JSON logging -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>


            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-spring-boot-starter.version}</version>
            </dependency>


            <!-- Spring Boot依赖管理 -->
            <!-- <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency> -->

            <dependency>
                <groupId>org.apache.calcite</groupId>
                <artifactId>calcite-core</artifactId>
                <version>${version.calcite}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.calcite</groupId>
                <artifactId>calcite-linq4j</artifactId>
                <version>${version.calcite}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>