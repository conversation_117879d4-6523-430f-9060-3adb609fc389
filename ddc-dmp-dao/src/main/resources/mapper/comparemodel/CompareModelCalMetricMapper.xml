<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelCalMetricMapper">

    <resultMap id="BaseResultMap" type="com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelCalMetric">
            <id property="id" column="id" />
            <result property="compareModelCalculatedMetricNo" column="compare_model_calculated_metric_no" />
            <result property="enterpriseNo" column="enterprise_no" />
            <result property="compareModelNo" column="compare_model_no" />
            <result property="metricCode" column="metric_code" />
            <result property="metricName" column="metric_name" />
            <result property="sort" column="sort" />
            <result property="description" column="description" />
            <result property="baselineMetricScopeType" column="baseline_metric_scope_type" />
            <result property="baselineMetricScope" column="baseline_metric_scope" />
            <result property="compareMetricScopeType" column="compare_metric_scope_type" />
            <result property="compareMetricScope" column="compare_metric_scope" />
            <result property="expressionType" column="expression_type" />
            <result property="expression" column="expression" />
            <result property="formatConfig" column="format_config" />
            <result property="enableStatus" column="enable_status" />
            <result property="status" column="status" />
            <result property="deleted" column="deleted" />
            <result property="createNo" column="create_no" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="modifyNo" column="modify_no" />
            <result property="modifyName" column="modify_name" />
            <result property="modifyTime" column="modify_time" />
            <result property="opTimestamp" column="op_timestamp" />
    </resultMap>

    <sql id="Base_Column_List">
        id,compare_model_calculated_metric_no,enterprise_no,compare_model_no,metric_code,metric_name,
        sort,description,baseline_metric_scope_type,baseline_metric_scope,compare_metric_scope_type,
        compare_metric_scope,expression_type,expression,format_config,enable_status,
        status,deleted,create_no,create_name,create_time,
        modify_no,modify_name,modify_time,op_timestamp
    </sql>
</mapper>
