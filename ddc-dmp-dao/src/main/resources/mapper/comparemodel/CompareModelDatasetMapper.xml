<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelDatasetMapper">

    <resultMap id="BaseResultMap" type="com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelDataset">
            <id property="id" column="id" />
            <result property="enterpriseNo" column="enterprise_no" />
            <result property="compareModelNo" column="compare_model_no" />
            <result property="datasetNo" column="dataset_no" />
            <result property="datasetType" column="dataset_type" />
            <result property="sort" column="sort" />
            <result property="joinType" column="join_type" />
            <result property="status" column="status" />
            <result property="deleted" column="deleted" />
            <result property="createNo" column="create_no" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="modifyNo" column="modify_no" />
            <result property="modifyName" column="modify_name" />
            <result property="modifyTime" column="modify_time" />
            <result property="opTimestamp" column="op_timestamp" />
    </resultMap>

    <sql id="Base_Column_List">
        id,enterprise_no,compare_model_no,dataset_no,dataset_type,sort,
        join_type,status,deleted,create_no,create_name,
        create_time,modify_no,modify_name,modify_time,op_timestamp
    </sql>
</mapper>
