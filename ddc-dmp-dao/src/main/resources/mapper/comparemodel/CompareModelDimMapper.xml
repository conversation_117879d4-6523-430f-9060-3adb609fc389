<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelDimMapper">

    <resultMap id="BaseResultMap" type="com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelDim">
            <id property="id" column="id" />
            <result property="compareModelDimNo" column="compare_model_dim_no" />
            <result property="enterpriseNo" column="enterprise_no" />
            <result property="dimType" column="dim_type" />
            <result property="compareModelNo" column="compare_model_no" />
            <result property="datasetNo" column="dataset_no" />
            <result property="fieldCode" column="field_code" />
            <result property="sort" column="sort" />
            <result property="status" column="status" />
            <result property="deleted" column="deleted" />
            <result property="createNo" column="create_no" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="modifyNo" column="modify_no" />
            <result property="modifyName" column="modify_name" />
            <result property="modifyTime" column="modify_time" />
            <result property="opTimestamp" column="op_timestamp" />
    </resultMap>

    <sql id="Base_Column_List">
        id,compare_model_dim_no,enterprise_no,dim_type,compare_model_no,dataset_no,
        field_code,sort,status,deleted,create_no,
        create_name,create_time,modify_no,modify_name,modify_time,
        op_timestamp
    </sql>
</mapper>
