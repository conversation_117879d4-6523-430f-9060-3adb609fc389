<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelMapper">

    <resultMap id="BaseResultMap" type="com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel">
            <id property="id" column="id" />
            <result property="enterpriseNo" column="enterprise_no" />
            <result property="modelNo" column="model_no" />
            <result property="modelCode" column="model_code" />
            <result property="modelName" column="model_name" />
            <result property="compareDatasetNo" column="compare_dataset_no" />
            <result property="description" column="description" />
            <result property="status" column="status" />
            <result property="enableStatus" column="enable_status" />
            <result property="deleted" column="deleted" />
            <result property="createNo" column="create_no" />
            <result property="createName" column="create_name" />
            <result property="createTime" column="create_time" />
            <result property="modifyNo" column="modify_no" />
            <result property="modifyName" column="modify_name" />
            <result property="modifyTime" column="modify_time" />
            <result property="version" column="version" />
            <result property="opTimestamp" column="op_timestamp" />
    </resultMap>

    <sql id="Base_Column_List">
        id,enterprise_no,model_no,model_code,model_name,compare_dataset_no,
        description,status,enable_status,deleted,create_no,
        create_name,create_time,modify_no,modify_name,modify_time,
        version,op_timestamp
    </sql>
</mapper>
