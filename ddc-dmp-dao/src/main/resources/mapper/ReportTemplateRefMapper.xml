<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yyigou.ddc.dmp.dao.mapper.ReportTemplateRefMapper">

    <select id="selectListByManage"
            resultType="com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateManagePageRes">
        select
            tmp.*,
            rt_ref.id as ref_id,
            rt_ref.enterprise_no as ref_enterprise_no,
            rt_ref.enable_status as ref_enable_status
        from dmp_report_template_ref rt_ref
        right join (
            select
                rt.id,
                rt.template_code,
                rt.template_name,
                rt.template_mode,
                rt.dataset_no,
                rt.plan_no,
                rt.row_fields,
                rt.column_fields,
                rt.value_fields,
                rt.biz_config,
                rt.filter_config,
                rt.config_json,
                rt.description,
                rt.status,
                rt.enable_status
            from dmp_report_template2 rt
            where rt.deleted = 0
                and (
                    rt.enterprise_no = #{enterpriseNo}
                    or  (rt.preset = 1 and rt.enable_status = 1 )
                )
        ) tmp on rt_ref.template_code = tmp.template_code and rt_ref.deleted = 0


    </select>
</mapper>