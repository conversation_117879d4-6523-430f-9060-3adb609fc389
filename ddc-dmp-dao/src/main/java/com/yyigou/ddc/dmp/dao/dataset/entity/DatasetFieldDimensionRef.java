package com.yyigou.ddc.dmp.dao.dataset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("v2_dmp_dataset_dimension_ref")
public class DatasetFieldDimensionRef {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String enterpriseNo;
    private String datasetNo;
    private String datasetFieldNo;
    private String dimensionNo;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private Timestamp opTimestamp;
}
