package com.yyigou.ddc.dmp.dao.comparemodel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 比对模型
 * @TableName dmp_compare_model
 */
@TableName(value ="dmp_compare_model")
@Data
public class CompareModel {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private String enterpriseNo;

    /**
     * 
     */
    private String modelNo;

    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 比对数据集编号
     */
    private String compareDatasetNo;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

    /**
     * 启用状态 1-启用，2-禁用
     */
    private Integer enableStatus;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 创建人编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createNo;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 操作人编号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyNo;

    /**
     * 操作人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyTime;

    /**
     * 版本
     */
    private Integer version;


}