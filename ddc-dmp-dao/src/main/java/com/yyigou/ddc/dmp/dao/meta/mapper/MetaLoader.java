package com.yyigou.ddc.dmp.dao.meta.mapper;

import com.yyigou.ddc.dmp.dao.meta.entity.Columns;
import com.yyigou.ddc.dmp.dao.meta.entity.Schemata;
import com.yyigou.ddc.dmp.dao.meta.entity.Tables;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Types;
import java.util.List;

@Component
public class MetaLoader {
    private final static String LOAD_SCHEMATA_SQL = "SELECT * FROM information_schema.schemata";
    private final static String GET_SCHEMATA_SQL = "SELECT * FROM information_schema.schemata where schema_name = ?";
    private final static String LOAD_TABLES_SQL = "SELECT * FROM information_schema.tables WHERE table_schema = ?";
    private final static String GET_TABLES_SQL = "SELECT * FROM information_schema.tables WHERE table_schema = ? AND table_name = ?";
    private final static String LOAD_COLUMNS_SQL = "SELECT * FROM information_schema.columns WHERE table_schema = ? AND table_name = ?";
    private final static String GET_COLUMNS_SQL = "SELECT * FROM information_schema.columns WHERE table_schema = ? AND table_name = ? AND column_name = ?";
    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;

    public List<Schemata> loadSchematas() {
        return dorisJdbcTemplate.query(LOAD_SCHEMATA_SQL, new BeanPropertyRowMapper<>(Schemata.class));
    }

    public Schemata getSchemata(String schemaName) {
        List<Schemata> list = dorisJdbcTemplate.query(GET_SCHEMATA_SQL, new Object[]{schemaName}, new int[]{Types.VARCHAR}, new BeanPropertyRowMapper<>(Schemata.class));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.getFirst();
    }

    public List<Tables> loadTables(String schemaName) {
        return dorisJdbcTemplate.query(LOAD_TABLES_SQL, new Object[]{schemaName}, new int[]{Types.VARCHAR}, new BeanPropertyRowMapper<>(Tables.class));
    }

    public Tables getTable(String schemaName, String tableName) {
        List<Tables> list = dorisJdbcTemplate.query(GET_TABLES_SQL, new Object[]{schemaName, tableName}, new int[]{Types.VARCHAR, Types.VARCHAR}, new BeanPropertyRowMapper<>(Tables.class));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.getFirst();
    }

    public List<Columns> loadColumns(String schemaName, String tableName) {
        return dorisJdbcTemplate.query(LOAD_COLUMNS_SQL, new Object[]{schemaName, tableName}, new int[]{Types.VARCHAR, Types.VARCHAR}, new BeanPropertyRowMapper<>(Columns.class));
    }

    public Columns getColumn(String schemaName, String tableName, String columnName) {
        List<Columns> list = dorisJdbcTemplate.query(GET_COLUMNS_SQL, new Object[]{schemaName, tableName, columnName}, new int[]{Types.VARCHAR, Types.VARCHAR, Types.VARCHAR}, new BeanPropertyRowMapper<>(Columns.class));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.getFirst();
    }
}