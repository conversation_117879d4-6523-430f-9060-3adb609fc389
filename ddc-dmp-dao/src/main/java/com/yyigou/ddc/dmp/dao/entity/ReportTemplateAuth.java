package com.yyigou.ddc.dmp.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 报表模板授权关系表
 * @TableName dmp_report_template_auth
 */
@TableName(value ="dmp_report_template_auth")
@Data
public class ReportTemplateAuth {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 被授权人用户编号
     */
    private String authToUserNo;

    /**
     * 被授权人用户名称
     */
    private String authToUserName;

    /**
     * 授权组织编号
     */
    private String authOrgNo;

    /**
     * 报表模板编码
     */
    private String templateCode;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 创建人编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createNo;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 修改人编号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyNo;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyTime;


}