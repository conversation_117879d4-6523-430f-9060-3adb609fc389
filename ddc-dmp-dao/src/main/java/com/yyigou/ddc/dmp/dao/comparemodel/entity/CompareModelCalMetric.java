package com.yyigou.ddc.dmp.dao.comparemodel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 比对模型计算指标
 * @TableName dmp_compare_model_cal_metric
 */
@TableName(value ="dmp_compare_model_cal_metric")
@Data
public class CompareModelCalMetric {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 比对模型计算指标编号
     */
    private String calMetricNo;

    /**
     * 租户id
     */
    private String enterpriseNo;

    /**
     * 模型编号
     */
    private String compareModelNo;

    /**
     * 指标编码
     */
    private String calMetricCode;

    /**
     * 指标名称
     */
    private String calMetricName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 绑定的基准指标范围类型.1-全部，2-指定
     */
    private Integer baselineMetricScopeType;

    /**
     * 绑定的基准指标范围。json数组
     */
    private String baselineMetricScope;

    /**
     * 绑定的比对指标范围类型.1-全部，2-指定
     */
    private Integer compareMetricScopeType;

    /**
     * 绑定的比对指标范围。json数组
     */
    private String compareMetricScope;

    /**
     * 计算表达式
     */
    private String expression;

    /**
     * 格式化内容
     */
    private String formatConfig;

    /**
     * 启用状态
     */
    private Integer enableStatus;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 创建人编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createNo;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 操作人编号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyNo;

    /**
     * 操作人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyTime;


}