package com.yyigou.ddc.dmp.dao.dimension.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("v2_dmp_dimension")
public class DimensionField {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String enterpriseNo;
    private String dimensionNo;
    private String fieldCode;
    private String fieldSemantic;
    private String description;
    private Integer displayOrder;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}
