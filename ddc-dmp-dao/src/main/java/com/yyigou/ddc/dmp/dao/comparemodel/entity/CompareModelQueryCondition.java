package com.yyigou.ddc.dmp.dao.comparemodel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 比对模型关联查询条件表
 * @TableName dmp_compare_model_query_condition
 */
@TableName(value ="dmp_compare_model_query_condition")
@Data
public class CompareModelQueryCondition {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 唯一编号
     */
    private String compareModelQueryConditionNo;

    /**
     * 租户id
     */
    private String enterpriseNo;

    /**
     * 数据集编号
     */
    private String datasetNo;

    /**
     * 条件类型。1-主数据集查询条件，2-基准指标查询条件
     */
    private Integer conditionType;

    /**
     * 查询条件编号
     */
    private String datasetQueryConditionNo;

    /**
     * 比对模型编号
     */
    private String compareModelNo;

    /**
     * 比对模型关联基准指标编号
     */
    private String compareModelMetricNo;

    /**
     * 是否是基准指标的公共查询条件
     */
    private Integer isCommon;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 创建人编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createNo;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 操作人编号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyNo;

    /**
     * 操作人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyTime;


}