package com.yyigou.ddc.dmp.dao.mapper;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface DmpBaseMapper <T> extends BaseMapper<T> {

    /**
     * 因为MybatisPlus的updateById和update方法默认不更新null字段，所以增加这个方法
     * @param enterpriseNo
     * @param entity
     * @return
     */
    default int updateAllById(String enterpriseNo, T entity) {
        Object id = getTableId(entity);
        if (id == null || StrUtil.isBlank(enterpriseNo)) {
            return 0;
        }
        UpdateWrapper<T> wrapper = new UpdateWrapper<T>();
        wrapper.eq("id", id);
        wrapper.eq("enterprise_no", enterpriseNo);
        // 获取Class对象
        Class<?> clazz = entity.getClass();
        // 获取所有声明的字段
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {

            // 允许访问私有字段
            field.setAccessible(true);
            String lineName = field.getName().replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();

            if("serial_version_uid".equals(lineName)){
                continue;
            }
            try {
                wrapper.set(lineName, field.get(entity));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        return update(entity, wrapper);
    }

    /**
     * 获取主键信息
     *
     * @param entity
     * @return
     */
    default Object getTableId(T entity) {
        Map<String, Field> beanPropertyFields = ReflectUtil.getFieldMap(entity.getClass());
        for (Field value : beanPropertyFields.values()) {
            if (Modifier.isTransient(value.getModifiers())
                    || Modifier.isStatic(value.getModifiers())) {
                continue;
            }
            value.setAccessible(true);
            TableId annotation = value.getAnnotation(TableId.class);
            if (annotation != null) {
                try {
                    return value.get(entity);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("请输入id");
                }
            }
        }
        return new BusinessException("请先用@TableId注解标明主键");
    }


    /**
     * 批量新增，addBatch 根本没用BatchExecutor，性能其实也一般
     *
     * @param list 需要新增的类
     * @return 需要集合
     */
    default  <E extends DmpBaseMapper<T>> boolean updateBatchByIdAndEnterpriseNo(List<T> list , String enterpriseNo,Class<E> mapperClass) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        // 使用批处理实现批量插入
        SqlSessionFactory sqlSessionFactory = SpringUtil.getBean(SqlSessionFactory.class);
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);

        E mapper = sqlSession.getMapper(mapperClass);
        for (T t : list) {
            Object id = getTableId(t);
            UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("enterprise_no", enterpriseNo)
                    .eq("id", id);
            mapper.update(t, updateWrapper);
        }
        sqlSession.commit();
        sqlSession.clearCache();
        return true;
    }

}
