package com.yyigou.ddc.dmp.dao.compareplan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 比价方案关联指标表
 * @TableName dmp_compare_plan_metric
 */
@TableName(value ="dmp_compare_plan_metric")
@Data
public class ComparePlanMetric {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 编号
     */
    private String comparePlanMetricNo;

    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 模型指标关联编号
     */
    private String compareModelMetricNo;

    /**
     * 数据集编号
     */
    private String comparePlanNo;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 创建人编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createNo;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 操作人编号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyNo;

    /**
     * 操作人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyTime;

}