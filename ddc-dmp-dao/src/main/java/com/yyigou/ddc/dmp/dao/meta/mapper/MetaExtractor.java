package com.yyigou.ddc.dmp.dao.meta.mapper;

import com.mysql.cj.jdbc.MysqlDataSource;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.adapter.jdbc.JdbcSchema;
import org.apache.calcite.config.Lex;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.schema.SchemaPlus;
import org.apache.calcite.schema.impl.AbstractSchema;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlOperatorTable;
import org.apache.calcite.sql.fun.SqlLibrary;
import org.apache.calcite.sql.fun.SqlLibraryOperatorTableFactory;
import org.apache.calcite.sql.fun.SqlStdOperatorTable;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.util.SqlOperatorTables;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.apache.calcite.tools.FrameworkConfig;
import org.apache.calcite.tools.Frameworks;
import org.apache.calcite.tools.Planner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j
public class MetaExtractor {
    private final AtomicBoolean isWarmedUp = new AtomicBoolean(false);
    @Value("${spring.datasource.meta.schema-dwd}")
    private String schema_dwd = "dwd";
    @Value("${spring.datasource.meta.mysql.host}")
    private String dsrpHost;
    @Value("${spring.datasource.meta.mysql.port}")
    private int dsrpPort;
    @Value("${spring.datasource.meta.mysql.username}")
    private String dsrpUsername;
    @Value("${spring.datasource.meta.mysql.password}")
    private String dsrpPassword;
    @Value("${spring.datasource.meta.schema-yyigou-dsrp}")
    private String schema_yyigou_dsrp = "yyigou_dsrp";
    @Value("${spring.datasource.meta.doris.host}")
    private String dorisHost;
    @Value("${spring.datasource.meta.doris.port}")
    private int dorisPort;
    @Value("${spring.datasource.meta.doris.username}")
    private String dorisUsername;
    @Value("${spring.datasource.meta.doris.password}")
    private String dorisPassword;
    @Value("${spring.datasource.meta.mysql-catalog}")
    private String mysql_catalog = "mysql_rds";
    @Value("classpath:warmup.sql")
    private org.springframework.core.io.Resource resource;
    private SchemaPlus rootSchema;

    @PostConstruct
    private void init() throws Exception {
        rootSchema = buildSchema();

        new Thread(
                () -> {
                    try {
                        log.warn("warm up...");
                        long l = System.currentTimeMillis();

                        InputStream inputStream = resource.getInputStream();
                        String sql = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                        inputStream.close();

                        sql = String.format(sql, schema_dwd, schema_dwd, mysql_catalog, schema_dwd, mysql_catalog);

                        validateSQL(sql, true);

                        log.warn("warm up cost: {}ms", System.currentTimeMillis() - l);
                    } catch (Exception e) {
                        log.error("warm up error", e);
                    }

                    isWarmedUp.set(true);
                }
        ).start();

    }

    private Map<String, Object> sqlTrans(String rawSql) {
        // 删除空格
        String sql = rawSql.trim();

        // 删除末尾的分号
        String lastChar = sql.substring(sql.length() - 1);
        if (";".equals(lastChar)) {
            sql = sql.substring(0, sql.length() - 1);
        }

        // 因为if表达式作为where条件，不影响字段解析，所以直接删除
        // 删除 ${if(...)} 表达式
//        sql = sql.replaceAll("\\$\\{if\\([^}]*\\)\\}", "");

        // 因为简单变量不影响字段解析，所以直接删除
        // 删除简单变量 ${xxx}
        sql = sql.replaceAll("\\$\\{[^}]+\\}", "");

        // 替换 IFNULL 为 COALESCE，否则无法识别IFNULL函数
        sql = sql.replace("IFNULL", "COALESCE");

        // 处理 no 字段，作为关键字不能裸用，NO ACTION（外键约束）、NO INDEX（某些数据库的 DDL）、NO WAIT（锁超时选项）
        sql = sql.replaceAll("\\.no", ".`no`");
        sql = sql.replaceAll("\\sno", " `no`");

        // 处理中文，由于解析时不使用utf8mb4，所以替换中文为Unicode
        // 存储 unicode -> 中文
        Map<String, String> unicodeToChineseMap = new HashMap<>();
        sql = replaceChinese(unicodeToChineseMap, sql);

        Map<String, Object> result = new HashMap<>();
        result.put("sql", sql);
        result.put("unicodeToChineseMap", unicodeToChineseMap);

        return result;
    }

    private Map<String, MysqlDataSource> getDatasource() {
        Map<String, MysqlDataSource> datasourceMap = new HashMap<>();

        MysqlDataSource dataSource_yyigou_dw = new MysqlDataSource();
        dataSource_yyigou_dw.setUrl(String.format("jdbc:mysql://%s:%s/%s", dorisHost, dorisPort, schema_dwd));
        dataSource_yyigou_dw.setServerName(dorisHost);
        dataSource_yyigou_dw.setPortNumber(dorisPort);
        dataSource_yyigou_dw.setDatabaseName(schema_dwd);
        dataSource_yyigou_dw.setUser(dorisUsername);
        dataSource_yyigou_dw.setPassword(dorisPassword);
        datasourceMap.put(schema_dwd, dataSource_yyigou_dw);

        MysqlDataSource dataSource_yyigou_dsrp = new MysqlDataSource();
        dataSource_yyigou_dsrp.setUrl(String.format("jdbc:mysql://%s:%s/%s", dsrpHost, dsrpPort, schema_yyigou_dsrp));
        dataSource_yyigou_dsrp.setServerName(dsrpHost);
        dataSource_yyigou_dsrp.setPortNumber(dsrpPort);
        dataSource_yyigou_dsrp.setDatabaseName(schema_yyigou_dsrp);
        dataSource_yyigou_dsrp.setUser(dsrpUsername);
        dataSource_yyigou_dsrp.setPassword(dsrpPassword);
        datasourceMap.put(schema_yyigou_dsrp, dataSource_yyigou_dsrp);

        return datasourceMap;
    }

    private SchemaPlus buildSchema() throws Exception {
        Map<String, MysqlDataSource> datasource = getDatasource();

        Properties props = new Properties();
        Connection connection = DriverManager.getConnection("jdbc:calcite:", props);
        CalciteConnection calciteConn = connection.unwrap(CalciteConnection.class);
        SchemaPlus rootSchema = calciteConn.getRootSchema();

        rootSchema.add(schema_dwd, JdbcSchema.create(
                rootSchema, schema_dwd, datasource.get(schema_dwd), null, schema_dwd));

        SchemaPlus mysqlCatalog = rootSchema.add(mysql_catalog, new AbstractSchema());
        mysqlCatalog.add(schema_yyigou_dsrp, JdbcSchema.create(
                mysqlCatalog, schema_yyigou_dsrp, datasource.get(schema_yyigou_dsrp), null, schema_yyigou_dsrp));

        return rootSchema;
    }

    private Planner buildPlanner(SchemaPlus catalog) {
        SqlOperatorTable chainedTable = SqlOperatorTables.chain(
                SqlStdOperatorTable.instance(),
                SqlLibraryOperatorTableFactory.INSTANCE.getOperatorTable(SqlLibrary.MYSQL)
        );

        FrameworkConfig frameworkConfig = Frameworks.newConfigBuilder()
                .parserConfig(SqlParser.configBuilder()
                        .setConformance(SqlConformanceEnum.MYSQL_5)
                        .setLex(Lex.MYSQL)
                        .setCaseSensitive(false)
                        .setIdentifierMaxLength(256)
                        .build())
                .operatorTable(chainedTable)
                .defaultSchema(catalog)
                .build();

        return Frameworks.getPlanner(frameworkConfig);
    }

    public void validateSQL(String rawSql) throws BusinessException {
        validateSQL(rawSql, false);
    }

    private void validateSQL(String rawSql, boolean warmup) throws BusinessException {
        try {
            if (!warmup && !isWarmedUp.get()) {
                throw new BusinessException("元数据未载入完成，请稍后重试。");
            }


            Map<String, Object> transResult = sqlTrans(rawSql);
            String sql = transResult.get("sql").toString();

            try (Planner planner = buildPlanner(rootSchema)) {
                SqlNode parsed = planner.parse(sql);
                planner.validate(parsed);
            }

        } catch (Exception e) {
            log.error("validateSQL error:", e);

            throw new BusinessException(e.getMessage());
        }
    }


    // 替换中文为 __UNICODE_xxxx__ 格式
    private String replaceChinese(Map<String, String> unicodeToChineseMap, String input) {
        StringBuilder sb = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (Character.UnicodeBlock.of(c).toString().contains("CJK")) {
                String unicodeKey = String.format("__UNICODE_%04X__", (int) c);
                unicodeToChineseMap.put(unicodeKey, String.valueOf(c));
                sb.append(unicodeKey);
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }
}
