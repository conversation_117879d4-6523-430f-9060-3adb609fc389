package com.yyigou.ddc.dmp.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 报表模板引用表
 */
@Data
@TableName(value = "dmp_report_template_ref")
public class ReportTemplateRef {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "enterprise_no")
    private String enterpriseNo;

    /**
     * 授权用户编号
     */
    @TableField(value = "employee_no")
    private String employeeNo;

    /**
     * 配置id
     */
    @TableField(value = "template_code")
    private String templateCode;

    /**
     * 启用状态 1-启用，2-禁用
     */
    @TableField(value = "enable_status")
    private Integer enableStatus;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @TableField(fill = FieldFill.INSERT)
    private Boolean status;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Boolean deleted;

    /**
     * 创建人编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createNo;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 操作人编号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyNo;

    /**
     * 操作人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyTime;


}