package com.yyigou.ddc.dmp.dao.compareplan.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 比价方案关联显示字段表
 * @TableName dmp_compare_plan_field
 */
@TableName(value ="dmp_compare_plan_field")
@Data
public class ComparePlanField {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户编号
     */
    private String enterpriseNo;

    /**
     * 比价方案编号
     */
    private String comparePlanNo;

    /**
     * 字段类型。1-行维度，2-列维度，3-值维度
     */
    private Integer fieldType;

    /**
     * 字段源类型。1-比对对象，2-比对维度，3-比对指标，4-基准指标，5-计算指标
     */
    private Integer sourceType;

    /**
     * 来源编号
     */
    private String sourceNo;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 创建人编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createNo;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    /**
     * 操作人编号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyNo;

    /**
     * 操作人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyTime;

}