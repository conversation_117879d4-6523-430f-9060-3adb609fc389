//package com.yyigou.ddc.dmp.dao.generator;
//
//import com.baomidou.mybatisplus.generator.AutoGenerator;
//import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
//import com.baomidou.mybatisplus.generator.config.GlobalConfig;
//import com.baomidou.mybatisplus.generator.config.PackageConfig;
//import com.baomidou.mybatisplus.generator.config.StrategyConfig;
//import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
//import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
//
//public class CodeGenerator {
//    public static void main(String[] args) {
//        // 1. 全局配置
//        GlobalConfig gc = new GlobalConfig.Builder()
//                .author("AutoGenerator")
//                .outputDir(projectPath + "/ddc-dmp-dao/src/main/java")
//                .fileOverride(true)
//                .serviceName("%sService")
//                .baseResultMap(true)
//                .baseColumnList(true)
//                .build();
//        String projectPath = System.getProperty("user.dir");
//
//        // DAO模块输出配置
//        gc.setOutputDir(projectPath + ("/ddc-dmp-dao/src/main/java"));
//        gc.setAuthor("AutoGenerator");
//        gc.setOpen(false);
//        gc.setFileOverride(true);
//        gc.setServiceName("%sService"); // 去掉Service接口的I前缀
//        gc.setBaseResultMap(true);
//        gc.setBaseColumnList(true);
//
//        // 2. 数据源配置
//        DataSourceConfig dsc = new DataSourceConfig.Builder()
//                .url("***************************************************************************************************************")
//                .driverName("com.mysql.cj.jdbc.Driver")
//                .username("root")
//                .password("root")
//                .build();
//
//        // 3. 包配置
//        PackageConfig pc = new PackageConfig.Builder()
//                .parent("com.yyigou.ddc.dmp")
//                .build();
//
//        // 4. 策略配置
//        StrategyConfig strategy = new StrategyConfig.Builder()
//                .naming(NamingStrategy.underline_to_camel)
//                .columnNaming(NamingStrategy.underline_to_camel)
//                .entityLombokModel(true)
//                .restControllerStyle(true)
//                .tablePrefix("dmp_")
//                .include(
//                        "dmp_analysis_subject",
//                        "dmp_dataset",
//                        "dmp_analysis_subject_dataset",
//                        "dmp_data_model",
//                        "dmp_data_model_field",
//                        "dmp_dataset_field_ref",
//                        "dmp_report_template",
//                        "dmp_report_template_share"
//                )
//                .controllerMappingHyphenStyle(true)
//                .build();
//
//        // 5. 整合配置
//        AutoGenerator mpg = new AutoGenerator.Builder()
//                .globalConfig(gc)
//                .dataSource(dsc)
//                .packageInfo(pc)
//                .strategy(strategy)
//                .templateEngine(new VelocityTemplateEngine())
//                .build();
//
//        // 生成DAO层代码(Entity/Mapper)
//        pc.moduleName("dao").entity("entity").mapper("mapper");
//        pc.service(null).serviceImpl(null).controller(null);
//        gc.outputDir(projectPath + "/ddc-dmp-dao/src/main/java");
//        mpg.execute();
//
//        // 生成Service层代码
//        pc.moduleName("service").entity(null).mapper(null);
//        pc.service("").serviceImpl("impl").controller(null);
//        gc.outputDir(projectPath + "/ddc-dmp-service/src/main/java");
//        mpg.execute();
//
//        // 生成Web层代码(Controller)
//        pc.moduleName("web.controller").entity(null).mapper(null);
//        pc.service(null).serviceImpl(null).controller("");
//        gc.outputDir(projectPath + "/ddc-dmp-web/src/main/java");
//        mpg.execute();
//    }
//}