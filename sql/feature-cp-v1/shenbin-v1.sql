create table yyigou_ddc_dmp.dmp_analysis_subject
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(64)                         not null comment '租户编号',
    subject_no    varchar(64)                         not null comment '分析主题唯一标识',
    subject_name  varchar(255)                        not null comment '分析主题名称',
    description   text                                null comment '主题描述',
    status        tinyint   default 1                 not null comment '数据是否有效：0-无效，1-有效',
    deleted       tinyint   default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(64)                         null comment '创建人编号',
    create_name   varchar(128)                        null comment '创建人名称',
    create_time   varchar(32)                         null comment '创建时间',
    modify_no     varchar(64)                         null comment '修改人编号',
    modify_name   varchar(128)                        null comment '修改人名称',
    modify_time   varchar(32)                         null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
)
    comment '分析主题信息表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_analysis_subject (enterprise_no);

create table yyigou_ddc_dmp.dmp_analysis_subject_dataset_ref
(
    id                     bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no          varchar(64)                         not null comment '租户编号',
    subject_dataset_ref_no varchar(64)                         not null comment '分析主题与数据集关系编号',
    subject_no             varchar(64)                         not null comment '分析主题ID',
    dataset_no             varchar(64)                         not null comment '数据集ID',
    status                 tinyint   default 1                 not null comment '数据是否有效：0-无效，1-有效',
    deleted                tinyint   default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no              varchar(64)                         null comment '创建人编号',
    create_name            varchar(128)                        null comment '创建人名称',
    create_time            varchar(32)                         null comment '创建时间',
    modify_no              varchar(64)                         null comment '修改人编号',
    modify_name            varchar(128)                        null comment '修改人名称',
    modify_time            varchar(32)                         null comment '修改时间',
    op_timestamp           timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
)
    comment '分析主题与数据集关联关系表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_analysis_subject_dataset_ref (enterprise_no);

create table yyigou_ddc_dmp.dmp_data_model
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(255)                        null comment '租户编号',
    model_no      varchar(255)                        null comment '模型唯一标识，UUID',
    model_name    varchar(255)                        null comment '数据模型名称',
    source_type   varchar(255)                        null comment '数据源类型:sql,table',
    sql_view      text                                null comment '逻辑sql',
    description   text                                null comment '模型备注或描述',
    status        int       default 1                 not null comment '数据是否有效：0-无效，1-有效',
    deleted       int       default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(255)                        null comment '创建人编号',
    create_name   varchar(255)                        null comment '创建人名称',
    create_time   varchar(255)                        null comment '创建时间',
    modify_no     varchar(255)                        null comment '修改人编号',
    modify_name   varchar(255)                        null comment '修改人名称',
    modify_time   varchar(255)                        null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
)
    comment '数据模型表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_data_model (enterprise_no);

create table yyigou_ddc_dmp.dmp_data_model_field
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(255)                        null comment '租户编号',
    model_no      varchar(255)                        null comment '所属数据模型ID',
    field_code    varchar(255)                        null comment '字段编码',
    field_name    varchar(255)                        null comment '字段名称',
    data_type     varchar(50)                         null comment '字段数据类型',
    date_format   varchar(50)                         null comment '日期格式',
    length        bigint                              null comment '字段长度',
    num_precision int                                 null comment '数值精度',
    num_scale     int                                 null comment '数值小数位',
    nullable      tinyint(1)                          null comment '是否可空',
    default_value varchar(255)                        null comment '默认值',
    field_order   int                                 null comment '字段排序',
    field_key     varchar(255)                        null comment '字段键',
    description   text                                null comment '字段描述',
    status        int       default 1                 not null comment '数据是否有效：0-无效，1-有效',
    deleted       int       default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(255)                        null comment '创建人编号',
    create_name   varchar(255)                        null comment '创建人名称',
    create_time   varchar(50)                         null comment '创建时间',
    modify_no     varchar(255)                        null comment '修改人编号',
    modify_name   varchar(255)                        null comment '修改人名称',
    modify_time   varchar(50)                         null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
);

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_data_model_field (enterprise_no);

create table yyigou_ddc_dmp.dmp_data_model_field_ref
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no     varchar(64)                         not null comment '租户编号',
    data_model_ref_no varchar(64)                         not null comment '数据模型关系唯一标识',
    source_field_code varchar(64)                         not null comment '源数据模型字段',
    target_field_code varchar(64)                         not null comment '目标数据模型字段',
    deleted           tinyint   default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no         varchar(64)                         null comment '创建人编号',
    create_name       varchar(128)                        null comment '创建人名称',
    create_time       varchar(32)                         null comment '创建时间',
    modify_no         varchar(64)                         null comment '修改人编号',
    modify_name       varchar(128)                        null comment '修改人名称',
    modify_time       varchar(32)                         null comment '修改时间',
    op_timestamp      timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
)
    comment '数据模型字段关联关系表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_data_model_field_ref (enterprise_no);

create table yyigou_ddc_dmp.dmp_data_model_ref
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no     varchar(64)                         not null comment '租户编号',
    data_model_ref_no varchar(64)                         not null comment '逻辑主键',
    source_model_no   varchar(64)                         not null comment '源数据模型',
    target_model_no   varchar(64)                         not null comment '目标数据模型',
    rel_type          varchar(10)                         not null comment '关联关系：1-1, 1-n, n-1, n-n',
    deleted           tinyint   default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no         varchar(64)                         null comment '创建人编号',
    create_name       varchar(128)                        null comment '创建人名称',
    create_time       varchar(32)                         null comment '创建时间',
    modify_no         varchar(64)                         null comment '修改人编号',
    modify_name       varchar(128)                        null comment '修改人名称',
    modify_time       varchar(32)                         null comment '修改时间',
    op_timestamp      timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
)
    comment '数据模型关联关系表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_data_model_ref (enterprise_no);

create table yyigou_ddc_dmp.dmp_dataset
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(64)                         not null comment '租户编号',
    dataset_no    varchar(64)                         not null comment '数据集唯一标识',
    dataset_name  varchar(255)                        not null comment '数据集名称',
    description   text                                null comment '数据集描述',
    status        tinyint   default 1                 not null comment '数据是否有效：0-无效，1-有效',
    deleted       tinyint   default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(64)                         null comment '创建人编号',
    create_name   varchar(128)                        null comment '创建人名称',
    create_time   varchar(32)                         null comment '创建时间',
    modify_no     varchar(64)                         null comment '修改人编号',
    modify_name   varchar(128)                        null comment '修改人名称',
    modify_time   varchar(32)                         null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
)
    comment '数据集信息表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dataset (enterprise_no);

create table yyigou_ddc_dmp.dmp_dataset_model_ref
(
    id                   bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no        varchar(64)                         not null comment '租户编号',
    dataset_model_ref_no varchar(64)                         not null comment '数据集和模型关系编号',
    dataset_no           varchar(64)                         not null comment '所属数据集ID',
    model_no             varchar(64)                         not null comment '字段来源数据模型',
    deleted              tinyint   default 0                 not null comment '是否删除：0-未删除，1-已删除',
    create_no            varchar(64)                         null comment '创建人编号',
    create_name          varchar(128)                        null comment '创建人名称',
    create_time          varchar(32)                         null comment '创建时间',
    modify_no            varchar(64)                         null comment '修改人编号',
    modify_name          varchar(128)                        null comment '修改人名称',
    modify_time          varchar(32)                         null comment '修改时间',
    op_timestamp         timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
)
    comment '数据集与模型关联关系表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dataset_model_ref (enterprise_no);

