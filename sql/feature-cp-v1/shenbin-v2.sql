-- 模型
create table yyigou_ddc_dmp.dmp_data_model
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(255)        not null comment '租户编号',
    model_no      varchar(255)        not null comment '模型唯一标识，UUID',
    model_name    varchar(255)        not null comment '数据模型名称',
    source_type   varchar(255)        not null comment '数据源类型:doris,excel',
    source_config text null comment '数据源专有配置',
    schema_name   varchar(255)        not null comment 'schema名称',
    table_name    varchar(255)        not null comment '表名',
    description   text null comment '模型备注或描述',
    status        int       default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted       int       default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(255) null comment '创建人编号',
    create_name   varchar(255) null comment '创建人名称',
    create_time   varchar(255) null comment '创建时间',
    modify_no     varchar(255) null comment '修改人编号',
    modify_name   varchar(255) null comment '修改人名称',
    modify_time   varchar(255) null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '最后操作时间'
) comment '数据模型表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_data_model (enterprise_no);


-- 字段
create table yyigou_ddc_dmp.dmp_data_model_field
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(255)        not null comment '租户编号',
    model_no      varchar(255)        not null comment '所属数据模型编号',
    field_code    varchar(255)        not null comment '字段编码',
    field_name    varchar(255)        not null comment '字段名称',
    data_type     varchar(50)         not null comment '字段数据类型',
    date_format   varchar(50) null comment '日期格式',
    length        bigint null comment '字段长度',
    num_precision int null comment '数值精度',
    num_scale     int null comment '数值小数位',
    nullable      tinyint(1)                          null comment '是否可空',
    default_value varchar(255) null comment '默认值',
    field_order   int null comment '字段排序',
    description   text null comment '字段描述',
    status        int       default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted       int       default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(255) null comment '创建人编号',
    create_name   varchar(255) null comment '创建人名称',
    create_time   varchar(50) null comment '创建时间',
    modify_no     varchar(255) null comment '修改人编号',
    modify_name   varchar(255) null comment '修改人名称',
    modify_time   varchar(50) null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
);

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_data_model_field (enterprise_no);


-- 维度
create table yyigou_ddc_dmp.dmp_dimension
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no  varchar(64)         not null comment '租户编号',
    dimension_code varchar(100)        not null comment '维度编码',
    dimension_name varchar(200)        not null comment '维度名称',
    description    varchar(500) null comment '维度描述',
    status         tinyint   default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted        tinyint   default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no      varchar(64) null comment '创建人编号',
    create_name    varchar(128) null comment '创建人名称',
    create_time    varchar(19) null comment '创建时间',
    modify_no      varchar(64) null comment '修改人编号',
    modify_name    varchar(128) null comment '修改人名称',
    modify_time    varchar(19) null comment '修改时间',
    op_timestamp   timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '最后操作时间'
);

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dimension (enterprise_no);


-- 维度字段
create table yyigou_ddc_dmp.dmp_dimension_field
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no  varchar(64)         not null comment '租户编号',
    dimension_code varchar(100)        not null comment '维度编码',
    model_no       varchar(100)        not null comment '模型编号',
    field_code     varchar(100)        not null comment '模型字段',
    field_type     varchar(100)        not null comment '字段类型：维度键，维度名称，维度属性',
    description    varchar(500) null comment '维度描述',
    status         tinyint   default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted        tinyint   default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no      varchar(64) null comment '创建人编号',
    create_name    varchar(128) null comment '创建人名称',
    create_time    varchar(19) null comment '创建时间',
    modify_no      varchar(64) null comment '修改人编号',
    modify_name    varchar(128) null comment '修改人名称',
    modify_time    varchar(19) null comment '修改时间',
    op_timestamp   timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '最后操作时间'
);

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dimension_field (enterprise_no);


-- 数据集
create table yyigou_ddc_dmp.dmp_dataset
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(64)         not null comment '租户编号',
    dataset_no    varchar(64)         not null comment '数据集唯一标识',
    dataset_name  varchar(255)        not null comment '数据集名称',
    main_model_no varchar(64)         not null comment '主数据模型',
    description   text null comment '数据集描述',
    status        tinyint   default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted       tinyint   default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(64) null comment '创建人编号',
    create_name   varchar(128) null comment '创建人名称',
    create_time   varchar(32) null comment '创建时间',
    modify_no     varchar(64) null comment '修改人编号',
    modify_name   varchar(128) null comment '修改人名称',
    modify_time   varchar(32) null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
) comment '数据集信息表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dataset (enterprise_no);

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dataset_model_ref (enterprise_no);


-- 数据集模型关系
create table yyigou_ddc_dmp.dmp_dataset_model_ref
(
    id                   bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no        varchar(64)         not null comment '租户编号',
    dataset_model_ref_no varchar(64)         not null comment '逻辑主键',
    dataset_no           varchar(64)         not null comment '数据集编号',
    source_model_no      varchar(64)         not null comment '源数据模型',
    target_model_no      varchar(64)         not null comment '目标数据模型',
    rel_type             varchar(10)         not null comment '关联关系：innerjoin, leftjoin, rightjoin',
    join_condition       text                not null comment '关联条件',
    deleted              tinyint   default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no            varchar(64) null comment '创建人编号',
    create_name          varchar(128) null comment '创建人名称',
    create_time          varchar(32) null comment '创建时间',
    modify_no            varchar(64) null comment '修改人编号',
    modify_name          varchar(128) null comment '修改人名称',
    modify_time          varchar(32) null comment '修改时间',
    op_timestamp         timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
) comment '数据集模型关系表';

-- 数据集模型字段
create table yyigou_ddc_dmp.dmp_dataset_model_field_ref
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(64)         not null comment '租户编号',
    dataset_no    varchar(64)         not null comment '数据集编号',
    model_no      varchar(64)         not null comment '数据模型',
    field_code    varchar(255)        not null comment '字段编码',
    field_name    varchar(255)        not null comment '字段名称',
    deleted       tinyint   default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(64) null comment '创建人编号',
    create_name   varchar(128) null comment '创建人名称',
    create_time   varchar(32) null comment '创建时间',
    modify_no     varchar(64) null comment '修改人编号',
    modify_name   varchar(128) null comment '修改人名称',
    modify_time   varchar(32) null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
) comment '数据集模型字段表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dataset_model_field_ref (enterprise_no);


-- 数据集指标
create table yyigou_ddc_dmp.dmp_dataset_metric
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no  varchar(255)        not null comment '租户编号',
    metric_code    varchar(255)        not null comment '指标编码',
    metric_name    varchar(255)        not null comment '指标名称',
    dataset_no     varchar(255)        not null comment '所属数据集编号',
    model_no       varchar(255)        not null comment '模型编号',
    field_code     varchar(255)        not null comment '字段编码',
    agg_type       varchar(255) null comment '聚合方式',
    unit           varchar(255) null comment '单位',
    num_precision  varchar(255) null comment '精度',
    display_format varchar(255) null comment '展示格式',
    description    text null comment '指标描述',
    status         int       default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted        int       default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no      varchar(255) null comment '创建人编号',
    create_name    varchar(255) null comment '创建人名称',
    create_time    varchar(50) null comment '创建时间',
    modify_no      varchar(255) null comment '修改人编号',
    modify_name    varchar(255) null comment '修改人名称',
    modify_time    varchar(50) null comment '修改时间',
    op_timestamp   timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
);

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dataset_metric (enterprise_no);


-- 数据集维度
create table yyigou_ddc_dmp.dmp_dataset_dimension
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no      varchar(255)        not null comment '租户编号',
    dataset_no         varchar(255)        not null comment '所属数据集编号',
    model_no           varchar(255)        not null comment '模型编号',
    field_code         varchar(255)        not null comment '字段编码',
    dimension_model_no varchar(255)        not null comment '维度模型编号',
    description        text null comment '维度描述',
    status             int       default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted            int       default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no          varchar(255) null comment '创建人编号',
    create_name        varchar(255) null comment '创建人名称',
    create_time        varchar(50) null comment '创建时间',
    modify_no          varchar(255) null comment '修改人编号',
    modify_name        varchar(255) null comment '修改人名称',
    modify_time        varchar(50) null comment '修改时间',
    op_timestamp       timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
);

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_dataset_dimension (enterprise_no);


-- 分析主题
create table yyigou_ddc_dmp.dmp_analysis_subject
(
    id            bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no varchar(64)         not null comment '租户编号',
    subject_no    varchar(64)         not null comment '分析主题唯一标识',
    subject_name  varchar(255)        not null comment '分析主题名称',
    description   text null comment '主题描述',
    status        tinyint   default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted       tinyint   default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no     varchar(64) null comment '创建人编号',
    create_name   varchar(128) null comment '创建人名称',
    create_time   varchar(32) null comment '创建时间',
    modify_no     varchar(64) null comment '修改人编号',
    modify_name   varchar(128) null comment '修改人名称',
    modify_time   varchar(32) null comment '修改时间',
    op_timestamp  timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '最后操作时间'
) comment '分析主题信息表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_analysis_subject (enterprise_no);


-- 分析主题与数据集关联关系
create table yyigou_ddc_dmp.dmp_analysis_subject_dataset_ref
(
    id                     bigint auto_increment comment '主键ID'
        primary key,
    enterprise_no          varchar(64)         not null comment '租户编号',
    subject_dataset_ref_no varchar(64)         not null comment '分析主题与数据集关系编号',
    subject_no             varchar(64)         not null comment '分析主题编号',
    dataset_no             varchar(64)         not null comment '数据集编号',
    status                 tinyint   default 1 not null comment '数据是否有效：0-无效，1-有效',
    deleted                tinyint   default 0 not null comment '是否删除：0-未删除，1-已删除',
    create_no              varchar(64) null comment '创建人编号',
    create_name            varchar(128) null comment '创建人名称',
    create_time            varchar(32) null comment '创建时间',
    modify_no              varchar(64) null comment '修改人编号',
    modify_name            varchar(128) null comment '修改人名称',
    modify_time            varchar(32) null comment '修改时间',
    op_timestamp           timestamp default CURRENT_TIMESTAMP null comment '最后操作时间'
) comment '分析主题与数据集关联关系表';

create index idx_enterprise_no
    on yyigou_ddc_dmp.dmp_analysis_subject_dataset_ref (enterprise_no);

