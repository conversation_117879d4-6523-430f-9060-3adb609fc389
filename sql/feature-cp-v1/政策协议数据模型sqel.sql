WITH
-- 第一层：基础数据预过滤和准备
-- 1. 产品维度数据（预处理所有产品信息）
    goods_info AS (SELECT g.enterprise_no,
                          g.sku_no,
                          g.sku_code,
                          g.goods_name,
                          g.register_code,
                          g.factory_no,
                          g.brand_no,
                          g.category_no,
                          g.common_name,
                          g.instrument_model,
                          g.spec,
                          g.is_virtual,
                          g.virtual_type,
                          c.category_name,
                          b.brand_name,
                          f.company_name AS factory_name
                   FROM ${dorisEnv}mysql_rds.yyigou_dsrp.bdc_goods g
                            INNER JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.bdc_brand b
                                       ON g.enterprise_no = b.enterprise_no AND g.brand_no = b.brand_no AND
                                          b.deleted = 0
                            INNER JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.bdc_company f
                                       ON g.enterprise_no = f.enterprise_no AND g.factory_no = f.company_no AND
                                          f.deleted = 0
                            INNER JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.bdc_goods_category c
                                       ON g.enterprise_no = c.enterprise_no AND g.category_no = c.category_no AND
                                          c.deleted = 0
                   WHERE g.enterprise_no = '${#enterprise_no}'
                     AND g.deleted = 0
                     AND g.control_status = 1
-- 产品过滤条件
    ${#if(#len(sku_no)==0,"","AND g.sku_no IN ('"+ sku_no +"')")}
    ${#if(#len(sku_code)==0,"","AND g.sku_code IN ('"+ sku_code +"')")}
    ${#if(#len(factory_no)==0,"","AND g.factory_no IN ('"+ factory_no +"')")}
    ${#if(#len(factory_name)==0,"","AND f.company_name LIKE '%"+ factory_name +"%'")}
    ${#if(#len(register_code)==0,"","AND g.register_code IN ('"+ register_code +"')")}
    ${#if(#len(goods_name)==0,"","AND g.goods_name LIKE '%"+ goods_name +"%'")}
    ${#if(#len(brand_no)==0,"","AND b.brand_no IN ('"+ brand_no +"')")}
    ${#if(#len(brand_name)==0,"","AND b.brand_name LIKE '%"+ brand_name +"%'")}
    ${#if(#len(company_name)==0,"","AND f.company_name LIKE '%"+ company_name +"%'")}
    ${#if(#len(category_name)==0,"","AND c.category_name LIKE '%"+ category_name +"%'")}
    ${#if(#len(common_name)==0,"","AND g.common_name LIKE '%"+ common_name +"%'")}
    )
   ,

-- 2. 供应商维度数据（预处理所有供应商信息）
    supplier_info AS (
SELECT
    s.enterprise_no, s.supplier_code, s.supplier_name, s.supplier_no, s.unified_social_code, s.supplier_category_no, s.cooperation_mode, sc.category_name, sc.category_code, ucd.doc_item_name AS cooperation_mode_name
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.bdc_supplier s
    LEFT JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.bdc_supplier_category sc
ON s.supplier_category_no = sc.no AND sc.deleted = 0
    LEFT JOIN ${dorisEnv}mysql_rds.yyigou_ddc.uim_custom_doc_item ucd
    ON s.cooperation_mode = ucd.doc_item_code AND ucd.custom_doc_code = 'dsrp_supplier_cooperation_mode'
WHERE s.enterprise_no = '${#enterprise_no}'
  AND s.deleted = 0
-- 供应商过滤条件
    ${#if(#len(supply_category_code)==0,"","AND sc.category_code IN ('"+ supply_category_code +"')")} ${#if(#len(cooperation_mode_name)==0,"","AND ucd.doc_item_name LIKE '%"+ cooperation_mode_name +"%'")} ${#if(#len(supply_category_name)==0,"","AND sc.category_name LIKE '%"+ supply_category_name +"%'")} ${#if(#len(supply_supplier_name)==0,"","AND s.supplier_name LIKE '%"+ supply_supplier_name +"%'")}
    )
    ,

-- 3. 组织维度数据（预处理所有组织信息）
    organization_info AS (
SELECT
    o.enterprise_no, o.org_no, o.org_name
FROM ${dorisEnv}mysql_rds.yyigou_ddc.uim_organization o
WHERE o.enterprise_no = '${#enterprise_no}'
    )
    ,

-- 4. 合同类型维度数据（预处理协议类型信息）
    contract_type_info AS (
SELECT
    enterprise_no, ct_type_code, ct_type_name
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_contract_type
WHERE enterprise_no = '${#enterprise_no}'
  AND deleted = 0
    )
    ,

-- 5. 采购模式维度数据（预处理采购模式信息）
    purchase_model_info AS (
SELECT
    enterprise_no, model_code, model_name
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_purchase_model
WHERE enterprise_no = '${#enterprise_no}'
  AND deleted = 0
    )
    ,

-- 6. 条款维度数据（预处理条款参数信息）
    common_params AS (
SELECT
    enterprise_no, param_code, param_name, sort
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.bdc_common_param
WHERE enterprise_no = '${#enterprise_no}'
  AND status = 1
  AND deleted = 0
    ),

-- 7. 违约程度字典数据
    breach_level_info AS (
SELECT
    enterprise_no, doc_item_code, doc_item_name
FROM ${dorisEnv}mysql_rds.yyigou_ddc.uim_custom_doc_item
WHERE enterprise_no = '${#enterprise_no}'
  AND custom_doc_code = 'breach_level'
  AND deleted=0
    )
    ,

-- 8. 合作方式字典数据
    cooperation_method_info AS (
SELECT
    enterprise_no, doc_item_code, doc_item_name
FROM ${dorisEnv}mysql_rds.yyigou_ddc.uim_custom_doc_item
WHERE enterprise_no = '${#enterprise_no}'
  AND custom_doc_code = 'cooperation_method'
    )
    ,

-- 第二层：协议查询（类似Java中的主表查询）
-- 9. 采购政策协议（先查询满足条件的协议）
    purchase_policy_contracts AS (
SELECT
    'purchase' AS src, cp.enterprise_no, cp.contract_no, cp.contract_name, cp.sign_subject_no, cp.pur_org_no, cp.trans_type_no, cp.ct_category_code, cp.pur_model_code, cp.supplier_code, cp.ct_status, cp.sign_time, cp.plan_effective_time, cp.plan_expire_time, cp.is_long, cp.audit_status, cp.create_time, cp.modify_time, cp.principal_no, cp.principal_name,
    -- 明细信息
    cpi.item_unique_id, cpi.policy_item_unique_id, cpi.sku_code, cpi.group_no, cpi.cooperation_method_code, cpi.biz_attr, cpi.tax_rate, cpi.price, cpi.num, cpi.amount, cpi.is_policy, cpi.is_compose_main, cpi.in_valid_status, cpi.policy_principal_no, cpi.policy_principal_name
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_purchase cp
    INNER JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.ct_purchase_item cpi
ON cp.contract_no = cpi.contract_no AND cp.enterprise_no = cpi.enterprise_no
WHERE cp.enterprise_no = '${#enterprise_no}'
  AND cp.deleted = 0
  AND cp.audit_status = 1
  AND cp.ct_category_code = 'CGZCXY'
  AND cpi.deleted = 0
  AND cpi.status = 1
-- 协议过滤条件（在主查询阶段就过滤）
    ${#if(#len(contract_no)==0,"","AND cp.contract_no LIKE '%"+ contract_no +"%'")} ${#if(#len(contract_name)==0,"","AND cp.contract_name LIKE '%"+ contract_name +"%'")} ${#if(#len(sign_subject_no)==0,"","AND cp.sign_subject_no IN ('"+ sign_subject_no +"')")} ${#if(#len(pur_org_no)==0,"","AND cp.pur_org_no IN ('"+ pur_org_no +"')")} ${#if(#len(supplier_code)==0,"","AND cp.supplier_code IN ('"+ supplier_code +"')")} ${#if(#len(trans_type_no)==0,"","AND cp.trans_type_no IN ('"+ trans_type_no +"')")} ${#if(#len(pur_model_code)==0,"","AND cp.pur_model_code IN ('"+ pur_model_code +"')")} ${#if(#len(cooperation_method_code)==0,"","AND cpi.cooperation_method_code IN ('"+ cooperation_method_code +"')")}
-- 时间过滤条件
    ${#if(#len(starttime)==0,"","AND cp.plan_effective_time >= '"+ starttime +" 00:00:00'")} ${#if(#len(endtime)==0,"","AND cp.plan_effective_time <= '"+ endtime +" 23:59:59'")} ${#if(#len(start_sign_date)==0,"","AND cp.sign_time >= '"+ start_sign_date +" 00:00:00'")} ${#if(#len(end_sign_date)==0,"","AND cp.sign_time <= '"+ end_sign_date +" 23:59:59'")}
-- 价格过滤条件
    ${#if(#len(price_range_min)==0,"","AND cpi.price >= "+ price_range_min)} ${#if(#len(price_range_max)==0,"","AND cpi.price <= "+ price_range_max)} ${#if(#len(amount_range_min)==0,"","AND cpi.amount >= "+ amount_range_min)} ${#if(#len(amount_range_max)==0,"","AND cpi.amount <= "+ amount_range_max)}
-- 状态过滤条件
    ${#if(#len(ct_status)==0,"","AND cp.ct_status IN ('"+ ct_status +"')")} ${#if(#len(in_valid_status)==0,"","AND cpi.in_valid_status IN ('"+ in_valid_status +"')")}
    )
    ,

-- 10. 内部政策协议（先查询满足条件的协议）
    associate_policy_contracts AS (
SELECT
    'associate' AS src, cp.enterprise_no, cp.contract_no, cp.contract_name, cp.sign_subject_no, cp.supplier_org_no AS pur_org_no, cp.trans_type_no, cp.ct_category_code, cp.pur_model_code, cp.supplier_org_code AS supplier_code, cp.ct_status, cp.sign_time, cp.plan_effective_time, cp.plan_expire_time, cp.is_long, cp.audit_status, cp.create_time, cp.modify_time, cp.principal_no, cp.principal_name,
    -- 明细信息
    cpi.item_unique_id, cpi.policy_item_unique_id, cpi.sku_code, cpi.group_no, cpi.cooperation_method_code, cpi.biz_attr, cpi.tax_rate, cpi.price, cpi.num, cpi.amount, cpi.is_policy, cpi.is_compose_main, cpi.in_valid_status, cpi.policy_principal_no, cpi.policy_principal_name
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_associate_agreement cp
    INNER JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.ct_associate_item cpi
ON cp.contract_no = cpi.contract_no AND cp.enterprise_no = cpi.enterprise_no
WHERE cp.enterprise_no = '${#enterprise_no}'
  AND cp.deleted = 0
  AND cp.audit_status = 1
  AND cp.ct_category_code = 'CGZCXY'
  AND cpi.deleted = 0
  AND cpi.status = 1
-- 协议过滤条件（字段映射调整）
    ${#if(#len(contract_no)==0,"","AND cp.contract_no LIKE '%"+ contract_no +"%'")} ${#if(#len(contract_name)==0,"","AND cp.contract_name LIKE '%"+ contract_name +"%'")} ${#if(#len(sign_subject_no)==0,"","AND cp.sign_subject_no IN ('"+ sign_subject_no +"')")} ${#if(#len(pur_org_no)==0,"","AND cp.supplier_org_no IN ('"+ pur_org_no +"')")} ${#if(#len(supplier_code)==0,"","AND cp.supplier_org_code IN ('"+ supplier_code +"')")} ${#if(#len(trans_type_no)==0,"","AND cp.trans_type_no IN ('"+ trans_type_no +"')")} ${#if(#len(pur_model_code)==0,"","AND cp.pur_model_code IN ('"+ pur_model_code +"')")} ${#if(#len(cooperation_method_code)==0,"","AND cpi.cooperation_method_code IN ('"+ cooperation_method_code +"')")}
-- 时间过滤条件
    ${#if(#len(starttime)==0,"","AND cp.plan_effective_time >= '"+ starttime +" 00:00:00'")} ${#if(#len(endtime)==0,"","AND cp.plan_effective_time <= '"+ endtime +" 23:59:59'")} ${#if(#len(start_sign_date)==0,"","AND cp.sign_time >= '"+ start_sign_date +" 00:00:00'")} ${#if(#len(end_sign_date)==0,"","AND cp.sign_time <= '"+ end_sign_date +" 23:59:59'")}
-- 价格过滤条件
    ${#if(#len(price_range_min)==0,"","AND cpi.price >= "+ price_range_min)} ${#if(#len(price_range_max)==0,"","AND cpi.price <= "+ price_range_max)} ${#if(#len(amount_range_min)==0,"","AND cpi.amount >= "+ amount_range_min)} ${#if(#len(amount_range_max)==0,"","AND cpi.amount <= "+ amount_range_max)}
-- 状态过滤条件
    ${#if(#len(ct_status)==0,"","AND cp.ct_status IN ('"+ ct_status +"')")} ${#if(#len(in_valid_status)==0,"","AND cpi.in_valid_status IN ('"+ in_valid_status +"')")}
    )
    ,

-- 11. 合并所有协议主数据
    all_policy_contracts AS (
SELECT *
FROM purchase_policy_contracts
UNION ALL
SELECT *
FROM associate_policy_contracts
    ),

-- 第三层：扩展信息查询（类似Java中的关联查询）
-- 12. 条款信息（基于已查询的协议）
    contract_clauses_info AS (
SELECT
    cc.enterprise_no, cc.contract_no, cc.item_unique_id, cc.parameter_no, cc.parameter_value, cd.param_name, cd.sort, 'purchase' AS src
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_purchase_clauses cc
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_policy_contracts
    WHERE src = 'purchase'
    ) acm
ON cc.enterprise_no = acm.enterprise_no AND cc.contract_no = acm.contract_no
    LEFT JOIN common_params cd
    ON cc.enterprise_no = cd.enterprise_no AND cc.parameter_no = cd.param_code
WHERE cc.status = 1
  AND cc.deleted = 0
  AND (cc.item_unique_id IS NOT NULL
  AND cc.item_unique_id <> '')

UNION ALL

SELECT
    ac.enterprise_no, ac.contract_no, ac.item_unique_id, ac.parameter_no, ac.parameter_value, cd.param_name, cd.sort, 'associate' AS src
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_associate_clauses ac
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_policy_contracts
    WHERE src = 'associate'
    ) acm
ON ac.enterprise_no = acm.enterprise_no AND ac.contract_no = acm.contract_no
    LEFT JOIN common_params cd
    ON ac.enterprise_no = cd.enterprise_no AND ac.parameter_no = cd.param_code
WHERE ac.status = 1
  AND ac.deleted = 0
  AND (ac.item_unique_id IS NOT NULL
  AND ac.item_unique_id <> '')
    ),

-- 13. 政策指标信息（基于已查询的协议）
    policy_indicators_info AS (
SELECT
    ppi.enterprise_no, ppi.contract_no, ppi.item_unique_id, ppi.task_amount, ppi.indicator_people, ppi.apportion_period, CASE ppi.apportion_method
    WHEN 1 THEN '受益期分摊'
    WHEN 2 THEN '年度均摊'
    END AS apportion_method_name, ppi.annual_growth_rate, CASE ppi.assess_method
    WHEN 1 THEN '累计'
    WHEN 2 THEN '年度'
    END AS assess_method_name, ppi.start_name, ppi.delay_days, ppi.delay_months, ppi.fixed_days, bl.doc_item_name AS breach_level_name, 'purchase' AS src
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_purchase_policy_indicator ppi
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_policy_contracts
    WHERE src = 'purchase'
    ) acm
ON ppi.enterprise_no = acm.enterprise_no AND ppi.contract_no = acm.contract_no
    LEFT JOIN breach_level_info bl
    ON ppi.enterprise_no = bl.enterprise_no AND ppi.breach_level_code = bl.doc_item_code
WHERE ppi.deleted = 0 AND ppi.status = 1

UNION ALL

SELECT
    appi.enterprise_no, appi.contract_no, appi.item_unique_id, appi.task_amount, appi.indicator_people, appi.apportion_period, CASE appi.apportion_method
    WHEN 1 THEN '受益期分摊'
    WHEN 2 THEN '年度均摊'
    END AS apportion_method_name, appi.annual_growth_rate, CASE appi.assess_method
    WHEN 1 THEN '累计'
    WHEN 2 THEN '年度'
    END AS assess_method_name, appi.start_name, appi.delay_days, appi.delay_months, appi.fixed_days, bl.doc_item_name AS breach_level_name, 'associate' AS src
FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_associate_policy_indicator appi
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_policy_contracts
    WHERE src = 'associate'
    ) acm
ON appi.enterprise_no = acm.enterprise_no AND appi.contract_no = acm.contract_no
    LEFT JOIN breach_level_info bl
    ON appi.enterprise_no = bl.enterprise_no AND appi.breach_level_code = bl.doc_item_code
WHERE appi.deleted = 0
  AND appi.status = 1
    )
    ,

-- 14. 适用组织信息（基于已查询的协议）
    applicable_orgs_info AS (
SELECT
    ao.enterprise_no, ao.contract_no, GROUP_CONCAT(DISTINCT od.org_name) AS applicable_org_names, COUNT (DISTINCT ao.pur_org_no) AS org_count
FROM (
    -- 采购协议适用组织
    SELECT
    po.enterprise_no, po.contract_no, COALESCE (pco.control_purchase_org_no, po.pur_org_no) AS pur_org_no
    FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_purchase_org po
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_policy_contracts
    WHERE src = 'purchase'
    ) acm ON po.enterprise_no = acm.enterprise_no AND po.contract_no = acm.contract_no
    LEFT JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.ctl_purchase_control_org pco
    ON po.pur_org_group_id = pco.org_group_id AND pco.deleted = 0
    WHERE po.status = 1 AND po.deleted = 0
    UNION ALL

    -- 内部协议适用组织
    SELECT
    ao.enterprise_no, ao.contract_no, COALESCE (pco.control_purchase_org_no, ao.pur_org_no) AS pur_org_no
    FROM ${dorisEnv}mysql_rds.yyigou_dsrp.ct_associate_org ao
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_policy_contracts
    WHERE src = 'associate'
    ) acm ON ao.enterprise_no = acm.enterprise_no AND ao.contract_no = acm.contract_no
    LEFT JOIN ${dorisEnv}mysql_rds.yyigou_dsrp.ctl_purchase_control_org pco
    ON ao.pur_org_group_id = pco.org_group_id AND pco.deleted = 0
    WHERE ao.status = 1 AND ao.deleted = 0
    ) ao
    LEFT JOIN organization_info od
ON ao.enterprise_no = od.enterprise_no AND ao.pur_org_no = od.org_no
WHERE 1 = 1 ${#if(#len(applicable_org_no)==0,"","AND ao.pur_org_no IN ('"+ applicable_org_no +"')")}
GROUP BY ao.enterprise_no, ao.contract_no
    ),

-- 15. 流水线政策明细信息（基于已查询的协议）
    pipeline_detail_info AS (
SELECT
    apc.enterprise_no, apc.contract_no, apc.policy_item_unique_id, apc.group_no, apc.sku_code AS detail_sku_code, apc.cooperation_method_code AS detail_cooperation_method_code, apc.biz_attr AS detail_biz_attr, apc.tax_rate AS detail_tax_rate, apc.price AS detail_price, apc.num AS detail_num, apc.amount AS detail_amount
FROM all_policy_contracts apc
WHERE apc.is_policy = 0 -- 非政策项，即明细项
    ),

-- 第四层：最终结果集构建（类似Java中的VO组装）
    dwd_final_policy_contract AS (
SELECT
    a.enterprise_no AS enterprise_no, a.contract_no AS contract_no, a.contract_name AS contract_name, a.supplier_code AS supplier_code, a.ct_category_code AS ct_category_code,
    -- 时间维度字段
    a.plan_effective_time AS plan_effective_time, CASE WHEN a.is_long = 1 THEN '9999-12-31' ELSE a.plan_expire_time END AS plan_expire_time, a.sign_time AS sign_time, a.create_time AS create_time, a.modify_time AS modify_time,
    -- 协议明细
    a.policy_item_unique_id AS policy_item_unique_id, a.group_no AS group_no,
    -- 政策信息（流水线政策）
    CASE WHEN a.is_policy = 1 THEN a.sku_code ELSE '' END AS policy_sku_code, CASE WHEN a.is_policy = 1 THEN b.common_name ELSE '' END AS policy_name, CASE
    WHEN b.is_virtual = 1 AND b.virtual_type = 3 THEN '是'
    ELSE '否'
    END AS is_pipeline,
    -- 政策明细信息
    a.sku_code AS detail_sku_code, CASE a.biz_attr
    WHEN 1 THEN '采购'
    WHEN 2 THEN '联动'
    WHEN 3 THEN '赠品'
    END AS biz_attr_name, a.tax_rate AS tax_rate, a.price AS price, a.num AS num, a.amount AS amount,
    -- 状态信息
    CASE
    WHEN a.ct_status = 0 THEN '待生效'
    WHEN a.ct_status = 1 THEN '生效中'
    WHEN a.ct_status = 2 THEN '终止'
    WHEN a.ct_status = 3 THEN '冻结'
    WHEN a.ct_status = 4 THEN '作废'
    WHEN a.ct_status = 5 THEN '已失效'
    ELSE '非生效'
    END AS contract_status_name, CASE
    WHEN a.ct_status = 1 AND a.in_valid_status = 0 THEN '待生效'
    WHEN a.ct_status = 1 AND a.in_valid_status = 1 THEN '生效中'
    ELSE '已失效'
    END AS policy_status_name, a.ct_status AS ct_status, a.in_valid_status AS in_valid_status, a.is_policy AS is_policy, a.is_compose_main AS is_compose_main,
    -- 关联信息填充
    -- 产品信息填充（政策明细产品）
    detail_b.goods_name AS detail_goods_name, detail_b.sku_no AS detail_sku_no, detail_b.spec AS detail_spec, detail_b.factory_no AS detail_factory_no, detail_b.register_code AS detail_register_code, detail_b.brand_name AS detail_brand_name, detail_b.factory_name AS detail_factory_name, detail_b.category_name AS detail_category_name, detail_b.common_name AS detail_common_name,detail_b.instrument_model AS detail_instrument_model,
    -- 供应商信息填充
    c.supplier_name AS supplier_name, c.category_name AS supplier_category_name, c.cooperation_mode_name AS supplier_cooperation_mode_name,
    -- 签约组织填充
    d.org_name AS sign_org_name,
    -- 管理组织填充
    e.org_name AS pur_org_name,
    -- 合同类型填充
    f.ct_type_name AS ct_type_name,
    -- 采购模式填充
    g.model_name AS model_name,
    -- 合作方式填充
    cm.doc_item_name AS cooperation_method_name,
    -- 填充适用组织
    h.applicable_org_names AS applicable_org_names,
    -- 填充条款信息
    cci.parameter_no AS parameter_no, cci.param_name AS param_name, cci.parameter_value AS parameter_value, cci.sort AS clause_sort,
    -- 填充政策指标信息
    pi.task_amount AS task_amount, pi.indicator_people AS indicator_people, pi.apportion_period AS apportion_period, pi.apportion_method_name AS apportion_method_name, pi.annual_growth_rate AS annual_growth_rate, pi.assess_method_name AS assess_method_name, pi.start_name AS start_name, pi.delay_days AS delay_days, pi.delay_months AS delay_months, pi.fixed_days AS fixed_days, pi.breach_level_name AS breach_level_name,
    -- 责任人信息
    a.principal_no AS principal_no, a.principal_name AS principal_name, a.policy_principal_no AS policy_principal_no, a.policy_principal_name AS policy_principal_name

FROM all_policy_contracts a
    -- 关联政策产品维度（流水线政策）
    LEFT JOIN goods_info b
ON a.enterprise_no = b.enterprise_no AND a.sku_code = b.sku_code
    -- 关联政策明细产品维度
    LEFT JOIN goods_info detail_b
    ON a.enterprise_no = detail_b.enterprise_no
    AND a.sku_code = detail_b.sku_code
    -- 关联流水线明细信息
    LEFT JOIN pipeline_detail_info pd
    ON a.enterprise_no = pd.enterprise_no
    AND a.contract_no = pd.contract_no
    AND a.policy_item_unique_id = pd.policy_item_unique_id
    AND a.group_no = pd.group_no
    -- 关联供应商维度
    LEFT JOIN supplier_info c
    ON a.enterprise_no = c.enterprise_no AND a.supplier_code = c.supplier_code
    -- 关联签约组织维度
    LEFT JOIN organization_info d
    ON a.enterprise_no = d.enterprise_no AND a.sign_subject_no = d.org_no
    -- 关联管理组织维度
    LEFT JOIN organization_info e
    ON a.enterprise_no = e.enterprise_no AND a.pur_org_no = e.org_no
    -- 关联合同类型维度
    LEFT JOIN contract_type_info f
    ON a.enterprise_no = f.enterprise_no AND a.trans_type_no = f.ct_type_code
    -- 关联采购模式维度
    LEFT JOIN purchase_model_info g
    ON a.enterprise_no = g.enterprise_no AND a.pur_model_code = g.model_code
    -- 关联合作方式维度
    LEFT JOIN cooperation_method_info cm
    ON a.enterprise_no = cm.enterprise_no AND a.cooperation_method_code = cm.doc_item_code
    -- 关联适用组织信息
    LEFT JOIN applicable_orgs_info h
    ON a.enterprise_no = h.enterprise_no AND a.contract_no = h.contract_no
    -- 关联条款信息
    LEFT JOIN contract_clauses_info cci
    ON a.enterprise_no = cci.enterprise_no
    AND a.contract_no = cci.contract_no
    AND a.policy_item_unique_id = cci.item_unique_id
    -- 关联政策指标信息
    LEFT JOIN policy_indicators_info pi
    ON a.enterprise_no = pi.enterprise_no
    AND a.contract_no = pi.contract_no
    AND a.policy_item_unique_id = pi.item_unique_id
    )

-- 最终查询明细级数据
SELECT *
FROM dwd_final_policy_contract
WHERE 1 = 1
      -- 最终过滤条件
    ${#if(#len(zc_sku_code)==0,"","AND policy_sku_code LIKE '%"+ zc_sku_code +"%'")} ${#if(#len(detail_sku_code)==0,"","AND detail_sku_code LIKE '%"+ detail_sku_code +"%'")}
ORDER BY
    enterprise_no,
    contract_no DESC,
    supplier_name,
    sign_org_name,
    group_no + 0,
    is_pipeline DESC,
    is_policy DESC,
    is_compose_main DESC,
    policy_sku_code,
    detail_sku_code,
    biz_attr_name,
    clause_sort;