
-- `yyigou_ddc_sgp`.`t_module` 表配置，
INSERT INTO `yyigou_ddc_sgp`.`t_module` (`id`, `moduleCode`, `moduleName`, `developer`, `deployDate`, `packageOrigin`, `discoveryUrl`, `targetUrl`, `apiNamePrefix`, `sortNumber`, `detail`, `moduleState`, `updateMode`, `returnCodeStart`, `returnCodeEnd`, `isMockData`, `isDeleted`, `creatorCode`, `createTime`, `lastUpdatorCode`, `lastUpdateTime`) VALUES (null, 'service-ddc-dmp', 'ddc dmp项目', '鲍伟伟', '2027-07-25', 'com.yyigou.ddc.dmp.web.api', '', '', 'ddc.dmp', 0, NULL, 0, 'A', NULL, NULL, 0, 0, '0', '2020-03-16 18:04:20', NULL, NULL);



CREATE TABLE yyigou_ddc_dmp.`dmp_report_template` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                       `template_no` varchar(32) DEFAULT NULL COMMENT '唯一编号',
                                       `enterprise_no` varchar(32) NOT NULL COMMENT '租户id',
                                       `source_template_no` varchar(32) DEFAULT NULL COMMENT '租户记录平台复制时的来源报表模板编号',
                                       `template_code` varchar(32) DEFAULT NULL COMMENT '报表模板编码，系统生成，升级版本时该字段不变',
                                       `template_name` varchar(100) DEFAULT NULL COMMENT '报表模板名称',
                                       `template_mode` tinyint(255) DEFAULT NULL COMMENT '报表模板来源模式。1-独立报表，2-比价方案',
                                       `dataset_no` varchar(32) DEFAULT NULL COMMENT '数据集编号',
                                       `plan_no` varchar(32) DEFAULT NULL COMMENT '比价方案编号',
                                       `row_fields` json DEFAULT NULL COMMENT '行配置',
                                       `column_fields` json DEFAULT NULL COMMENT '列配置',
                                       `value_fields` json DEFAULT NULL COMMENT '指标配置',
                                       `biz_config` json DEFAULT NULL COMMENT '报表业务配置',
                                       `filter_config` json DEFAULT NULL COMMENT '数据筛选条件',
                                       `config_json` json DEFAULT NULL COMMENT 'antv渲染配置信息',
                                       `description` varchar(255) DEFAULT NULL COMMENT '描述信息',
                                       `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
                                       `enable_status` tinyint(4) DEFAULT '1' COMMENT '启用状态 1-启用，2-禁用',
                                       `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志 0：未删除 1：已删除',
                                       `create_no` varchar(32) DEFAULT '' COMMENT '创建人编号',
                                       `create_name` varchar(100) DEFAULT '' COMMENT '创建人名称',
                                       `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
                                       `modify_no` varchar(32) DEFAULT '' COMMENT '操作人编号',
                                       `modify_name` varchar(100) DEFAULT '' COMMENT '操作人名称',
                                       `modify_time` varchar(19) DEFAULT '' COMMENT '操作时间',
                                       `version` int(11) DEFAULT NULL COMMENT '版本',
                                       `preset` tinyint(4) DEFAULT NULL COMMENT '是否平台预置 0-否，1-是',
                                       `op_timestamp` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='报表模板';


CREATE TABLE yyigou_ddc_dmp.`dmp_report_template_auth` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `enterprise_no` varchar(20) NOT NULL COMMENT '租户编号',
                                            `auth_to_user_no` varchar(32) DEFAULT NULL COMMENT '被授权人用户编号',
                                            `auth_to_user_name` varchar(100) DEFAULT NULL COMMENT '被授权人用户名称',
                                            `auth_org_no` varchar(20) DEFAULT NULL COMMENT '授权组织编号',
                                            `template_code` varchar(32) DEFAULT NULL COMMENT '报表模板编码',
                                            `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
                                            `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                            `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
                                            `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
                                            `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
                                            `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
                                            `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
                                            `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
                                            `op_timestamp` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_enterprise_template_code` (`enterprise_no`,`template_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='报表模板授权关系表';


CREATE TABLE yyigou_ddc_dmp.`dmp_report_template_ref` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `enterprise_no` varchar(32) DEFAULT NULL COMMENT '租户id',
                                           `employee_no` varchar(32) NOT NULL COMMENT '授权用户编号',
                                           `template_code` varchar(32) DEFAULT NULL COMMENT '配置id',
                                           `enable_status` tinyint(4) DEFAULT '1' COMMENT '启用状态 1-启用，2-禁用',
                                           `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
                                           `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志 0：未删除 1：已删除',
                                           `create_no` varchar(32) DEFAULT '' COMMENT '创建人编号',
                                           `create_name` varchar(100) DEFAULT '' COMMENT '创建人名称',
                                           `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
                                           `modify_no` varchar(32) DEFAULT '' COMMENT '操作人编号',
                                           `modify_name` varchar(100) DEFAULT '' COMMENT '操作人名称',
                                           `modify_time` varchar(19) DEFAULT '' COMMENT '操作时间',
                                           `op_timestamp` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表模板引用表';