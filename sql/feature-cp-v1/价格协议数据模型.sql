WITH
-- 第一层：基础数据预过滤和准备
-- 1. 产品维度数据（预处理所有产品信息）
    goods_info AS (
        SELECT
            g.enterprise_no,
            g.sku_code,
            g.goods_name,
            g.register_code,
            g.factory_no,
            g.brand_no,
            g.category_no,
            g.common_name,
            g.instrument_model,
            g.spec,
            g.is_virtual,
            g.virtual_type,
            c.category_name,
            b.brand_name,
            f.company_name AS factory_name
        FROM mysql_rds.yyigou_dsrp.bdc_goods g
                 INNER JOIN mysql_rds.yyigou_dsrp.bdc_brand b
                            ON g.enterprise_no = b.enterprise_no AND g.brand_no = b.brand_no AND b.deleted = 0
                 INNER JOIN mysql_rds.yyigou_dsrp.bdc_company f
                            ON g.enterprise_no = b.enterprise_no AND g.factory_no = f.company_no AND f.deleted = 0
                 INNER JOIN mysql_rds.yyigou_dsrp.bdc_goods_category c
                            ON g.enterprise_no = b.enterprise_no AND g.category_no = c.category_no AND c.deleted = 0
        WHERE g.enterprise_no = '${enterprise_no}' AND g.deleted = 0
-- 产品过滤条件
    ${if(len(sku_code)==0,"","AND g.sku_code IN ('"+ sku_code +"')")}
    ${if(len(brand_no)==0,"","AND g.brand_no in ('"+ brand_no +"')")}
    ),

-- 2. 供应商维度数据（预处理所有供应商信息）
    supplier_info AS (
SELECT
    s.enterprise_no,
    s.supplier_no,
    s.supplier_code,
    s.supplier_name
FROM mysql_rds.yyigou_dsrp.bdc_supplier s
WHERE s.enterprise_no = '${enterprise_no}' AND s.deleted = 0
    ${if(len(supplier_code)==0,"","AND s.supplier_code in ('"+ supplier_code +"')")}
    ),


-- 3. 组织维度数据（预处理所有组织信息）
    organization_info AS (
SELECT
    o.enterprise_no,
    o.org_no,
    o.org_code,
    o.org_name
FROM mysql_rds.yyigou_ddc.uim_organization o
WHERE o.enterprise_no = '${enterprise_no}'
    ),

-- 4. 合同类型维度数据（预处理协议类型信息）
    contract_type_info AS (
SELECT
    ct_type_code,
    ct_type_name
FROM mysql_rds.yyigou_dsrp.ct_contract_type
WHERE enterprise_no = '${enterprise_no}' and	deleted = 0
    ),

-- 5. 采购模式维度数据（预处理采购模式信息）
    purchase_model_info AS (
SELECT
    model_code,
    model_name
FROM mysql_rds.yyigou_dsrp.ct_purchase_model
WHERE enterprise_no = '${enterprise_no}' and	deleted = 0
    ),

-- 6. 条款维度数据（预处理条款参数信息）
    common_params AS (
SELECT
    enterprise_no,
    param_code,
    param_name
FROM mysql_rds.yyigou_dsrp.bdc_common_param
WHERE enterprise_no = '${enterprise_no}' AND status = 1 AND deleted = 0
    ),


-- 第二层：协议查询（类似Java中的主表查询）
-- 7. 采购协议（先查询满足条件的协议）
    purchase_contracts AS (
SELECT
    'purchase' AS src,
    cp.enterprise_no,
    cp.contract_no,
    cp.contract_name,
    cp.sign_subject_no,
    cp.pur_org_no,
    cp.trans_type_no,
    cp.ct_category_code,
    cp.pur_model_code,
    cp.supplier_code,
    cp.ct_status,
    cp.sign_time,
    cp.plan_effective_time,
    cp.plan_expire_time,
    cp.is_long,
    cp.audit_status,
    cp.create_time,
    cp.modify_time,
    -- 明细信息
    cpi.item_unique_id,
    cpi.sku_code,
    cpi.biz_attr,
    cpi.tax_rate,
    cpi.price,
    cpi.num,
    cpi.amount
FROM mysql_rds.yyigou_dsrp.ct_purchase cp
    INNER JOIN mysql_rds.yyigou_dsrp.ct_purchase_item cpi
ON cp.contract_no = cpi.contract_no AND cp.enterprise_no = cpi.enterprise_no
WHERE cp.enterprise_no = '${enterprise_no}'
  AND cp.deleted = 0 AND cp.audit_status = 1 AND cp.ct_category_code = 'CGJGXY'
  AND cpi.deleted = 0
-- 协议过滤条件（在主查询阶段就过滤）
    ${if(len(contract_no)==0,"","AND cp.contract_no LIKE '%"+ contract_no +"%'")}
    ${if(len(contract_name)==0,"","AND cp.contract_name LIKE '%"+ contract_name +"%'")}
    ${if(len(sign_subject_no)==0,"","AND cp.sign_subject_no IN ('"+ sign_subject_no +"')")}
    ${if(len(pur_org_no)==0,"","AND cp.pur_org_no IN ('"+ pur_org_no +"')")}
    ${if(len(supplier_code)==0,"","AND cp.supplier_code IN ('"+ supplier_code +"')")}
    ${if(len(trans_type_no)==0,"","AND cp.trans_type_no IN ('"+ trans_type_no +"')")}
    ${if(len(pur_model_code)==0,"","AND cp.pur_model_code IN ('"+ pur_model_code +"')")}
    ${if(len(cooperation_method_code)==0,"","AND cpi.cooperation_method_code IN ('"+ cooperation_method_code +"')")}
-- 时间过滤条件
    ${if(len(starttime)==0,"","AND cp.plan_effective_time >= '"+ starttime +" 00:00:00'")}
    ${if(len(endtime)==0,"","AND cp.plan_effective_time <= '"+ endtime +" 23:59:59'")}
    ${if(len(start_sign_date)==0,"","AND cp.sign_time >= '"+ start_sign_date +" 00:00:00'")}
    ${if(len(end_sign_date)==0,"","AND cp.sign_time <= '"+ end_sign_date +" 23:59:59'")}
-- 物料过滤条件
    ${if(len(sku_code)==0,"","AND cpi.sku_code IN ('"+ sku_code +"')")}
-- 价格过滤条件
    ${if(len(price_range_min)==0,"","AND cpi.price >= "+ price_range_min)}
    ${if(len(price_range_max)==0,"","AND cpi.price <= "+ price_range_max)}
    ${if(len(amount_range_min)==0,"","AND cpi.amount >= "+ amount_range_min)}
    ${if(len(amount_range_max)==0,"","AND cpi.amount <= "+ amount_range_max)}
-- 状态过滤条件
    ${if(len(ct_status)==0,"","AND cp.ct_status IN ('"+ ct_status +"')")}
    ),

-- 8. 内部协议（先查询满足条件的协议）
    associate_contracts AS (
SELECT
    'associate' AS src,
    cp.enterprise_no,
    cp.contract_no,
    cp.contract_name,
    cp.sign_subject_no,
    cp.supplier_org_no AS pur_org_no,
    cp.trans_type_no,
    cp.ct_category_code,
    cp.pur_model_code,
    cp.supplier_org_code AS supplier_code,
    cp.ct_status,
    cp.sign_time,
    cp.plan_effective_time,
    cp.plan_expire_time,
    cp.is_long,
    cp.audit_status,
    cp.create_time,
    cp.modify_time,
    -- 明细信息
    cpi.item_unique_id,
    cpi.sku_code,
    cpi.biz_attr,
    cpi.tax_rate,
    cpi.price,
    cpi.num,
    cpi.amount
FROM mysql_rds.yyigou_dsrp.ct_associate_agreement cp
    INNER JOIN mysql_rds.yyigou_dsrp.ct_associate_item cpi
ON cp.contract_no = cpi.contract_no AND cp.enterprise_no = cpi.enterprise_no
WHERE cp.enterprise_no = '${enterprise_no}'
  AND cp.deleted = 0 AND cp.audit_status = 1 AND cp.ct_category_code = 'GLJGXY'
  AND cpi.deleted = 0
-- 协议过滤条件（字段映射调整）
    ${if(len(contract_no)==0,"","AND cp.contract_no LIKE '%"+ contract_no +"%'")}
    ${if(len(contract_name)==0,"","AND cp.contract_name LIKE '%"+ contract_name +"%'")}
    ${if(len(sign_subject_no)==0,"","AND cp.sign_subject_no IN ('"+ sign_subject_no +"')")}
    ${if(len(pur_org_no)==0,"","AND cp.supplier_org_no IN ('"+ pur_org_no +"')")}
    ${if(len(supplier_code)==0,"","AND cp.supplier_org_code IN ('"+ supplier_code +"')")}
    ${if(len(trans_type_no)==0,"","AND cp.trans_type_no IN ('"+ trans_type_no +"')")}
    ${if(len(pur_model_code)==0,"","AND cp.pur_model_code IN ('"+ pur_model_code +"')")}
    ${if(len(cooperation_method_code)==0,"","AND cai.cooperation_method_code IN ('"+ cooperation_method_code +"')")}
-- 时间过滤条件
    ${if(len(starttime)==0,"","AND cp.plan_effective_time >= '"+ starttime +" 00:00:00'")}
    ${if(len(endtime)==0,"","AND cp.plan_effective_time <= '"+ endtime +" 23:59:59'")}
    ${if(len(start_sign_date)==0,"","AND cp.sign_time >= '"+ start_sign_date +" 00:00:00'")}
    ${if(len(end_sign_date)==0,"","AND cp.sign_time <= '"+ end_sign_date +" 23:59:59'")}
-- 物料过滤条件
    ${if(len(sku_code)==0,"","AND cpi.sku_code IN ('"+ sku_code +"')")}
-- 价格过滤条件
    ${if(len(price_range_min)==0,"","AND cpi.price >= "+ price_range_min)}
    ${if(len(price_range_max)==0,"","AND cpi.price <= "+ price_range_max)}
    ${if(len(amount_range_min)==0,"","AND cpi.amount >= "+ amount_range_min)}
    ${if(len(amount_range_max)==0,"","AND cpi.amount <= "+ amount_range_max)}
-- 状态过滤条件
    ${if(len(ct_status)==0,"","AND cp.ct_status IN ('"+ ct_status +"')")}
    ),

-- 9. 合并所有协议主数据
    all_contracts AS (
SELECT * FROM purchase_contracts
UNION ALL
SELECT * FROM associate_contracts
    ),


-- 第三层：扩展信息查询（类似Java中的关联查询）
-- 10. 条款信息（基于已查询的协议）
    contract_clauses_info AS (
SELECT
    cc.enterprise_no,
    cc.contract_no,
    cc.item_unique_id,
    cc.parameter_no,
    cc.parameter_value,
    cd.param_name,
    'purchase' AS src
FROM mysql_rds.yyigou_dsrp.ct_purchase_clauses cc
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'purchase'
    ) acm ON cc.enterprise_no = acm.enterprise_no AND cc.contract_no = acm.contract_no
    LEFT JOIN common_params cd
    ON cc.enterprise_no = cd.enterprise_no AND cc.parameter_no = cd.param_code
WHERE cc.status = 1 AND cc.deleted = 0

UNION ALL

SELECT
    ac.enterprise_no,
    ac.contract_no,
    ac.item_unique_id,
    ac.parameter_no,
    ac.parameter_value,
    cd.param_name,
    'associate' AS src
FROM mysql_rds.yyigou_dsrp.ct_associate_clauses ac
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'associate'
    ) acm ON ac.enterprise_no = acm.enterprise_no AND ac.contract_no = acm.contract_no
    LEFT JOIN common_params cd
    ON ac.enterprise_no = cd.enterprise_no AND ac.parameter_no = cd.param_code
WHERE ac.status = 1 AND ac.deleted = 0
    ),

-- 11. 适用组织信息（基于已查询的协议）
    applicable_orgs_info AS (
SELECT
    ao.enterprise_no,
    ao.contract_no,
    GROUP_CONCAT(DISTINCT od.org_name) AS applicable_org_names,
    COUNT(DISTINCT ao.pur_org_no) AS org_count
FROM (
    -- 采购协议适用组织
    SELECT
    po.enterprise_no, po.contract_no,
    COALESCE(pco.control_purchase_org_no, po.pur_org_no) AS pur_org_no
    FROM mysql_rds.yyigou_dsrp.ct_purchase_org po
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'purchase'
    ) acm ON po.enterprise_no = acm.enterprise_no AND po.contract_no = acm.contract_no
    LEFT JOIN mysql_rds.yyigou_dsrp.ctl_purchase_control_org pco
    ON po.pur_org_group_id = pco.org_group_id AND pco.deleted = 0
    WHERE po.status = 1 AND po.deleted = 0

    UNION ALL

    -- 内部协议适用组织
    SELECT
    ao.enterprise_no, ao.contract_no,
    COALESCE(pco.control_purchase_org_no, ao.pur_org_no) AS pur_org_no
    FROM mysql_rds.yyigou_dsrp.ct_associate_org ao
    INNER JOIN (
    SELECT DISTINCT enterprise_no, contract_no
    FROM all_contracts
    WHERE src = 'associate'
    ) acm ON ao.enterprise_no = acm.enterprise_no AND ao.contract_no = acm.contract_no
    LEFT JOIN mysql_rds.yyigou_dsrp.ctl_purchase_control_org pco
    ON ao.pur_org_group_id = pco.org_group_id AND pco.deleted = 0
    WHERE ao.status = 1 AND ao.deleted = 0
    ) ao
    LEFT JOIN organization_info od
ON ao.enterprise_no = od.enterprise_no AND ao.pur_org_no = od.org_no
WHERE 1 = 1
    ${if(len(pur_org_no)==0,"","AND ao.pur_org_no IN ('"+ pur_org_no +"')")}
GROUP BY ao.enterprise_no, ao.contract_no
    ),

-- 第四层：最终结果集构建（类似Java中的VO组装）
    dwd_final_price_contract AS (

SELECT
    a.enterprise_no AS enterprise_no,
    a.contract_no AS contract_no,
    a.contract_name AS contract_name,
    a.supplier_code AS supplier_code,
    a.ct_category_code AS ct_category_code,
    -- 时间维度字段 --
    a.plan_effective_time AS plan_effective_time,
    CASE WHEN a.is_long = 1 THEN '9999-12-31' ELSE a.plan_expire_time END AS plan_expire_time,
    a.sign_time AS sign_time,
    a.create_time AS create_time,
    a.modify_time AS modify_time,
    --  协议明细 ------
    a.sku_code AS sku_code,
    case a.biz_attr
    when 1 then '采购'
    when 2 then '联动'
    when 3 then '赠品'
    end AS biz_attr_name,
    a.tax_rate AS tax_rate,
    a.price AS price,
    a.num AS num,
    a.amount AS amount,
    -- 关联信息填充 --
    -- 产品信息填充 --
    b.goods_name AS goods_name,
    b.brand_no AS brand_no,
    b.brand_name AS brand_name,
    b.factory_name AS factory_name,
    b.category_no AS category_no,
    b.category_name AS category_name,
    b.common_name AS common_name,
    b.instrument_model AS instrument_model,
    b.spec AS spec,
    b.register_code AS register_code,
    -- 供应商信息填充 --
    c.supplier_no AS supplier_no,
--     c.supplier_code AS supplier_code,
    c.supplier_name AS supplier_name,
    -- 签约组织填充 --
    d.org_code AS sign_org_code,
    d.org_name AS sign_org_name,
    -- 管理组织填充 --
    e.org_code AS pur_org_code,
    e.org_name AS pur_org_name,
    -- 合同类型填充 --
    f.ct_type_name AS ct_type_name,
    -- 采购模式填充 --
    g.model_name AS model_name,
    -- 填充适用组织 --
    h.applicable_org_names AS applicable_org_names,
    -- 填充条款信息 --
    cci.parameter_no AS parameter_no,
    cci.param_name AS param_name,
    cci.parameter_value AS parameter_value

FROM all_contracts a
-- 关联产品维度（预处理的维度数据）
    INNER JOIN goods_info b
ON a.enterprise_no = b.enterprise_no AND a.sku_code = b.sku_code
-- 关联供应商维度（预处理的维度数据）
    INNER JOIN supplier_info c
    ON a.enterprise_no = c.enterprise_no AND a.supplier_code = c.supplier_code
-- 关联签约组织维度（预处理的维度数据）
    LEFT JOIN organization_info d
    ON a.enterprise_no = d.enterprise_no AND a.sign_subject_no = d.org_no
-- 关联管理组织维度（预处理的维度数据）
    LEFT JOIN organization_info e
    ON a.enterprise_no = e.enterprise_no AND a.pur_org_no = e.org_no
-- 关联合同类型维度（预处理的维度数据）
    LEFT JOIN contract_type_info f
    ON a.trans_type_no = f.ct_type_code
-- 关联采购模式维度（预处理的维度数据）
    LEFT JOIN purchase_model_info g
    ON a.pur_model_code = g.model_code
-- 关联适用组织信息
    LEFT JOIN applicable_orgs_info h
    ON a.enterprise_no = h.enterprise_no AND a.contract_no = h.contract_no
-- 关联条款信息（基于主数据的扩展查询）
    LEFT JOIN contract_clauses_info cci
    ON a.enterprise_no = cci.enterprise_no AND a.contract_no = cci.contract_no
    )

-- 最终查询明细级数据
SELECT * FROM dwd_final_price_contract
ORDER BY
    contract_no DESC,
    supplier_name,
    sign_org_name,
    sku_code;
