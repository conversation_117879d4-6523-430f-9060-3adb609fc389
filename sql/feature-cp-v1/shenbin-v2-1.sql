CREATE TABLE `dmp_analysis_subject`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no` varchar(64)  NOT NULL COMMENT '租户编号',
    `subject_no`    varchar(64)  NOT NULL COMMENT '分析主题唯一标识',
    `subject_name`  varchar(255) NOT NULL COMMENT '分析主题名称',
    `description`   text COMMENT '主题描述',
    `status`        tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`       tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`     varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`   varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`   varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`     varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`   varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`   varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY             `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='分析主题表';

CREATE TABLE `dmp_analysis_subject_dataset_ref`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no` varchar(64) NOT NULL COMMENT '租户编号',
    `subject_no`    varchar(64) NOT NULL COMMENT '分析主题编号',
    `dataset_no`    varchar(64) NOT NULL COMMENT '数据集编号',
    `status`        tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`       tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`     varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`   varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`   varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`     varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`   varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`   varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY             `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='分析主题与数据集关系表';

CREATE TABLE `dmp_dataset`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`       varchar(64)  NOT NULL COMMENT '租户编号',
    `dataset_no`          varchar(64)  NOT NULL COMMENT '数据集唯一标识',
    `dataset_code`        varchar(255) NOT NULL COMMENT '数据集编码',
    `dataset_name`        varchar(255) NOT NULL COMMENT '数据集名称',
    `driving_schema_name` varchar(255) NOT NULL COMMENT '驱动表schema',
    `driving_table_name`  varchar(255) NOT NULL COMMENT '驱动表',
    `description`         text COMMENT '数据集描述',
    `status`              tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`             tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`           varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`         varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`         varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`           varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`         varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`         varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`        timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                   `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集表';

CREATE TABLE `dmp_dataset_join_rel`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`      varchar(64)  NOT NULL COMMENT '租户编号',
    `dataset_no`         varchar(64)  NOT NULL COMMENT '数据集编号',
    `target_schema_name` varchar(255) NOT NULL COMMENT '目标表schema',
    `target_table_name`  varchar(255) NOT NULL COMMENT '目标表',
    `join_type`          varchar(32)  NOT NULL COMMENT '关联关系：innerjoin, leftjoin, rightjoin',
    `join_condition`     text         NOT NULL COMMENT '关联条件',
    `status`             tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`            tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`          varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`        varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`        varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`          varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`        varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`        varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`       timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                  `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集join关系表';

CREATE TABLE `dmp_dataset_field`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`    varchar(64)  NOT NULL COMMENT '租户编号',
    `dataset_no`       varchar(64)  NOT NULL COMMENT '数据集编号',
    `dataset_field_no` varchar(64)  NOT NULL COMMENT '数据集字段编号',
    `schema_name`      varchar(255) NOT NULL COMMENT '字段来源表schema',
    `table_name`       varchar(255) NOT NULL COMMENT '字段来源表',
    `field_code`       varchar(255) NOT NULL COMMENT '字段编码',
    `field_name`       varchar(255) NOT NULL COMMENT '字段名称',
    `description`      text COMMENT '数据集字段描述',
    `status`           tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`          tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`        varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`      varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`      varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`        varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`      varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`      varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`     timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集字段表';

CREATE TABLE `dmp_dimension`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`  varchar(64)  NOT NULL COMMENT '租户编号',
    `dimension_no`   varchar(100) NOT NULL COMMENT '维度编号',
    `dimension_code` varchar(255) NOT NULL COMMENT '维度编码',
    `dimension_name` varchar(255) NOT NULL COMMENT '维度名称',
    `schema_name`    varchar(255) NOT NULL COMMENT '字段来源表schema',
    `table_name`     varchar(255) NOT NULL COMMENT '字段来源表',
    `description`    text COMMENT '维度描述',
    `status`         tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`        tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`      varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`    varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`    varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`      varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`    varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`    varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`   timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY              `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='维度表';

CREATE TABLE `dmp_dimension_field`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`  varchar(64)  NOT NULL COMMENT '租户编号',
    `dimension_no`   varchar(100) NOT NULL COMMENT '维度编号',
    `field_code`     varchar(255) NOT NULL COMMENT '模型字段',
    `field_semantic` varchar(32)  NOT NULL COMMENT '字段语义：business_key，display_name，description_only',
    `description`    text COMMENT '维度描述',
    `display_order`  int(11) NOT NULL DEFAULT '1' COMMENT '展示顺序',
    `date_format`    varchar(32) NULL COMMENT '日期格式',
    `status`         tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`        tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`      varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`    varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`    varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`      varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`    varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`    varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`   timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY              `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='维度字段表';

CREATE TABLE `dmp_dataset_dimension_ref`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`    varchar(64) NOT NULL COMMENT '租户编号',
    `dataset_no`       varchar(64) NOT NULL COMMENT '所属数据集编号',
    `dataset_field_no` varchar(64) NOT NULL COMMENT '数据集字段编号',
    `dimension_no`     varchar(64) NOT NULL COMMENT '维度编号',
    `description`      text COMMENT '维度描述',
    `status`           tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`          tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`        varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`      varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`      varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`        varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`      varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`      varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`     timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='数据集字段和维度关联表';

CREATE TABLE `dmp_metric`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `enterprise_no`    varchar(64)  NOT NULL COMMENT '租户编号',
    `metric_no`        varchar(64)  NOT NULL COMMENT '指标编号',
    `metric_code`      varchar(255) NOT NULL COMMENT '指标编码',
    `metric_name`      varchar(255) NOT NULL COMMENT '指标名称',
    `unit`             varchar(255) DEFAULT NULL COMMENT '单位',
    `num_precision`    varchar(255) DEFAULT NULL COMMENT '精度',
    `display_format`   varchar(255) DEFAULT NULL COMMENT '展示格式',
    `description`      text COMMENT '指标描述',
    `dataset_no`       varchar(64)  NOT NULL COMMENT '所属数据集编号',
    `dataset_field_no` varchar(64)  NOT NULL COMMENT '数据集字段编号',
    `agg_type`         varchar(32)  DEFAULT NULL COMMENT '聚合方式',
    `status`           tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
    `deleted`          tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    `create_no`        varchar(64)  DEFAULT NULL COMMENT '创建人编号',
    `create_name`      varchar(128) DEFAULT NULL COMMENT '创建人名称',
    `create_time`      varchar(32)  DEFAULT NULL COMMENT '创建时间',
    `modify_no`        varchar(64)  DEFAULT NULL COMMENT '修改人编号',
    `modify_name`      varchar(128) DEFAULT NULL COMMENT '修改人名称',
    `modify_time`      varchar(32)  DEFAULT NULL COMMENT '修改时间',
    `op_timestamp`     timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
    PRIMARY KEY (`id`),
    KEY                `idx_enterprise_no` (`enterprise_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='指标表';