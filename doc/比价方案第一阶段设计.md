# 比价方案第一阶段设计

## 一、整体说明

### 1.1 术语理解

| **技术术语** | **面向业务人员的语言** | **面向开发的语言** | **举例** |
| --- | --- | --- | --- |
| 分析主题（Subject Area） | 我们要分析的业务领域 | 一类分析任务的组织容器 | 产品比价分析、供应商评估 |
| 数据集（DataSet） | 比价分析的字段清单（可视表） | 查询视图、逻辑表 | 一张产品价格对比表 |
| 数据模型（DataModel） | 系统已有的数据模块 | 可复用的数据结构定义 | 产品模型、价格模型 |
| 模型字段 | 表里的每一列字段 | 字段定义（含类型、表达式） | 产品名称、价格、品牌 |
| 维度（Dimension） | 用来分组/分类的字段 | SQL 中用于 GROUP BY 的字段 | 品牌、采购模式、机构 |
| 指标（Metric） | 用来统计/对比的数字 | SQL 中用于聚合函数的字段 | 最高价、最低价、平均价 |
| 字段引用关系表 | 从哪个模型选了哪个字段 | 数据集字段来源的映射配置 | 从产品模型选品牌，从价格模型选价格字段 |

### 1.2 流程说明

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Pd6l2Z7Q04jQ2l7M/img/d7b41d19-3029-4363-9ca1-2df01a77c61d.png)

## 二、表结构

### 2.1 ER图关系

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Pd6l2Z7Q04jQ2l7M/img/f0a0baae-f343-4df9-b6ac-64950763260d.png)

### 2.2 详细建表语句

```mysql
CREATE TABLE `dmp_analysis_subject` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enterprise_no` varchar(20) DEFAULT NULL COMMENT '租户编号',
  `subject_uuid` varchar(64) DEFAULT NULL COMMENT '分析主题唯一标识',
  `subject_name` varchar(128) NOT NULL COMMENT '分析主题名称',
  `description` text COMMENT '主题描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_subject_uuid` (`subject_uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分析主题表';

CREATE TABLE `dmp_dataset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enterprise_no` varchar(20) NOT NULL COMMENT '租户编号',
  `dataset_uuid` varchar(64) NOT NULL COMMENT '数据集唯一标识',
  `dataset_name` varchar(128) NOT NULL COMMENT '数据集名称',
  `description` text COMMENT '数据集描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dataset_uuid` (`dataset_uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据集表';

CREATE TABLE `dmp_analysis_subject_dataset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enterprise_no` varchar(20) DEFAULT NULL COMMENT '租户编号',
  `subject_uuid` varchar(64) NOT NULL COMMENT '分析主题ID',
  `dataset_uuid` varchar(64) NOT NULL COMMENT '数据集ID',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_subject_dataset_id` (`enterprise_no`,`subject_uuid`,`dataset_uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分析主题数据集关系表';

CREATE TABLE `dmp_data_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enterprise_no` varchar(20) DEFAULT NULL COMMENT '租户编号',
  `model_uuid` varchar(64) DEFAULT NULL COMMENT '模型唯一标识，UUID',
  `model_name` varchar(128) NOT NULL COMMENT '数据模型名称',
  `source_type` varchar(32) DEFAULT NULL COMMENT '数据源类型:1-mysql,2-doris,3-api',
  `source_db_name` varchar(128) DEFAULT NULL COMMENT '来源库名',
  `source_table_name` varchar(128) DEFAULT NULL COMMENT '来源表名/视图名',
  `source_api_url` varchar(200) DEFAULT NULL COMMENT '来源api',
  `description` text COMMENT '模型备注或描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_uuid` (`model_uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据模型表';

CREATE TABLE `dmp_data_model_field` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enterprise_no` varchar(20) DEFAULT NULL COMMENT '租户编号',
  `model_uuid` varchar(64) NOT NULL COMMENT '所属数据模型ID',
  `field_code` varchar(64) NOT NULL COMMENT '字段编码',
  `field_name` varchar(128) DEFAULT NULL COMMENT '字段名称',
  `is_primary` tinyint(2) DEFAULT '0' COMMENT '是否主键:0-否，1-是',
  `is_dimension` tinyint(2) DEFAULT '0' COMMENT '是否维度字段:0-否，1-是',
  `is_metric` tinyint(2) DEFAULT '0' COMMENT '是否指标字段:0-否，1-是',
  `data_type` varchar(64) DEFAULT NULL COMMENT '字段数据类型',
  `is_derived` tinyint(2) DEFAULT '0' COMMENT '是否派生字段:0-否，1-是',
  `expression` text COMMENT '字段表达式（如存在）',
  `component_type` varchar(64) DEFAULT NULL COMMENT '字段用于展示的组件类型（如下拉、数值等）',
  `description` text COMMENT '字段描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_model_uuid` (`enterprise_no`,`model_uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据模型字段表';

CREATE TABLE `dmp_dataset_field_ref` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enterprise_no` varchar(20) DEFAULT NULL COMMENT '租户编号',
  `dataset_uuid` varchar(64) NOT NULL COMMENT '所属数据集ID',
  `model_uuid` varchar(64) NOT NULL COMMENT '字段来源数据模型',
  `field_code` varchar(64) NOT NULL COMMENT '字段编码',
  `field_alias` varchar(128) DEFAULT NULL COMMENT '字段别名',
  `field_data_type` varchar(64) DEFAULT NULL COMMENT '字段数据类型（如 string, int, double, date 等）',
  `sort_num` int(11) DEFAULT NULL COMMENT '字段排序',
  `is_dimension` tinyint(1) DEFAULT '0' COMMENT '是否维度字段',
  `is_metric` tinyint(1) DEFAULT '0' COMMENT '是否指标字段',
  `is_derived` tinyint(4) DEFAULT '0' COMMENT '是否派生字段（1=是，0=否）',
  `expression` text COMMENT '派生字段表达式（仅当 is_derived=1 时有效）',
  `component_type` varchar(64) DEFAULT NULL COMMENT '前端组件类型（input/select/date/ref 等）',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_dataset_uuid` (`enterprise_no`,`dataset_uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据集模型字段引用关系表';

CREATE TABLE `dmp_report_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `enterprise_no` varchar(20) NOT NULL COMMENT '租户编号',
  `template_code` varchar(64) NOT NULL COMMENT '报表模板编码',
  `template_name` varchar(128) DEFAULT NULL COMMENT '报表模板名称',
  `description` text COMMENT '报表说明',
  `dataset_refs` json DEFAULT NULL COMMENT '数据集引用列表（JSON 数组，含主数据集与子数据集标识）',
  `join_relations` json DEFAULT NULL COMMENT '数据集关联关系配置（JSON）',
  `row_fields` json DEFAULT NULL COMMENT '行字段列表（JSON 数组）',
  `column_fields` json DEFAULT NULL COMMENT '列字段列表（JSON 数组）',
  `value_fields` json DEFAULT NULL COMMENT '值字段列表（JSON 数组，可含表达式字段）',
  `expression_fields` json DEFAULT NULL COMMENT '计算字段配置（JSON 数组）',
  `style_config` json DEFAULT NULL COMMENT '报表样式配置（JSON，可包含冻结列、隐藏字段、样式项等）',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_template_code` (`enterprise_no`,`template_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='报表模板表';

CREATE TABLE `dmp_report_template_share` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `enterprise_no` varchar(20) NOT NULL COMMENT '租户编号',
  `org_no` varchar(20) DEFAULT NULL COMMENT '组织编号',
  `use_enterprise_no` varchar(20) DEFAULT NULL COMMENT '使用租户编号',
  `use_org_no` varchar(20) DEFAULT NULL COMMENT '使用组织编号',
  `template_code` varchar(64) NOT NULL COMMENT '报表模板编码',
  `status` tinyint(1) DEFAULT '1' COMMENT '数据是否有效：0-无效，1-有效',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_no` varchar(32) DEFAULT NULL COMMENT '创建人编号',
  `create_name` varchar(100) DEFAULT NULL COMMENT '创建人名称',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `modify_no` varchar(32) DEFAULT NULL COMMENT '修改人编号',
  `modify_name` varchar(100) DEFAULT NULL COMMENT '修改人名称',
  `modify_time` varchar(19) DEFAULT NULL COMMENT '修改时间',
  `op_timestamp` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_template_code` (`enterprise_no`,`template_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表模板共享关系表';
```

备注：此库只是做方案设计对称信息用的，正式开发时需确定是否放在yyigou\_ddc\_dmp库。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Pd6l2Z7Q04jQ2l7M/img/64f2a73e-b996-487f-8ad5-892a866a8018.png)

## 三、数据预研

1.  报表模板中插入了一条数据，主要为了给前端渲染样式使用
    

```mysql
INSERT INTO dmp_report_template (
	enterprise_no,
	template_code,
	template_name,
	description,
	dataset_refs,
	join_relations,
	row_fields,
	column_fields,
	value_fields,
	expression_fields,
	style_config
)
VALUES
	(
		'2000000',
		'price_comparison_tpl_001',
		'价格比对分析报表',
		'指定某品牌产品，子公司采购价与集团采购价比较',
		'[{"dataset_code": "ds_supplier_prices", "role": "main"}, {"dataset_code": "ds_baseline_price", "role": "baseline"}]',
		'[{"source_dataset": "ds_supplier_prices", "target_dataset": "ds_baseline_price", "on": [{"source_field": "产品名称", "target_field": "产品名称"}, {"source_field": "品牌", "target_field": "品牌"}], "type": "left"}]',
		'["产品名称", "品牌"]',
		'[{"field": "供应商", "values": ["供应商A", "供应商B", "基准供应商"]}, {"field": "管理组织", "values": ["A组织", "B组织", "集团"]}, {"field": "采购模式", "values": ["集采", "集采", "集中采购"]}]',
		'[{"field": "含税价", "agg": "SUM"}, {"field": "差异率", "agg": "AVG"}]',
		'[{"field_alias": "差异率", "expression": "(含税价 - 基准价) / 基准价", "data_type": "decimal", "format": "percent", "description": "与基准价的差异率"}]',
		'{"frozenCols": 2, "highlightDiffColor": true}'
	);
```

2.  mock前端渲染数据
    

[请至钉钉文档查看附件《指定品牌子公司基准价比较mock\_data.json》](https://alidocs.dingtalk.com/i/nodes/YMyQA2dXW7rKBpyqu6r3E3n98zlwrZgb?corpId=ding7e4f7e636417345635c2f4657eb6378f&iframeQuery=anchorId%3DX02mcg5i8zj8mjzrnjpso2&sideCollapsed=true&utm_scene=team_space)

3.  antV/S2显示样式
    

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Pd6l2Z7Q04jQ2l7M/img/d787cf72-162d-4388-af58-410dc4fb107a.png)

虽然显示出来了，意思也差不多，但是和报表原始样式不太一致，可能需要高峰那边看看要怎么配合给到前端，因为第一阶段报表模板是写死的。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Pd6l2Z7Q04jQ2l7M/img/36abb500-7c4e-486a-9d3d-d1398516cbeb.png)

## 四、数据集数据模型整理

### 4.1 表结构说明

1.  ER图
    

![数据模型.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Pd6l2Z7Q04jQ2l7M/img/eed3727a-fadd-4fe1-adc5-21009becc8e4.png)

2.  表结构和表关系说明
    

*   采购政策协议涉及到的表如下所示：
    
    *   ecs\_enterprise：租户表
        
    *   uim\_organization：组织表
        
    *   uim\_org\_group：组织分组表
        
    *   uim\_org\_group\_rel：组织分组明细
        
    *   ct\_purchase：采购政策协议
        
    *   ct\_purchase\_item：采购政策明细表
        
    *   ct\_purchase\_org：协议适用组织表
        
    *   ct\_purchase\_clauses：合同政策条款
        
    *   ct\_purchase\_policy\_indicator：采购政策指标表
        
    *   ct\_purchase\_service\_fee：采购合同政策服务费用表
        
    *   bdc\_common\_param：条款参数表
        
    *   bdc\_supplier：供应商表
        
    *   bdc\_registration\_cert：注册证表
        
    *   bdc\_company：生产企业表
        
    *   bdc\_goods\_category：产品大类表（存货分类表）
        
    *   bdc\_brand：品牌表
        
    *   bdc\_goods：物料表
        

*   关联政策协议涉及到的表如下所示：
    
    *   ecs\_enterprise：租户表
        
    *   uim\_organization：组织表
        
    *   uim\_org\_group：组织分组表
        
    *   uim\_org\_group\_rel：组织分组明细
        
    *   ct\_associate\_agreement：内部政策协议
        
    *   ct\_associate\_item：内部政策协议明细
        
    *   ct\_associate\_org：内部政策适用组织
        
    *   ct\_associate\_clauses：内部合同政策条款
        
    *   ct\_associate\_policy\_indicator：内部政策协议指标表
        
    *   ct\_associate\_service\_fee：内部合同政策服务费用表
        
    *   bdc\_common\_param：条款参数表
        
    *   bdc\_supplier：供应商表
        
    *   bdc\_registration\_cert：注册证表
        
    *   bdc\_company：生产企业表
        
    *   bdc\_goods\_category：产品大类表（存货分类表）
        
    *   bdc\_brand：品牌表
        
    *   bdc\_goods：物料表
        

3.  业务说明
    

:::
采购政策协议：是指集团组织或者子公司向供应商采购医疗器械设备，或者供应商向集团或者子公司投放、半卖半送设备产品所签订的设备政策协议，通常约定了任务量指标或者服务费用。

内部政策协议：集团组织间约定的针对设备类产品达成的价格，任务量约定。供货组织是集团或子公司，适用组织是子公司；即供货组织是适用组织（子公司）的供应商。也就是内部政策协议在报表分析时，可以转换成采购政策协议，这样在子公司的角度，都可以以采购的视角去统计分析。

关键表关系说明：

政策协议是主表，政策协议明细是子表，任务量指标表和服务费用表是政策协议明细的子表；

合同政策条款参数表既是政策协议的子表，又是政策协议明细的子表；原因是签订政策协议时会有约定各种协议条款，在引用政策协议做合同时，政策的条款会带到合同里，同时合同又可以约定自己的合同条款。

政策协议明细，一般说的是指设备产品约定任务量或者人份数；并且有时涉及到流水线产品时，一个政策产品下会有多个组合明细产品，在政策协议明细表里会有个字段是否政策产品来表示。
:::

---

## 五、doris数据建模

### 5.1 Doris 宽表字段推荐清单（字段 + 角色分类）

| 字段名称 | 字段角色 | 说明 |
| --- | --- | --- |
| 协议编号 | 维度 | 来源 `ct_purchase`，唯一标识政策协议 |
| 签约组织 | 维度 | 来源 `ct_purchase.org_no`，签约方组织 |
| 管理组织 | 维度 | 来源 `ct_purchase.manage_org_no`，归属组织 |
| 适用组织 | 维度 | 来源 `ct_purchase_org`，协议适用的子公司组织 |
| 供应商 | 维度 | 来源 `bdc_supplier`，供应商名称 |
| 供应商性质 | 维度 | 来源 `bdc_supplier.supplier_type` |
| 协议状态 | 维度 | 来源 `ct_purchase.status` |
| 协议负责人 | 维度 | 来源 `ct_purchase.charge_person` |
| 协议类型（采购/内部） | 维度 | 来源 `ct_purchase.policy_type` |
| 项目名称 | 维度 | 来源 `ct_purchase.project_name` |
| 产品名称 | 维度 | 来源 `bdc_goods.name` |
| 品牌名称 | 维度 | 来源 `bdc_brand.name` |
| 型号 | 维度 | 来源 `bdc_goods.model_no` |
| 注册证编号 | 维度 | 来源 `bdc_registration_cert.cert_no` |
| 产品类别 | 维度 | 来源 `bdc_goods_category.name` |
| 采购模式 | 维度 | 来源 `ct_purchase.purchase_mode` |
| 价格时间 | 维度 | 来源 `ct_purchase_item.price_date` |
| 任务量人份 | 指标 | 来源 `ct_purchase_policy_indicator.target_quantity` |
| 合作周期（月） | 指标 | 来源 `ct_purchase_policy_indicator.duration_months` |
| 含税价 | 指标 | 来源 `ct_purchase_item.tax_price` |
| 租赁费 | 指标 | 来源 `ct_purchase_service_fee.lease_fee` |
| 保证金 | 指标 | 来源 `ct_purchase_service_fee.deposit_amount` |
| 差异率 | 派生 | `(含税价 - 基准价) / 基准价` |
| 完成率 | 派生 | `实际金额 / 任务金额` |
| 产权归属(期初) | 维度 | 来源 `ct_purchase_clauses.ownership_start` |
| 各类政策条款 | 维度 | 来源 `ct_purchase_clauses.xxx_clause_type` |

---

### 5.2 字段来源表参考（MySQL 表）

*   `ct_purchase`：协议主表
    
*   `ct_purchase_item`：设备明细
    
*   `ct_purchase_org`：适用组织
    
*   `ct_purchase_policy_indicator`：任务量、人份、金额等指标
    
*   `ct_purchase_service_fee`：服务类费用（租赁费、保证金）
    
*   `ct_purchase_clauses`：各类政策条款（维保、违约、产权等）
    
*   `bdc_goods`, `bdc_brand`, `bdc_registration_cert`：产品类
    
*   `bdc_supplier`, `bdc_company`：企业类
    
*   `uim_organization`, `uim_org_group`：组织维度
    

---

### 5.3 Doris 宽表建表 SQL（表名：`**dwf_ct_policy_full**`）

```sql
CREATE TABLE `dwf_ct_policy_full` (
  `policy_no` STRING COMMENT '协议编号',
  `sign_org` STRING COMMENT '签约组织',
  `supplier_name` STRING COMMENT '供应商',
  `supplier_type` STRING COMMENT '供应商性质',
  `manage_org` STRING COMMENT '管理组织',
  `policy_status` STRING COMMENT '协议状态',
  `owner` STRING COMMENT '责任人',
  `apply_org` STRING COMMENT '适用组织',
  `grouo_no` STRING COMMENT '组号',
  `policy_principal_no` STRING COMMENT '政策负责人编号',
  `policy_principal_name` STRING COMMENT '政策负责人名称',
  `is_` STRING COMMENT '是否流水线',
  `policy_no` STRING COMMENT '政策编码',
  `policy_name` STRING COMMENT '政策名称',
  `sku_code` STRING COMMENT '产品编码',
  `product_name` STRING COMMENT '产品名称',
  `register_cert_no` STRING COMMENT '注册证号',
  `category_no` STRING COMMENT '大类-五级分类',
  `brand_name` STRING COMMENT '品牌',
  `company_no` STRING COMMENT '生产企业',
  `model_no` STRING COMMENT '规格型号',
  `cop_method` STRING COMMENT '合作方式',
  `invalid_status` STRING COMMENT '效期状态',
  `tax_rate` DECIMAL(10,4) COMMENT '税率(%)',
  `tax_price` DECIMAL(10,4) COMMENT '含税单价',
  `num` BIGINT COMMENT '数量',
  `tax_amount` DECIMAL(18,6) COMMENT '含税金额',
  `task_volume_amount` DECIMAL(18,6) COMMENT '任务量金额',
  `task_volume_person` BIGINT COMMENT '任务量人份',
  `cooperation_months` INT COMMENT '合作周期（月）',
  `ownership_start` STRING COMMENT '产权归属(期初)',
  `penalty_level` STRING COMMENT '违约等级',
  `penalty_clause` STRING COMMENT '违约条款',
  `lease_clause` STRING COMMENT '租赁条款',
  `maintain_clause` STRING COMMENT '维保条款',
  `deposit_clause` DECIMAL(18,6) COMMENT '保证金条款',
  `rebate_clause` STRING COMMENT '返利/返还/赠送条款'
)
UNIQUE KEY(`policy_no`, `product_name`, `manage_org`)
DISTRIBUTED BY HASH(`policy_no`) BUCKETS 10
PARTITION BY RANGE (`price_date`) (
  PARTITION p2023 VALUES LESS THAN ("2024-01-01"),
  PARTITION p2024 VALUES LESS THAN ("2025-01-01"),
  PARTITION pmax VALUES LESS THAN ("9999-12-31")
)
PROPERTIES (
  "replication_allocation" = "tag.location.default: 3",
  "enable_unique_key_merge_on_write" = "true",
  "light_schema_change" = "true",
  "storage_format" = "V2"
);

```
---

### 5.4 Doris 宽表字段映射（ETL 推荐来源）

你可以在上方看到一个可浏览表格，里面列出了：

*   Doris 宽表字段中文名
    
*   Doris 字段编码（ETL 可作为列名）
    
*   字段角色（维度、指标、派生）
    
*   推荐来源：MySQL 表结构（如 `ct_purchase`, `ct_purchase_item`, `bdc_goods`, 等）
    

这些将用于 ETL 字段映射配置。例如：

| 宽表字段名称 | 字段编码 | 字段类型 | 推荐来源表 |
| --- | --- | --- | --- |
| 含税单价 | 含税单价 | 指标 | `ct_purchase_item.tax_price` |
| 协议编号 | 协议编号 | 维度 | `ct_purchase.purchase_no` |
| 违约条款 | 违约条款 | 维度 | `ct_purchase_clauses.penalty_clause` |

```sql
{
  "target_table": "dwf_ct_policy_full",
  "field_mappings": [
    {
      "target_field": "policy_no",
      "source_table": "ct_purchase",
      "source_field": "purchase_no"
    },
    {
      "target_field": "product_name",
      "source_table": "bdc_goods",
      "source_field": "name"
    },
    {
      "target_field": "tax_price",
      "source_table": "ct_purchase_item",
      "source_field": "final_tax_price"
    },
    {
      "target_field": "supplier_name",
      "source_table": "bdc_supplier",
      "source_field": "supplier_name"
    }
    // ...
  ]
}

```
---

### 5.5 下一步计划

1.  **开发 ETL 映射表或任务配置**（DataX / Flink / Hive），字段名 → 源表字段
    
2.  **将这张表注册为 Doris 数据模型（**`**dmp_data_model**`**）**
    
3.  **为每个字段补充建模元数据（**`**dmp_data_model_field**`**）**
    
4.  **构建数据集 & 报表模板配置，基于宽表模型进行报表渲染**
    

如果你还需要我帮你生成：

*   `INSERT INTO dmp_data_model_field` 的初始化脚本；
    
*   Doris → MySQL 映射的 JSON 配置模板（供 DataX 使用）；
    
*   多表聚合宽表的 Hive/Flink SQL；