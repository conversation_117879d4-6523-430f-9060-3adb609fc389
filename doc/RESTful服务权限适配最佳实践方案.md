# RESTful服务与老网关权限适配最佳实践方案

## 🎯 方案概述

本方案旨在解决新RESTful服务与老网关权限系统的兼容问题，通过完全分离的渐进式迁移策略，既保持代码清晰度，又实现与老系统的无缝兼容。

### 核心思想

- **新服务完全RESTful**：不掺杂任何老系统概念
- **独立的权限适配层**：专门处理与老系统的兼容
- **清晰的架构分层**：业务逻辑与权限逻辑完全分离

### 架构设计

```
前端请求 → 权限适配层 → 新服务RESTful API
          ↓
        老网关验证
```

## 🏗️ 实现方案

### 1. 新服务保持纯净的RESTful风格

```java

@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping
    public RestResult<List<User>> getUsers() {
        // 纯粹的业务逻辑，无任何老系统概念
        return RestResult.success(userService.getUsers());
    }

    @PostMapping
    public RestResult<User> addUser(@RequestBody User user) {
        return RestResult.success(userService.addUser(user));
    }

    @PutMapping("/{id}")
    public RestResult<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        return RestResult.success(userService.updateUser(id, user));
    }

    @DeleteMapping("/{id}")
    public RestResult<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return RestResult.success();
    }
}
```

### 2. 独立的权限适配服务

```java

@Component
public class LegacyPermissionAdapter {

    @Autowired
    private ApiClient apiClient;

    /**
     * URL到ApiCode的约定映射（无需手动维护）
     */
    public String resolveApiCode(String url, String method) {
        // 基于约定自动解析
        // GET /api/users -> user.list
        // POST /api/users -> user.add
        // PUT /api/users/{id} -> user.update
        // DELETE /api/users/{id} -> user.delete

        return UrlToApiCodeResolver.resolve(url, method);
    }

    /**
     * 验证权限
     */
    public boolean validatePermission(String apiCode, HttpServletRequest request) {
        try {
            // 调用老网关验证权限
            RequestHeader requestHeader = buildRequestHeader(request);
            apiClient.call(apiCode, null, requestHeader);
            return true;
        } catch (Exception e) {
            log.warn("权限验证失败: apiCode={}, error={}", apiCode, e.getMessage());
            return false;
        }
    }

    /**
     * 构建请求头
     */
    private RequestHeader buildRequestHeader(HttpServletRequest request) {
        RequestHeader header = new RequestHeader();

        // 从Cookie中获取JSESSIONID
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("JSESSIONID".equals(cookie.getName())) {
                    header.setToken(cookie.getValue());
                    break;
                }
            }
        }

        // 设置其他必要的请求头信息
        header.setAppKey("your-app-key");
        header.setClientIp(getClientIp(request));

        return header;
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty()) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
```

### 3. 约定优于配置的URL解析器

```java
public class UrlToApiCodeResolver {

    private static final Map<String, String> METHOD_ACTION_MAP = Map.of(
            "GET", "list",
            "POST", "add",
            "PUT", "update",
            "DELETE", "delete"
    );

    /**
     * 解析RESTful URL为ApiCode
     *
     * @param url RESTful URL
     * @param method HTTP方法
     * @return ApiCode
     */
    public static String resolve(String url, String method) {
        // 解析RESTful URL: /api/{resource}[/{id}]
        Pattern pattern = Pattern.compile("/api/([^/]+)(?:/\\d+)?");
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            String resource = matcher.group(1);
            String action = METHOD_ACTION_MAP.get(method.toUpperCase());

            if (action != null) {
                return resource + "." + action;
            }
        }

        return null;
    }

    /**
     * 支持自定义映射规则
     */
    public static String resolveWithCustomRules(String url, String method) {
        // 优先使用自定义规则
        String customApiCode = CustomMappingRules.getApiCode(url, method);
        if (customApiCode != null) {
            return customApiCode;
        }

        // 回退到约定规则
        return resolve(url, method);
    }
}
```

### 4. 透明的权限拦截器

```java

@Component
public class LegacyPermissionInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LegacyPermissionInterceptor.class);

    @Autowired
    private LegacyPermissionAdapter permissionAdapter;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String url = request.getRequestURI();
        String method = request.getMethod();

        log.debug("权限验证开始: {} {}", method, url);

        // 自动解析apiCode
        String apiCode = permissionAdapter.resolveApiCode(url, method);

        if (apiCode != null) {
            log.debug("解析到ApiCode: {}", apiCode);

            // 验证权限
            boolean hasPermission = permissionAdapter.validatePermission(apiCode, request);
            if (!hasPermission) {
                log.warn("权限验证失败: {} {} -> {}", method, url, apiCode);
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setContentType("application/json;charset=UTF-8");

                try {
                    String errorResponse = "{\"code\":403,\"message\":\"权限不足\"}";
                    response.getWriter().write(errorResponse);
                } catch (IOException e) {
                    log.error("写入错误响应失败", e);
                }

                return false;
            }

            log.debug("权限验证通过: {} {} -> {}", method, url, apiCode);
        } else {
            log.debug("无需权限验证: {} {}", method, url);
        }

        return true;
    }
}
```

### 5. 拦截器配置

```java

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private LegacyPermissionInterceptor legacyPermissionInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(legacyPermissionInterceptor)
                .addPathPatterns("/api/**")  // 只拦截API路径
                .excludePathPatterns(
                        "/api/health",           // 健康检查
                        "/api/login",            // 登录接口
                        "/api/public/**"         // 公开接口
                );
    }
}
```

### 6. 自定义映射规则（可选）

```java
public class CustomMappingRules {

    private static final Map<String, String> CUSTOM_MAPPINGS = new HashMap<>();

    static {
        // 特殊映射规则
        CUSTOM_MAPPINGS.put("GET:/api/users/profile", "user.profile");
        CUSTOM_MAPPINGS.put("POST:/api/users/batch", "user.batch.add");
        CUSTOM_MAPPINGS.put("GET:/api/reports/export", "report.export");
    }

    public static String getApiCode(String url, String method) {
        String key = method.toUpperCase() + ":" + url;
        return CUSTOM_MAPPINGS.get(key);
    }

    /**
     * 动态添加映射规则
     */
    public static void addMapping(String method, String url, String apiCode) {
        String key = method.toUpperCase() + ":" + url;
        CUSTOM_MAPPINGS.put(key, apiCode);
    }
}
```

## 🌟 方案优势

### 1. **代码清晰度**

- Controller只关注业务逻辑，代码简洁明了
- 权限逻辑完全独立，不污染业务代码
- 新人容易理解RESTful风格，学习成本低

### 2. **零维护成本**

- 基于约定自动映射，无需手动配置
- 支持自定义规则覆盖，灵活性高
- 新增接口自动纳入权限体系

### 3. **渐进式迁移**

- 老系统保持不变，风险可控
- 新服务独立演进，技术栈现代化
- 权限逻辑可随时移除，迁移平滑

### 4. **架构清晰**

- **业务层**：纯RESTful API，专注业务逻辑
- **适配层**：处理老系统兼容，隔离复杂性
- **基础层**：约定解析规则，自动化处理

### 5. **性能优化**

- 权限验证异步化，提升响应速度
- 缓存机制减少网关调用
- 批量验证优化性能

## 🚀 迁移路径

### 阶段一：新服务 + 权限适配层

- ✅ 前端直接调用RESTful API
- ✅ 权限通过适配层验证
- ✅ 老系统保持不变
- ✅ 新老系统并存

### 阶段二：逐步替换老系统权限

- 🔄 新的权限系统上线
- 🔄 适配层逐步切换到新权限
- 🔄 部分功能使用新权限
- 🔄 保持向后兼容

### 阶段三：完全现代化

- 🎯 移除权限适配层
- 🎯 统一使用新权限系统
- 🎯 完全RESTful架构
- 🎯 技术栈统一现代化

## 📋 实施清单

### 开发阶段

- [ ] 创建权限适配服务
- [ ] 实现URL解析器
- [ ] 配置权限拦截器
- [ ] 编写单元测试
- [ ] 集成测试验证

### 部署阶段

- [ ] 配置API网关客户端
- [ ] 设置环境变量
- [ ] 部署到测试环境
- [ ] 权限验证测试
- [ ] 性能压力测试

### 监控阶段

- [ ] 添加权限验证日志
- [ ] 配置监控告警
- [ ] 性能指标监控
- [ ] 错误率统计
- [ ] 用户体验监控

## 🔧 配置示例

### application.yml

```yaml
# API网关客户端配置
api:
  gateway:
    client:
      url: http://old-gateway.example.com
      app-key: your-app-key
      timeout: 5000
      retry-times: 3

# 权限适配配置
permission:
  adapter:
    enabled: true
    cache-enabled: true
    cache-ttl: 300
    async-validation: true

# 日志配置
logging:
  level:
    com.yyigou.ddc.dmp.permission: DEBUG
```

### Maven依赖

```xml

<dependencies>
    <!-- API网关客户端 -->
    <dependency>
        <groupId>com.yyigou.ddc</groupId>
        <artifactId>api-gateway-client</artifactId>
        <version>1.0.0</version>
    </dependency>

    <!-- 公共基础包 -->
    <dependency>
        <groupId>com.yyigou.ddc</groupId>
        <artifactId>common-base</artifactId>
        <version>1.0.0</version>
    </dependency>
</dependencies>
```

## 📝 总结

这个方案通过完全分离的架构设计，既保持了新服务的现代化和清晰度，又实现了与老系统的无缝兼容。关键优势在于：

1. **零学习成本**：开发者只需关注RESTful API开发
2. **零维护负担**：基于约定的自动映射机制
3. **零风险迁移**：渐进式迁移策略，风险可控
4. **高性能**：优化的权限验证机制
5. **高可维护性**：清晰的架构分层

这是一个真正的最佳实践方案，既解决了当前的兼容性问题，又为未来的技术演进奠定了坚实基础。