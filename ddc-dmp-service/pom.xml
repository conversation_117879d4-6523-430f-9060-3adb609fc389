<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>service-ddc-dmp</artifactId>
        <groupId>com.yyigou</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ddc-dmp-service</artifactId>
    <name>ddc-dmp-service</name>
    <description>Service layer module for ddc-dmp project</description>

    <dependencies>
        <!-- DAO Module -->
        <dependency>
            <groupId>com.yyigou</groupId>
            <artifactId>ddc-dmp-dao</artifactId>
        </dependency>

        <!-- Common Module -->
        <dependency>
            <groupId>com.yyigou</groupId>
            <artifactId>ddc-dmp-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yyigou</groupId>
            <artifactId>ddc-dmp-manager</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Spring Boot Service -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>
</project>