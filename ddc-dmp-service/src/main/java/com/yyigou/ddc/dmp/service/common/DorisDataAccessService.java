package com.yyigou.ddc.dmp.service.common;

import com.yyigou.ddc.dmp.common.context.SqlExecuteContext;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.SqlColumnMetaBO;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * doris的数据访问服务
 * <AUTHOR>
 * @date 2025/07/22
 */
public interface DorisDataAccessService {
    List<SqlColumnMetaBO> extractColumnNamesFromSql(String selectSql) throws SQLException;

    List<Map<String, Object>> executeQuery(SqlExecuteContext sqlExecuteContext) throws SQLException;
}
