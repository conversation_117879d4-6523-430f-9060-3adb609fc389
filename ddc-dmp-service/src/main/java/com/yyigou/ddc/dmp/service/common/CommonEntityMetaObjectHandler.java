package com.yyigou.ddc.dmp.service.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.StatusEnum;
import com.yyigou.ddc.dmp.model.bo.session.OperationModel;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
@Slf4j
@Component
public class CommonEntityMetaObjectHandler implements MetaObjectHandler {


    /**
     * 注意：
     * 1. 如果字段上没有加 @TableField(fill = FieldFill.INSERT) 注解，则 strictXxxxFill 不会填充该字段。比如某个entity上的deleted字段没加注解，则不会被设置为0
     * 2. 即使字段上加了填充注解 @TableField(fill = FieldFill.INSERT) ，严格模式填充策略下,默认有值不覆盖,如果提供的值为null也不填充
     *
     * @param metaObject 元对象
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("开始插入填充...");
        String employeeNo = Optional.ofNullable(UserHandleUtils.getOperationModelIfPreset()).map(OperationModel::getEmployerNo).orElse(null);
        String userName = Optional.ofNullable(UserHandleUtils.getOperationModelIfPreset()).map(OperationModel::getUserName).orElse(null);
        String now = DateUtil.now();

        this.strictInsertFill(metaObject, "createNo", String.class, employeeNo);
        this.strictInsertFill(metaObject, "createName", String.class, userName);
        this.strictInsertFill(metaObject, "createTime", String.class, now);
        this.strictInsertFill(metaObject, "modifyNo", String.class, employeeNo);
        this.strictInsertFill(metaObject, "modifyName", String.class, userName);
        this.strictInsertFill(metaObject, "modifyTime", String.class, now);

        this.strictInsertFill(metaObject, "deleted", Integer.class, DeletedEnum.UN_DELETE.getValue());
        this.strictInsertFill(metaObject, "status", Integer.class, StatusEnum.EFFECTIVE.getValue());
    }

    /**
     * 注意：
     * 1. 在 update(T entity, Wrapper<T> updateWrapper) 时，entity 不能为空，否则自动填充失效
     *
     * @param metaObject 元对象
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("开始更新填充...");
        String employeeNo = Optional.ofNullable(UserHandleUtils.getOperationModelIfPreset()).map(OperationModel::getEmployerNo).orElse(null);
        String userName = Optional.ofNullable(UserHandleUtils.getOperationModelIfPreset()).map(OperationModel::getUserName).orElse(null);
//        this.strictUpdateFill(metaObject, "modifyNo", String.class, employeeNo);
//        this.strictUpdateFill(metaObject, "modifyName", String.class, userName);
//        this.strictUpdateFill(metaObject, "modifyTime", String.class, DateUtil.now());
        if (hasUpdateFill(metaObject, "modifyNo")) {
            this.setFieldValByName("modifyNo", employeeNo, metaObject);
        }
        if (hasUpdateFill(metaObject, "modifyName")) {
            this.setFieldValByName("modifyName", userName, metaObject);
        }
        if (hasUpdateFill(metaObject, "modifyTime")) {
            this.setFieldValByName("modifyTime", DateUtil.now(), metaObject);
        }

    }


    /**
     * 检查字段是否具有 @TableField(fill = FieldFill.INSERT_UPDATE) 注解 或 fill = FieldFill.UPDATE
     *
     * @param metaObject 元对象
     * @param fieldName  字段名
     * @return 是否有 INSERT_UPDATE 填充注解
     */
    private boolean hasUpdateFill(MetaObject metaObject, String fieldName) {
        try {
            // 获取实体类
            Class<?> entityClass = metaObject.getOriginalObject().getClass();

            // 获取字段
            Field field = ReflectUtil.getField(entityClass, fieldName);
            if (field == null) {
                return false;
            }

            // 获取 TableField 注解
            TableField tableField = field.getAnnotation(TableField.class);
            if (tableField == null) {
                return false;
            }

            // 检查 fill 属性是否为 INSERT_UPDATE
            return tableField.fill() == FieldFill.INSERT_UPDATE || tableField.fill() == FieldFill.UPDATE;
        } catch (Exception e) {
            log.warn("检查字段填充注解时出错: {}", fieldName, e);
            return false;
        }
    }
}
