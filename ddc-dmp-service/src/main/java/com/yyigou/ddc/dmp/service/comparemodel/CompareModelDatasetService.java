package com.yyigou.ddc.dmp.service.comparemodel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelDataset;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
public interface CompareModelDatasetService extends IService<CompareModelDataset> {
    @Transactional
    void saveModelRefBaselineDataset(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<Long, CompareModelDataset> idToDbModelDatasetMap);

    @Transactional
    void updateModelRefBaselineDataset(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel);

    void fillBaselineDatasetList(CompareModelGetRes compareModelGetRes, CompareModel compareModel);
}
