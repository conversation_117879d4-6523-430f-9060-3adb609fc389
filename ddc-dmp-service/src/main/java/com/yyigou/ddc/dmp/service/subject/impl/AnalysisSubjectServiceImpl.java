package com.yyigou.ddc.dmp.service.subject.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.AnalysisSubjectDatasetRef;
import com.yyigou.ddc.dmp.dao.dataset.mapper.AnalysisSubjectDatasetRefMapper;
import com.yyigou.ddc.dmp.dao.subject.entity.AnalysisSubject;
import com.yyigou.ddc.dmp.dao.subject.mapper.AnalysisSubjectMapper;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectDatasetRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.SubjectRes;
import com.yyigou.ddc.dmp.service.subject.AnalysisSubjectService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AnalysisSubjectServiceImpl extends ServiceImpl<AnalysisSubjectMapper, AnalysisSubject> implements AnalysisSubjectService {
    @Resource
    private AnalysisSubjectDatasetRefMapper analysisSubjectDatasetRefMapper;


    @Override
    @Transactional
    public String saveSubject(SubjectSaveReq subjectSaveReq) {
        AnalysisSubject analysisSubject = BeanCopyUtil.copyFieldsByJson(subjectSaveReq, AnalysisSubject.class);
        if (StringUtils.isEmpty(analysisSubject.getSubjectNo())) {
            //新增
            analysisSubject.setSubjectNo(UUID.fastUUID().toString());
            save(analysisSubject);
        } else {
            boolean exists = exists(Wrappers.<AnalysisSubject>lambdaQuery()
                    .eq(AnalysisSubject::getEnterpriseNo, analysisSubject.getEnterpriseNo())
                    .eq(AnalysisSubject::getSubjectNo, analysisSubject.getSubjectNo())
                    .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("分析主题不存在");
            }

            AnalysisSubject toDeleteAnalysisSubject = new AnalysisSubject();
            toDeleteAnalysisSubject.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteAnalysisSubject, Wrappers.<AnalysisSubject>lambdaQuery()
                    .eq(AnalysisSubject::getEnterpriseNo, analysisSubject.getEnterpriseNo())
                    .eq(AnalysisSubject::getSubjectNo, analysisSubject.getSubjectNo())
                    .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
            save(analysisSubject);
        }

        return analysisSubject.getSubjectNo();
    }

    @Override
    public SubjectRes getSubject(SubjectGetReq subjectGetReq) {
        AnalysisSubject analysisSubject = getOne(Wrappers.<AnalysisSubject>lambdaQuery()
                .eq(AnalysisSubject::getEnterpriseNo, subjectGetReq.getEnterpriseNo())
                .eq(AnalysisSubject::getSubjectNo, subjectGetReq.getSubjectNo())
                .eq(AnalysisSubject::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == analysisSubject) {
            throw new BusinessException("分析主题不存在");
        }

        SubjectRes subjectRes = BeanCopyUtil.copyFields(analysisSubject, SubjectRes.class);

        List<AnalysisSubjectDatasetRef> analysisSubjectDatasetRefList = analysisSubjectDatasetRefMapper.selectList(Wrappers.<AnalysisSubjectDatasetRef>lambdaQuery()
                .eq(AnalysisSubjectDatasetRef::getEnterpriseNo, analysisSubject.getEnterpriseNo())
                .eq(AnalysisSubjectDatasetRef::getSubjectNo, analysisSubject.getSubjectNo())
                .eq(AnalysisSubjectDatasetRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isNotEmpty(analysisSubjectDatasetRefList)) {
            List<String> datasetNoList = analysisSubjectDatasetRefList.stream().map(AnalysisSubjectDatasetRef::getDatasetNo).collect(Collectors.toList());
            subjectRes.setDatasetNoList(datasetNoList);
        }

        return subjectRes;
    }

    @Override
    @Transactional
    public Long saveSubjectDatasetRef(SubjectDatasetRefSaveReq subjectDatasetRefSaveReq) {
        analysisSubjectDatasetRefMapper.update(null, Wrappers.<AnalysisSubjectDatasetRef>lambdaUpdate()
                .set(AnalysisSubjectDatasetRef::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(AnalysisSubjectDatasetRef::getEnterpriseNo, subjectDatasetRefSaveReq.getEnterpriseNo())
                .eq(AnalysisSubjectDatasetRef::getSubjectNo, subjectDatasetRefSaveReq.getSubjectNo())
                .eq(AnalysisSubjectDatasetRef::getDatasetNo, subjectDatasetRefSaveReq.getDatasetNo())
                .eq(AnalysisSubjectDatasetRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        AnalysisSubjectDatasetRef subjectDataset = new AnalysisSubjectDatasetRef();
        subjectDataset.setEnterpriseNo(subjectDatasetRefSaveReq.getEnterpriseNo());
        subjectDataset.setSubjectNo(subjectDatasetRefSaveReq.getSubjectNo());
        subjectDataset.setDatasetNo(subjectDatasetRefSaveReq.getDatasetNo());
        analysisSubjectDatasetRefMapper.insert(subjectDataset);

        return subjectDataset.getId();
    }
}