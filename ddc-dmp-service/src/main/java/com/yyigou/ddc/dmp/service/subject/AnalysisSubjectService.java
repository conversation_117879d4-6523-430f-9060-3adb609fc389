package com.yyigou.ddc.dmp.service.subject;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.subject.entity.AnalysisSubject;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectDatasetRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.SubjectRes;

public interface AnalysisSubjectService extends IService<AnalysisSubject> {
    String saveSubject(SubjectSaveReq subjectSaveReq);

    SubjectRes getSubject(SubjectGetReq subjectGetReq);

    Long saveSubjectDatasetRef(SubjectDatasetRefSaveReq subjectDatasetRefSaveReq);
}