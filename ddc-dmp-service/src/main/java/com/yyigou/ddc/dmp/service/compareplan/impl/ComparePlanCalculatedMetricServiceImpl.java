package com.yyigou.ddc.dmp.service.compareplan.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.constant.DmpConstant;
import com.yyigou.ddc.dmp.common.enums.CompareModelMetricScopeTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlanCalMetric;
import com.yyigou.ddc.dmp.dao.compareplan.mapper.ComparePlanCalculatedMetricMapper;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanRefCalculatedMetricReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefCalculatedMetricRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefCalculatedMetricRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefMetricRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelCalMetricService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanCalculatedMetricService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanFieldService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Service
@Slf4j
public class ComparePlanCalculatedMetricServiceImpl extends DmpServiceImpl<ComparePlanCalculatedMetricMapper, ComparePlanCalMetric> implements ComparePlanCalculatedMetricService {



    @Resource
    private CompareModelCalMetricService compareModelCalMetricService;

    @Resource
    private ComparePlanFieldService comparePlanFieldService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePlanRefCalculatedMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan, Map<String, ComparePlanCalMetric> noToRecordMap) {
        if (noToRecordMap == null) {
            noToRecordMap = new HashMap<>();
        }

        List<ComparePlanCalMetric> addList = new ArrayList<>();
        List<ComparePlanCalMetric> updateList = new ArrayList<>();

        // 只处理selected为true的计算指标
        if (CollectionUtils.isNotEmpty(comparePlanSaveReq.getCalculatedMetricList())) {
            int sort = 1;
            for (ComparePlanRefCalculatedMetricReq calculatedMetricReq : comparePlanSaveReq.getCalculatedMetricList()) {
                // 只处理选中的计算指标
                if (calculatedMetricReq.getSelected() != null && calculatedMetricReq.getSelected()) {
                    ComparePlanCalMetric comparePlanCalMetric = BeanCopyUtil.copyFields(calculatedMetricReq, ComparePlanCalMetric.class);
                    comparePlanCalMetric.setEnterpriseNo(comparePlan.getEnterpriseNo());
                    comparePlanCalMetric.setComparePlanNo(comparePlan.getPlanNo());
                    comparePlanCalMetric.setSort(sort++);

                    // 处理更新或新增逻辑
                    if (StringUtils.isNotEmpty(calculatedMetricReq.getComparePlanCalMetricNo())
                            && noToRecordMap.containsKey(calculatedMetricReq.getComparePlanCalMetricNo())) {
                        // 更新场景
                        ComparePlanCalMetric existingCalMetric = noToRecordMap.remove(calculatedMetricReq.getComparePlanCalMetricNo());
                        comparePlanCalMetric.setId(existingCalMetric.getId());
                        comparePlanCalMetric.setComparePlanCalMetricNo(calculatedMetricReq.getComparePlanCalMetricNo());
                        updateList.add(comparePlanCalMetric);
                    } else {
                        // 新增场景
                        String newComparePlanCalMetricNo = IdUtil.objectId();
                        if (StrUtil.isNotEmpty(calculatedMetricReq.getComparePlanCalMetricNo())) {
                            // 报表配置可能引用了这里的编号,因此需要同时修改引用处
                            comparePlanFieldService.replaceFieldRefComparePlanCalMetricNo(comparePlanSaveReq, calculatedMetricReq.getComparePlanCalMetricNo(), newComparePlanCalMetricNo);
                        }
                        comparePlanCalMetric.setComparePlanCalMetricNo(newComparePlanCalMetricNo);
                        addList.add(comparePlanCalMetric);
                    }
                }
            }
        }

        // 批量保存新增和更新的记录
        this.saveBatch(addList);
        this.updateBatchById(updateList);

        // 处理需要删除的记录（在noToRecordMap中剩余的记录）
        if (!noToRecordMap.isEmpty()) {
            Collection<ComparePlanCalMetric> needDeleteList = noToRecordMap.values();
            for (ComparePlanCalMetric needDelete : needDeleteList) {
                needDelete.setDeleted(DeletedEnum.DELETED.getValue());
            }
            this.updateBatchById(needDeleteList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanRefCalculatedMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan) {
        List<ComparePlanCalMetric> dbModelMetricList = this.list(Wrappers.lambdaQuery(ComparePlanCalMetric.class)
                .eq(ComparePlanCalMetric::getComparePlanNo, comparePlan.getPlanNo())
                .eq(ComparePlanCalMetric::getEnterpriseNo, comparePlan.getEnterpriseNo())
                .eq(ComparePlanCalMetric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, ComparePlanCalMetric> existingMetricMap = dbModelMetricList.stream()
                .collect(Collectors.toMap(ComparePlanCalMetric::getComparePlanCalMetricNo, Function.identity()));

        this.savePlanRefCalculatedMetric(comparePlanSaveReq, comparePlan, existingMetricMap);
    }

    /**
     * 填充方案的计算指标列表，会将模型的逻辑计算指标物化成多个实际的计算指标
     * @param comparePlanGetRes
     * @param comparePlan
     */
    @Override
    public void fillCalculatedMetricList(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan) {
        List<ComparePlanCalMetric> selectedPlanMetricList = this.list(Wrappers.lambdaQuery(ComparePlanCalMetric.class)
                .eq(ComparePlanCalMetric::getComparePlanNo, comparePlan.getPlanNo())
                .eq(ComparePlanCalMetric::getEnterpriseNo, comparePlan.getEnterpriseNo())
                .eq(ComparePlanCalMetric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, ComparePlanCalMetric> selectedCalMetricNoSet = selectedPlanMetricList.stream()
                .collect(Collectors.toMap(e ->StrUtil.format(DmpConstant.COMPARE_CAL_METRIC_FORMAT, e.getCompareMetricNo(), e.getBaselineMetricNo(), e.getCompareModelCalMetricNo()), Function.identity()));

        List<ComparePlanRefCalculatedMetricRes> calculatedMetricList = new ArrayList<>();

        List<CompareModelRefCalculatedMetricRes> modelCalMetricResList = compareModelCalMetricService.getModelCalMetricResList(comparePlan.getEnterpriseNo(), comparePlan.getCompareModelNo());

        // 根据逻辑计算指标生成物理计算指标
        for (CompareModelRefCalculatedMetricRes modelRefCalculatedMetricRes : modelCalMetricResList) {
            // 当前计算指标可用的比对指标列表
            List<ComparePlanRefMetricRes> candidateCompareMetricList = new ArrayList<>();
            List<ComparePlanRefMetricRes> candidateBaselineMetricList = new ArrayList<>();
            CompareModelMetricScopeTypeEnum compareMetricScopeTypeEnum = CompareModelMetricScopeTypeEnum.getByValue(modelRefCalculatedMetricRes.getCompareMetricScopeType());
            // 复用前面拿到的比对指标列表和基准指标列表
            if (compareMetricScopeTypeEnum == CompareModelMetricScopeTypeEnum.ALL) {
                candidateCompareMetricList.addAll(comparePlanGetRes.getCompareMetricList());
            } else {
                comparePlanGetRes.getCompareMetricList().stream()
                        .filter(e -> modelRefCalculatedMetricRes.getCompareMetricScope().contains(e.getCompareModelMetricNo()))
                        .forEach(candidateCompareMetricList::add);
            }

            CompareModelMetricScopeTypeEnum baselineMetricScopeTypeEnum = CompareModelMetricScopeTypeEnum.getByValue(modelRefCalculatedMetricRes.getBaselineMetricScopeType());
            if (baselineMetricScopeTypeEnum == CompareModelMetricScopeTypeEnum.ALL) {
                candidateBaselineMetricList.addAll(comparePlanGetRes.getBaselineMetricList());
            } else {
                comparePlanGetRes.getBaselineMetricList().stream()
                        .filter(e -> modelRefCalculatedMetricRes.getBaselineMetricScope().contains(e.getCompareModelMetricNo()))
                        .forEach(candidateBaselineMetricList::add);
            }

            // 最后双层for循环生成实际的计算指标
            for (ComparePlanRefMetricRes candidateCompareMetric : candidateCompareMetricList) {
                for (ComparePlanRefMetricRes candidateBaselineMetric : candidateBaselineMetricList) {
                    ComparePlanRefCalculatedMetricRes comparePlanRefCalculatedMetricRes = BeanCopyUtil.copyFields(modelRefCalculatedMetricRes, ComparePlanRefCalculatedMetricRes.class);
                    comparePlanRefCalculatedMetricRes.setCompareMetricNo(candidateCompareMetric.getCompareModelMetricNo());
                    comparePlanRefCalculatedMetricRes.setBaselineMetricNo(candidateBaselineMetric.getCompareModelMetricNo());
                    comparePlanRefCalculatedMetricRes.setCalMetricName(StrUtil.format(DmpConstant.COMPARE_CAL_METRIC_FORMAT, candidateCompareMetric.getMetricName(), candidateBaselineMetric.getMetricName(), modelRefCalculatedMetricRes.getCalMetricName()));
                    comparePlanRefCalculatedMetricRes.setCompareModelCalMetricNo(modelRefCalculatedMetricRes.getCalMetricNo());
                    String uniqueNo = StrUtil.format(DmpConstant.COMPARE_CAL_METRIC_FORMAT, candidateCompareMetric.getCompareModelMetricNo(), candidateBaselineMetric.getCompareModelMetricNo(), modelRefCalculatedMetricRes.getCalMetricNo());
                    ComparePlanCalMetric existPlanCalMetric = selectedCalMetricNoSet.get(uniqueNo);
                    if (modelRefCalculatedMetricRes.getFormatConfig() != null) {
                        comparePlanRefCalculatedMetricRes.setUnitCode(modelRefCalculatedMetricRes.getFormatConfig().getUnitCode());
                    }
                    if (existPlanCalMetric == null) {
                        comparePlanRefCalculatedMetricRes.setSelected(false);
                        // 这里也生成一个唯一编号，用于标识这个指标在报表部分的配置
                        comparePlanRefCalculatedMetricRes.setComparePlanCalMetricNo(IdUtil.objectId());
                    } else {
                        comparePlanRefCalculatedMetricRes.setSelected(true);
                        comparePlanRefCalculatedMetricRes.setComparePlanCalMetricNo(existPlanCalMetric.getComparePlanCalMetricNo());
                    }
                    calculatedMetricList.add(comparePlanRefCalculatedMetricRes);
                }
            }
        }

        comparePlanGetRes.setCalculatedMetricList(calculatedMetricList);

    }
}
