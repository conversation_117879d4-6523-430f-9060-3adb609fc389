package com.yyigou.ddc.dmp.service.comparemodel.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.enums.ComparePlanFieldSourceTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.EnableStatusEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelCalMetric;
import com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelCalMetricMapper;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelRefCalculatedMetricReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanRefCalculatedMetricReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanRefFieldReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CalculatedMetricFormatConfigRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefCalculatedMetricRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelCalMetricService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Service
@Slf4j
public class CompareModelCalMetricServiceImpl extends DmpServiceImpl<CompareModelCalMetricMapper, CompareModelCalMetric> implements CompareModelCalMetricService {

    /**
     * 保存 计算指标
     * @param compareModelSaveReq
     * @param compareModel
     * @param existingCalMetricMap 已存在的计算指标映射，为null时表示新增场景
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveModelRefCalMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelCalMetric> existingCalMetricMap) {
        if (existingCalMetricMap == null) {
            existingCalMetricMap = new HashMap<>();
        }

        List<CompareModelCalMetric> saveList = new ArrayList<>();
        List<CompareModelCalMetric> updateList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(compareModelSaveReq.getCalculatedMetricList())) {
            List<CompareModelCalMetric> compareModelCalMetricList = new ArrayList<>();
            int sort = 1;
            for (CompareModelRefCalculatedMetricReq calculatedMetricReq : compareModelSaveReq.getCalculatedMetricList()) {
                CompareModelCalMetric compareModelCalMetric = BeanCopyUtil.copyFields(calculatedMetricReq, CompareModelCalMetric.class);
                compareModelCalMetric.setCompareModelNo(compareModel.getModelNo());
                compareModelCalMetric.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
                compareModelCalMetric.setSort(sort++);
                if (CollectionUtils.isNotEmpty(calculatedMetricReq.getBaselineMetricScope())) {
                    compareModelCalMetric.setBaselineMetricScope(JSONUtil.toJsonStr(calculatedMetricReq.getBaselineMetricScope()));
                } else {
                    compareModelCalMetric.setBaselineMetricScope("[]");
                }
                if (CollectionUtils.isNotEmpty(calculatedMetricReq.getCompareMetricScope())) {
                    compareModelCalMetric.setCompareMetricScope(JSONUtil.toJsonStr(calculatedMetricReq.getCompareMetricScope()));
                } else {
                    compareModelCalMetric.setBaselineMetricScope("[]");
                }
                compareModelCalMetric.setFormatConfig(JSONUtil.toJsonStr(calculatedMetricReq.getFormatConfig()));
                compareModelCalMetric.setEnableStatus(EnableStatusEnum.ENABLE.getValue());
                compareModelCalMetricList.add(compareModelCalMetric);
            }

            // 处理保存或更新逻辑
            for (CompareModelCalMetric calMetric : compareModelCalMetricList) {
                if (StringUtils.isNotEmpty(calMetric.getCalMetricNo()) && existingCalMetricMap.containsKey(calMetric.getCalMetricNo())) {
                    // 更新场景
                    CompareModelCalMetric existingCalMetric = existingCalMetricMap.remove(calMetric.getCalMetricNo());
                    calMetric.setId(existingCalMetric.getId());
                    updateList.add(calMetric);
                } else {
                    // 新增场景
                    calMetric.setCalMetricNo(IdUtil.objectId());
                    saveList.add(calMetric);
                }
            }
        }

        this.saveBatch(saveList);
        this.updateBatchByIdAndEnterpriseNo(updateList);

        // 处理需要删除的计算指标
        if (!existingCalMetricMap.isEmpty()) {
            Collection<CompareModelCalMetric> needDeleteCalMetrics = existingCalMetricMap.values();
            for (CompareModelCalMetric needDeleteCalMetric : needDeleteCalMetrics) {
                needDeleteCalMetric.setDeleted(DeletedEnum.DELETED.getValue());
            }
            this.updateBatchByIdAndEnterpriseNo(needDeleteCalMetrics);
        }
    }

    /**
     * 更新计算指标
     * @param compareModelSaveReq
     * @param compareModel
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateModelRefCalMetric(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel) {
        List<CompareModelCalMetric> dbModelCalMetricList = this.list(Wrappers.lambdaQuery(CompareModelCalMetric.class)
                .eq(CompareModelCalMetric::getCompareModelNo, compareModel.getModelNo())
                .eq(CompareModelCalMetric::getEnterpriseNo, compareModel.getEnterpriseNo())
                .eq(CompareModelCalMetric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, CompareModelCalMetric> existingCalMetricMap = dbModelCalMetricList.stream()
                .collect(Collectors.toMap(CompareModelCalMetric::getCalMetricNo, Function.identity()));

        this.saveModelRefCalMetric(compareModelSaveReq, compareModel, existingCalMetricMap);
    }


    /**
     * 填充计算指标信息
     * @param compareModelGetRes 返回结果对象
     * @param compareModel 主模型对象
     */
    @Override
    public void fillCalculatedMetricList(CompareModelGetRes compareModelGetRes, CompareModel compareModel) {
        List<CompareModelCalMetric> calMetricList = this.list(
                Wrappers.lambdaQuery(CompareModelCalMetric.class)
                        .eq(CompareModelCalMetric::getCompareModelNo, compareModel.getModelNo())
                        .eq(CompareModelCalMetric::getEnterpriseNo, compareModel.getEnterpriseNo())
                        .eq(CompareModelCalMetric::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .orderByAsc(CompareModelCalMetric::getSort)
        );

        if (CollectionUtils.isNotEmpty(calMetricList)) {
            List<CompareModelRefCalculatedMetricRes> calMetricResList = new ArrayList<>();
            for (CompareModelCalMetric calMetric : calMetricList) {
                CompareModelRefCalculatedMetricRes calMetricRes = convertCompareModelRefCalculatedMetricRes(compareModel.getModelNo(), calMetric);
                calMetricResList.add(calMetricRes);
            }
            compareModelGetRes.setCalculatedMetricList(calMetricResList);
        } else {
            compareModelGetRes.setCalculatedMetricList(ListUtil.empty());
        }
    }

    private static CompareModelRefCalculatedMetricRes convertCompareModelRefCalculatedMetricRes(String compareModelNo, CompareModelCalMetric calMetric) {
        CompareModelRefCalculatedMetricRes calMetricRes = BeanCopyUtil.copyFields(calMetric, CompareModelRefCalculatedMetricRes.class);

        // 处理格式化配置
        if (StringUtils.isNotEmpty(calMetric.getFormatConfig())) {
            try {
                CalculatedMetricFormatConfigRes formatConfigRes = JSONUtil.toBean(calMetric.getFormatConfig(), CalculatedMetricFormatConfigRes.class);
                calMetricRes.setFormatConfig(formatConfigRes);
            } catch (Exception e) {
                log.error("解析计算指标格式化配置失败，modelNo: {}, calMetricNo: {}", compareModelNo, calMetric.getCalMetricNo(),e);
                throw new RuntimeException("解析计算指标格式化配置失败");
            }
        }

        // 处理基准指标范围和比对指标范围
        if (StringUtils.isNotEmpty(calMetric.getBaselineMetricScope())) {
            try {
                List<String> baselineMetricScope = JSONUtil.toList(calMetric.getBaselineMetricScope(), String.class);
                calMetricRes.setBaselineMetricScope(baselineMetricScope);
            } catch (Exception e) {
                log.error("解析计算指标基准指标范围失败，modelNo: {}, calMetricNo: {}",
                        compareModelNo, calMetric.getCalMetricNo(), e);
                throw new RuntimeException("解析计算指标基准指标范围失败");
            }
        }

        if (StringUtils.isNotEmpty(calMetric.getCompareMetricScope())) {
            try {
                List<String> compareMetricScope = JSONUtil.toList(calMetric.getCompareMetricScope(), String.class);
                calMetricRes.setCompareMetricScope(compareMetricScope);
            } catch (Exception e) {
                log.warn("解析计算指标比对指标范围失败，modelNo: {}, calMetricNo: {}",
                        compareModelNo, calMetric.getCalMetricNo(), e);
                throw new RuntimeException("解析计算指标比对指标范围失败");
            }
        }
        return calMetricRes;
    }

    @Override
    public List<CompareModelRefCalculatedMetricRes> getModelCalMetricResList(String enterpriseNo, String compareModelNo) {
        List<CompareModelCalMetric> modelMetrics = this.lambdaQuery()
                .eq(CompareModelCalMetric::getCompareModelNo, compareModelNo)
                .eq(CompareModelCalMetric::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .orderByAsc(CompareModelCalMetric::getSort)
                .list();
        if (CollectionUtils.isEmpty(modelMetrics)) {
            return ListUtil.empty();
        }
        List<CompareModelRefCalculatedMetricRes> compareModelRefCalMetricResList = new ArrayList<>();
        for (CompareModelCalMetric modelMetric : modelMetrics) {
            CompareModelRefCalculatedMetricRes compareModelRefCalMetricRes = convertCompareModelRefCalculatedMetricRes(compareModelNo, modelMetric);
            compareModelRefCalMetricResList.add(compareModelRefCalMetricRes);
        }

        return compareModelRefCalMetricResList;
    }

    @Override
    public void checkModelCalMetricExist(ComparePlanSaveReq comparePlanSaveReq) {
        Map<String, CompareModelCalMetric> calMetricNoMap = this.lambdaQuery()
                .eq(CompareModelCalMetric::getCompareModelNo, comparePlanSaveReq.getCompareModelNo())
                .eq(CompareModelCalMetric::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .orderByAsc(CompareModelCalMetric::getSort)
                .list().stream().collect(Collectors.toMap(CompareModelCalMetric::getCalMetricNo, Function.identity()));

        Map<String , ComparePlanRefCalculatedMetricReq> validPlanCalMetricMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(comparePlanSaveReq.getCalculatedMetricList())) {
            for (ComparePlanRefCalculatedMetricReq comparePlanRefCalculatedMetricReq : comparePlanSaveReq.getCalculatedMetricList()) {
                if (Objects.equals(comparePlanRefCalculatedMetricReq.getSelected(), true)) {
                    CompareModelCalMetric compareModelCalMetric = calMetricNoMap.get(comparePlanRefCalculatedMetricReq.getCompareModelCalMetricNo());
                    if (compareModelCalMetric == null) {
                        throw new BusinessException("计算指标【"+comparePlanRefCalculatedMetricReq.getCompareModelCalMetricNo()+"】不存在");
                    }
                    validPlanCalMetricMap.put(comparePlanRefCalculatedMetricReq.getComparePlanCalMetricNo(), comparePlanRefCalculatedMetricReq);
                }
            }
        }

        checkPlanFieldRefCalMetric(comparePlanSaveReq.getValueList(), validPlanCalMetricMap);
        checkPlanFieldRefCalMetric(comparePlanSaveReq.getColumnList(), validPlanCalMetricMap);
        checkPlanFieldRefCalMetric(comparePlanSaveReq.getRowList(), validPlanCalMetricMap);


    }

    /**
     * 真正校验比价方案报表引用的指标字段是否启用
     *
     * @param fieldList
     * @param validPlanCalMetricMap
     */
    private void checkPlanFieldRefCalMetric(List<ComparePlanRefFieldReq> fieldList,  Map<String , ComparePlanRefCalculatedMetricReq> validPlanCalMetricMap) {
        if (CollectionUtils.isNotEmpty(fieldList)) {
            for (ComparePlanRefFieldReq field : fieldList) {
                if (Objects.equals(ComparePlanFieldSourceTypeEnum.CALCULATED_METRIC.getValue(), field.getSourceType())) {
                    ComparePlanRefCalculatedMetricReq planRefMetricReq = validPlanCalMetricMap.get(field.getComparePlanCalMetricNo());
                    if (planRefMetricReq == null) {
                        throw new BusinessException("方案计算指标编号【"+ field.getComparePlanMetricNo()+"】不存在或未启用");
                    }
                }
            }
        }

    }

}
