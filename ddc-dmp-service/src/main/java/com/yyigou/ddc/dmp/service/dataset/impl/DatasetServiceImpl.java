package com.yyigou.ddc.dmp.service.dataset.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetField;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetJoinRel;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetFieldMapper;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetJoinRelMapper;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetMapper;
import com.yyigou.ddc.dmp.dao.meta.mapper.MetaExtractor;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.req.dimension.DatasetFieldDimensionRefSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetFieldsDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetJoinRelDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.service.dimension.DimensionService;
import com.yyigou.ddc.dmp.service.subject.AnalysisSubjectService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.NestedExceptionUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class DatasetServiceImpl extends ServiceImpl<DatasetMapper, Dataset> implements DatasetService {
    @Resource
    private DatasetMapper datasetMapper;

    @Resource
    private DatasetJoinRelMapper datasetJoinRelMapper;

    @Resource
    private DatasetFieldMapper datasetFieldMapper;

    @Resource
    private AnalysisSubjectService analysisSubjectService;

    @Resource
    private DimensionService dimensionService;

    @Resource
    private MetaExtractor metaExtractor;

    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;

    @Override
    @Transactional
    public String saveDataset(DatasetSaveReq datasetSaveReq) {
        Dataset dataset = BeanCopyUtil.copyFields(datasetSaveReq, Dataset.class);
        if (StringUtils.isEmpty(dataset.getDatasetNo())) {
            //新增
            dataset.setDatasetNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<Dataset>lambdaQuery()
                    .eq(Dataset::getEnterpriseNo, dataset.getEnterpriseNo())
                    .eq(Dataset::getDatasetNo, dataset.getDatasetNo())
                    .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("数据集不存在");
            }

            Dataset toDeleteDataset = new Dataset();
            toDeleteDataset.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteDataset, Wrappers.<Dataset>lambdaQuery()
                    .eq(Dataset::getEnterpriseNo, dataset.getEnterpriseNo())
                    .eq(Dataset::getDatasetNo, dataset.getDatasetNo())
                    .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(dataset);
        SubjectDatasetRefSaveReq subjectDatasetRefSaveReq = new SubjectDatasetRefSaveReq();
        subjectDatasetRefSaveReq.setEnterpriseNo(dataset.getEnterpriseNo());
        subjectDatasetRefSaveReq.setSubjectNo(datasetSaveReq.getSubjectNo());
        subjectDatasetRefSaveReq.setDatasetNo(dataset.getDatasetNo());
        analysisSubjectService.saveSubjectDatasetRef(subjectDatasetRefSaveReq);

        List<DatasetJoinRel> datasetJoinRels = BeanCopyUtil.copyFieldsList(datasetSaveReq.getDatasetJoinRels(), DatasetJoinRel.class);
        replaceDatasetJoinRels(dataset, datasetJoinRels);

        List<DatasetField> datasetFields = BeanCopyUtil.copyFieldsList(datasetSaveReq.getDatasetFields(), DatasetField.class);
        replaceDatesetFields(dataset, datasetFields);

        return dataset.getDatasetNo();
    }

    @Override
    public Long refDimension(DatasetFieldRefDimensionSaveReq datasetFieldRefDimensionSaveReq) {
        DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq = new DatasetFieldDimensionRefSaveReq();
        datasetFieldDimensionRefSaveReq.setEnterpriseNo(datasetFieldRefDimensionSaveReq.getEnterpriseNo());
        datasetFieldDimensionRefSaveReq.setDatasetNo(datasetFieldRefDimensionSaveReq.getDatasetNo());
        datasetFieldDimensionRefSaveReq.setDatasetFieldNo(datasetFieldRefDimensionSaveReq.getDatasetFieldNo());
        datasetFieldDimensionRefSaveReq.setDimensionNo(datasetFieldRefDimensionSaveReq.getDimensionNo());
        datasetFieldDimensionRefSaveReq.setDescription(datasetFieldRefDimensionSaveReq.getDescription());

        return dimensionService.saveDatasetFieldDimensionRef(datasetFieldDimensionRefSaveReq);
    }

    @Override
    public List<DatasetDetailRes> queryDataset(DatasetQueryReq datasetQueryReq) {
        List<Dataset> datasets = datasetMapper.selectList(Wrappers.lambdaQuery(Dataset.class)
                .eq(Dataset::getEnterpriseNo, datasetQueryReq.getEnterpriseNo())
                .eq(datasetQueryReq.getStatus() != null, Dataset::getStatus, datasetQueryReq.getStatus())
                .notIn(CollectionUtils.isNotEmpty(datasetQueryReq.getExcludeDatasetNoList()), Dataset::getDatasetNo, datasetQueryReq.getExcludeDatasetNoList())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return BeanCopyUtil.copyFieldsList(datasets, DatasetDetailRes.class);
    }

    private void replaceDatasetJoinRels(Dataset dataset, List<DatasetJoinRel> datasetJoinRels) {
        datasetJoinRelMapper.delete(Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, dataset.getEnterpriseNo())
                .eq(DatasetJoinRel::getDatasetNo, dataset.getDatasetNo())
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        datasetJoinRelMapper.insert(datasetJoinRels);
    }

    private void replaceDatesetFields(Dataset dataset, List<DatasetField> datasetFields) {
        datasetFieldMapper.delete(Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, dataset.getEnterpriseNo())
                .eq(DatasetField::getDatasetNo, dataset.getDatasetNo())
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        datasetFieldMapper.insert(datasetFields);
    }

    @Override
    public DatasetDetailRes getDataset(DatasetGetReq datasetGetReq) {
        Dataset dataset = getOne(Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, datasetGetReq.getEnterpriseNo())
                .eq(Dataset::getDatasetNo, datasetGetReq.getDatasetNo())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataset) {
            throw new BusinessException("数据集不存在");
        }

        DatasetDetailRes datasetDetailRes = BeanCopyUtil.copyFields(dataset, DatasetDetailRes.class);

        List<DatasetJoinRel> datasetJoinRels = datasetJoinRelMapper.selectList(Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, datasetGetReq.getEnterpriseNo())
                .eq(DatasetJoinRel::getDatasetNo, datasetGetReq.getDatasetNo())
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if (CollectionUtils.isNotEmpty(datasetJoinRels)) {
            List<DatasetJoinRelDetailRes> datasetJoinRelDetailRes = BeanCopyUtil.copyFieldsList(datasetJoinRels, DatasetJoinRelDetailRes.class);
            datasetDetailRes.setDatasetJoinRels(datasetJoinRelDetailRes);
        }

        List<DatasetField> datasetFields = datasetFieldMapper.selectList(Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, datasetGetReq.getEnterpriseNo())
                .eq(DatasetField::getDatasetNo, datasetGetReq.getDatasetNo())
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if (CollectionUtils.isNotEmpty(datasetFields)) {
            List<DatasetFieldsDetailRes> datasetFieldsDetailRes = BeanCopyUtil.copyFieldsList(datasetFields, DatasetFieldsDetailRes.class);
            datasetDetailRes.setDatasetFields(datasetFieldsDetailRes);
        }

        return datasetDetailRes;
    }

    @Override
    public List<DatasetDetailRes> getDatasetList(DatasetGetListReq datasetGetListReq) {
        List<Dataset> datasetList = datasetMapper.selectList(Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, datasetGetListReq.getEnterpriseNo())
                .in(Dataset::getDatasetNo, datasetGetListReq.getDatasetNoList())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (CollectionUtils.isEmpty(datasetList)) {
            return Collections.emptyList();
        }

        Set<String> datasetNoSet = datasetList.stream().map(Dataset::getDatasetNo).collect(Collectors.toSet());

        List<DatasetJoinRel> datasetJoinRels = datasetJoinRelMapper.selectList(Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, datasetGetListReq.getEnterpriseNo())
                .in(DatasetJoinRel::getDatasetNo, datasetNoSet)
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<DatasetJoinRel>> datasetNoToJoinRelMap = datasetJoinRels.stream().collect(Collectors.groupingBy(DatasetJoinRel::getDatasetNo));

        List<DatasetField> datasetFields = datasetFieldMapper.selectList(Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, datasetGetListReq.getEnterpriseNo())
                .in(DatasetField::getDatasetNo, datasetNoSet)
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        Map<String, List<DatasetField>> datasetNoToFieldMap = datasetFields.stream().collect(Collectors.groupingBy(DatasetField::getDatasetNo));

        List<DatasetDetailRes> datasetDetailResList = BeanCopyUtil.copyFieldsList(datasetList, DatasetDetailRes.class);
        for (DatasetDetailRes datasetDetailRes : datasetDetailResList) {
            List<DatasetJoinRel> datasetJoinRelList = datasetNoToJoinRelMap.get(datasetDetailRes.getDatasetNo());
            if (CollectionUtils.isNotEmpty(datasetJoinRelList)) {
                List<DatasetJoinRelDetailRes> datasetJoinRelDetailRes = BeanCopyUtil.copyFieldsList(datasetJoinRelList, DatasetJoinRelDetailRes.class);
                datasetDetailRes.setDatasetJoinRels(datasetJoinRelDetailRes);
            }

            List<DatasetField> datasetFieldList = datasetNoToFieldMap.get(datasetDetailRes.getDatasetNo());
            if (CollectionUtils.isNotEmpty(datasetFieldList)) {
                List<DatasetFieldsDetailRes> datasetFieldsDetailRes = BeanCopyUtil.copyFieldsList(datasetFieldList, DatasetFieldsDetailRes.class);
                datasetDetailRes.setDatasetFields(datasetFieldsDetailRes);
            }
        }

        return datasetDetailResList;
    }

    @Override
    public DatasetPreviewRes previewData(DatasetPreviewReq datasetPreviewReq) {
        DatasetGetReq datasetGetReq = new DatasetGetReq();
        datasetGetReq.setEnterpriseNo(datasetPreviewReq.getEnterpriseNo());
        datasetGetReq.setDatasetNo(datasetPreviewReq.getDatasetNo());
        DatasetDetailRes dataset = getDataset(datasetGetReq);

        // 3. 构建预览SQL
        String previewSql = buildPreviewSql(dataset, 10);

        // 4. 执行查询 - 暂时使用简单的JDBC查询
        List<Map<String, Object>> maps = dorisJdbcTemplate.queryForList(previewSql);

        DatasetPreviewRes datasetPreviewRes = new DatasetPreviewRes();
        datasetPreviewRes.setEnterpriseNo(datasetPreviewReq.getEnterpriseNo());
        datasetPreviewRes.setDatasetNo(datasetPreviewReq.getDatasetNo());
        datasetPreviewRes.setData(maps);

        return datasetPreviewRes;
    }

    @Override
    public void validateDataset(DatasetSaveReq datasetSaveReq) {
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq, "数据集配置不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq.getDrivingSchemaName(), "驱动表schema不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq.getDrivingTableName(), "驱动表名称不能为空");
        if (CollectionUtils.isNotEmpty(datasetSaveReq.getDatasetJoinRels())) {
            for (DatasetJoinRelSaveReq datasetJoinRel : datasetSaveReq.getDatasetJoinRels()) {
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getTargetSchemaName(), "关联表schema不能为空");
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getTargetTableName(), "关联表名称不能为空");
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getJoinCondition(), "关联条件不能为空");
            }
        }

        DatasetDetailRes dataset = BeanCopyUtil.copyFieldsByJson(datasetSaveReq, DatasetDetailRes.class);

        String previewSql = buildPreviewSql(dataset, 0);

        try {
            metaExtractor.validateSQL(previewSql);
        } catch (BusinessException e) {
            Throwable rootCause = NestedExceptionUtils.getRootCause(e);
            if (rootCause == null) {
                rootCause = e;
            }

            String errorMessage = extractCleanErrorMessage(rootCause);

            throw new BusinessException(String.format("数据集拼接的SQL：%s；校验失败根因：%s", previewSql, errorMessage));
        }
    }

    private String extractCleanErrorMessage(Throwable root) {
        String message = root.getMessage();
        if (message == null) {
            return root.toString(); // fallback
        }

        // 如果 message 含有类似 "异常类名: 错误信息"，提取冒号后的部分
        int colonIndex = message.indexOf(": ");
        if (colonIndex != -1 && colonIndex + 2 < message.length()) {
            return message.substring(colonIndex + 2).trim();
        }

        // 否则直接返回 message
        return message.trim();
    }

    /**
     * 构建预览SQL
     */
    private String buildPreviewSql(DatasetDetailRes dataset, int previewLimit) {
        ValidatorUtil.checkEmptyThrowEx(dataset, "数据集不存在");
        ValidatorUtil.checkEmptyThrowEx(dataset.getDrivingSchemaName(), "驱动表schema不能为空");
        ValidatorUtil.checkEmptyThrowEx(dataset.getDrivingTableName(), "驱动表不能为空");


        StringBuilder sql = new StringBuilder();

        List<DatasetFieldsDetailRes> datasetFieldsDetailRes = dataset.getDatasetFields();
        if (CollectionUtils.isNotEmpty(datasetFieldsDetailRes)) {
            sql.append("SELECT ");
            for (int i = 0; i < datasetFieldsDetailRes.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                String fieldCatalogName = datasetFieldsDetailRes.get(i).getCatalogName();
                if (StringUtils.isNotEmpty(fieldCatalogName)) {
                    fieldCatalogName = fieldCatalogName + ".";
                } else {
                    fieldCatalogName = "";
                }
                sql.append(fieldCatalogName)
                        .append(datasetFieldsDetailRes.get(i).getSchemaName())
                        .append(".")
                        .append(datasetFieldsDetailRes.get(i).getTableName())
                        .append(".")
                        .append(datasetFieldsDetailRes.get(i).getFieldCode());
            }
        }

        // 基于驱动表构建基础查询
        String drivingCatalogName = dataset.getDrivingCatalogName();
        if (StringUtils.isNotEmpty(drivingCatalogName)) {
            drivingCatalogName = drivingCatalogName + ".";
        } else {
            drivingCatalogName = "";
        }
        sql.append(" FROM ")
                .append(drivingCatalogName)
                .append(dataset.getDrivingSchemaName())
                .append(".")
                .append(dataset.getDrivingTableName());

        List<DatasetJoinRelDetailRes> datasetJoinRelDetailRes = dataset.getDatasetJoinRels();
        if (CollectionUtils.isNotEmpty(datasetJoinRelDetailRes)) {
            for (DatasetJoinRelDetailRes joinRel : datasetJoinRelDetailRes) {
                String joinCatalogName = joinRel.getTargetCatalogName();
                if (StringUtils.isNotEmpty(joinCatalogName)) {
                    joinCatalogName = joinCatalogName + ".";
                } else {
                    joinCatalogName = "";
                }

                sql.append(" ").append(convertJoinType(joinRel.getJoinType())).append(" ")
                        .append(joinCatalogName)
                        .append(joinRel.getTargetSchemaName())
                        .append(".")
                        .append(joinRel.getTargetTableName())
                        .append(" ON ")
                        .append(joinRel.getJoinCondition());
            }
        }

        // 添加LIMIT子句
        if (previewLimit > 0) {
            sql.append(" LIMIT ").append(previewLimit);
        }

        return sql.toString();
    }

    /**
     * 转换JOIN类型
     */
    private String convertJoinType(String joinType) {
        if (StringUtils.isEmpty(joinType)) {
            return "INNER JOIN";
        }

        switch (joinType.toLowerCase()) {
            case "innerjoin":
                return "INNER JOIN";
            case "leftjoin":
                return "LEFT JOIN";
            case "rightjoin":
                return "RIGHT JOIN";
            default:
                return "INNER JOIN";
        }
    }
}
