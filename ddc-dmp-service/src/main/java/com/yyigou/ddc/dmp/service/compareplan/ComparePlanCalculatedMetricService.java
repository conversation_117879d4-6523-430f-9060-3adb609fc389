package com.yyigou.ddc.dmp.service.compareplan;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlanCalMetric;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
public interface ComparePlanCalculatedMetricService extends IService<ComparePlanCalMetric> {

    void savePlanRefCalculatedMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan, Map<String, ComparePlanCalMetric> noToRecordMap);

    void updatePlanRefCalculatedMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan);

    void fillCalculatedMetricList(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan);
}
