package com.yyigou.ddc.dmp.service.metric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.metric.entity.Metric;
import com.yyigou.ddc.dmp.model.req.metric.MetricGetReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricQueryByNoListReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricQueryReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricSaveReq;
import com.yyigou.ddc.dmp.model.res.metric.MetricQueryRes;

import java.util.List;

public interface MetricService extends IService<Metric> {
    String saveMetric(MetricSaveReq metricSaveReq);

    List<MetricQueryRes> queryMetric(MetricQueryReq metricQueryReq);

    List<MetricQueryRes> queryMetricByMetricNoList(MetricQueryByNoListReq metricQueryByNoListReq);

    MetricQueryRes getMetric(MetricGetReq metricGetReq);
}