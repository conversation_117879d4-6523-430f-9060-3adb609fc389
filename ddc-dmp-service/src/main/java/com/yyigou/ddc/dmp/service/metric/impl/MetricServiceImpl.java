package com.yyigou.ddc.dmp.service.metric.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetMapper;
import com.yyigou.ddc.dmp.dao.metric.entity.Metric;
import com.yyigou.ddc.dmp.dao.metric.mapper.MetricMapper;
import com.yyigou.ddc.dmp.model.req.metric.MetricGetReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricQueryByNoListReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricQueryReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricSaveReq;
import com.yyigou.ddc.dmp.model.res.metric.MetricQueryRes;
import com.yyigou.ddc.dmp.service.metric.MetricService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class MetricServiceImpl extends ServiceImpl<MetricMapper, Metric> implements MetricService {
    @Resource
    private DatasetMapper datasetMapper;

    @Resource
    private MetricMapper metricMapper;

    @Override
    @Transactional
    public String saveMetric(MetricSaveReq metricSaveReq) {
        Metric metric = BeanCopyUtil.copyFields(metricSaveReq, Metric.class);

        Dataset dataset = datasetMapper.selectOne(Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, metric.getEnterpriseNo())
                .eq(Dataset::getDatasetNo, metric.getDatasetNo())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataset) {
            throw new BusinessException("数据集不存在");
        }

        if (StringUtils.isEmpty(metric.getMetricNo())) {
            //新增
            metric.setMetricNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<Metric>lambdaQuery()
                    .eq(Metric::getEnterpriseNo, metric.getEnterpriseNo())
                    .eq(Metric::getMetricNo, metric.getMetricNo())
                    .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("指标不存在");
            }

            Metric toDeleteMetric = new Metric();
            toDeleteMetric.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteMetric, Wrappers.<Metric>lambdaQuery()
                    .eq(Metric::getEnterpriseNo, metric.getEnterpriseNo())
                    .eq(Metric::getMetricNo, metric.getMetricNo())
                    .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(metric);

        return metric.getMetricNo();
    }

    @Override
    public List<MetricQueryRes> queryMetric(MetricQueryReq metricQueryReq) {
        List<Metric> metrics = metricMapper.selectList(Wrappers.<Metric>lambdaQuery()
                .eq(Metric::getEnterpriseNo, metricQueryReq.getEnterpriseNo())
                .in(Metric::getDatasetNo, metricQueryReq.getDatasetNoList())
                .eq(metricQueryReq.getStatus() != null, Metric::getStatus, metricQueryReq.getStatus())
                .notIn(CollectionUtils.isNotEmpty(metricQueryReq.getExcludeMetricNoList()), Metric::getMetricNo, metricQueryReq.getExcludeMetricNoList())
                .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return BeanCopyUtil.copyFieldsList(metrics, MetricQueryRes.class);
    }

    @Override
    public List<MetricQueryRes> queryMetricByMetricNoList(MetricQueryByNoListReq metricQueryByNoListReq) {
        List<Metric> metrics = metricMapper.selectList(Wrappers.<Metric>lambdaQuery()
                .eq(Metric::getEnterpriseNo, metricQueryByNoListReq.getEnterpriseNo())
                .in(Metric::getMetricNo, metricQueryByNoListReq.getMetricNoList())
                .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        return BeanCopyUtil.copyFieldsList(metrics, MetricQueryRes.class);
    }

    @Override
    public MetricQueryRes getMetric(MetricGetReq metricGetReq) {
        Metric metric = metricMapper.selectOne(Wrappers.<Metric>lambdaQuery()
                .eq(Metric::getEnterpriseNo, metricGetReq.getEnterpriseNo())
                .eq(Metric::getMetricNo, metricGetReq.getMetricNo())
                .eq(Metric::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        return BeanCopyUtil.copyFields(metric, MetricQueryRes.class);
    }
}
