package com.yyigou.ddc.dmp.service.comparemodel.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.enums.CompareModelQueryConditionTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelQueryCondition;
import com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelQueryConditionMapper;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelRefBaselineMetricReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelRefQueryConditionReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefBaselineMetricRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefQueryConditionRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelQueryConditionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Service
public class CompareModelQueryConditionServiceImpl extends DmpServiceImpl<CompareModelQueryConditionMapper, CompareModelQueryCondition> implements CompareModelQueryConditionService {


    /**
     * 更新比对模型和查询条件的关系
     * 注意这里不是全部删除，然后重新插入一遍。因为比价方案会引用 CompareModelQueryCondition 的编号，因此需要处理更新、删除、插入3种场景
     *
     * @param compareModelSaveReq
     * @param compareModel
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateModelRefDatasetQueryCondition(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel) {
        List<CompareModelQueryCondition> dbModelQueryConditionList = this.list(Wrappers.lambdaQuery(CompareModelQueryCondition.class)
                .eq(CompareModelQueryCondition::getCompareModelNo, compareModel.getModelNo())
                .eq(CompareModelQueryCondition::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .eq(CompareModelQueryCondition::getEnterpriseNo, compareModel.getEnterpriseNo())
        );
        Map<String, CompareModelQueryCondition> noToDbModelQueryConditionMap = dbModelQueryConditionList.stream().collect(Collectors.toMap(CompareModelQueryCondition::getCompareModelQueryConditionNo, Function.identity()));
        this.saveModelRefDatasetQueryCondition(compareModelSaveReq, compareModel, noToDbModelQueryConditionMap);
    }


    /**
     * 保存模型关联数据集查询条件
     * @param compareModelSaveReq
     * @param compareModel
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveModelRefDatasetQueryCondition(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelQueryCondition> noToDbModelQueryConditionMap) {
        if (noToDbModelQueryConditionMap == null) {
            noToDbModelQueryConditionMap = new java.util.HashMap<>();
        }
        List<CompareModelQueryCondition> addModelQueryConditions = new ArrayList<>();
        List<CompareModelQueryCondition> updateModelQueryConditions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(compareModelSaveReq.getDatasetQueryConditionList())) {
            int sort = 1;
            for (CompareModelRefQueryConditionReq datasetQueryConditionReq : compareModelSaveReq.getDatasetQueryConditionList()) {
                CompareModelQueryCondition modelQueryCondition = BeanCopyUtil.copyFields(datasetQueryConditionReq, CompareModelQueryCondition.class);
                modelQueryCondition.setDatasetNo(compareModel.getCompareDatasetNo());
                modelQueryCondition.setCompareModelNo(compareModel.getModelNo());
                modelQueryCondition.setSort(sort++);
                modelQueryCondition.setCompareModelMetricNo("");
                modelQueryCondition.setConditionType(CompareModelQueryConditionTypeEnum.COMPARE_DATASET.getValue());
                modelQueryCondition.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
                determineSaveOrUpdateAction(noToDbModelQueryConditionMap, modelQueryCondition, updateModelQueryConditions, addModelQueryConditions);
            }
        }

        // todo-zyc 处理通用查询条件
        if (CollectionUtils.isNotEmpty(compareModelSaveReq.getBaselineMetricList())) {
            for (CompareModelRefBaselineMetricReq baselineMetricReq : compareModelSaveReq.getBaselineMetricList()) {
                if (CollectionUtils.isNotEmpty(baselineMetricReq.getDatasetQueryConditionList())) {
                    int sort = 1;
                    for (CompareModelRefQueryConditionReq datasetQueryConditionReq : baselineMetricReq.getDatasetQueryConditionList()) {
                        CompareModelQueryCondition modelQueryCondition = BeanCopyUtil.copyFields(datasetQueryConditionReq, CompareModelQueryCondition.class);
                        modelQueryCondition.setDatasetNo(baselineMetricReq.getDatasetNo());
                        modelQueryCondition.setCompareModelNo(compareModel.getModelNo());
                        modelQueryCondition.setCompareModelMetricNo(baselineMetricReq.getCompareModelMetricNo());
                        modelQueryCondition.setSort(sort++);
                        modelQueryCondition.setConditionType(CompareModelQueryConditionTypeEnum.BASELINE_METRIC.getValue());
                        modelQueryCondition.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
                        modelQueryCondition.setCompareModelMetricNo(baselineMetricReq.getCompareModelMetricNo());
                        determineSaveOrUpdateAction(noToDbModelQueryConditionMap, modelQueryCondition, updateModelQueryConditions, addModelQueryConditions);
                    }
                }
            }
        }

        this.saveBatch(addModelQueryConditions);
        this.updateBatchByIdAndEnterpriseNo(updateModelQueryConditions);

        Collection<CompareModelQueryCondition> needDeleteModelQueryConditions = noToDbModelQueryConditionMap.values();
        for (CompareModelQueryCondition needDeleteModelQueryCondition : needDeleteModelQueryConditions) {
            needDeleteModelQueryCondition.setDeleted(DeletedEnum.DELETED.getValue());
        }
        this.updateBatchByIdAndEnterpriseNo(needDeleteModelQueryConditions);

    }

    /**
     * 根据条件编号确定查询条件的保存或更新操作
     * @param existingConditionMap 数据库中已存在的条件映射
     * @param condition 当前处理的查询条件
     * @param updateList 需要更新的条件列表
     * @param saveList 需要保存的条件列表
     */
    private static void determineSaveOrUpdateAction(Map<String, CompareModelQueryCondition> existingConditionMap,
                                                    CompareModelQueryCondition condition,
                                                    List<CompareModelQueryCondition> updateList,
                                                    List<CompareModelQueryCondition> saveList) {
        if (StrUtil.isNotEmpty(condition.getCompareModelQueryConditionNo())
                && existingConditionMap.containsKey(condition.getCompareModelQueryConditionNo())) {
            // 传了no并且no在数据库中还存在，才更新，否则就是插入
            CompareModelQueryCondition existingCondition = existingConditionMap.remove(condition.getCompareModelQueryConditionNo());
            condition.setId(existingCondition.getId());
            updateList.add(condition);
        } else {
            condition.setCompareModelQueryConditionNo(IdUtil.objectId());
            saveList.add(condition);
        }
    }


    /**
     * 填充查询条件信息
     * @param compareModelGetRes 返回结果对象
     * @param compareModel 主模型对象
     */
    @Override
    public void fillQueryConditionList(CompareModelGetRes compareModelGetRes, CompareModel compareModel) {
        List<CompareModelQueryCondition> queryConditionList = this.list(
                Wrappers.lambdaQuery(CompareModelQueryCondition.class)
                        .eq(CompareModelQueryCondition::getCompareModelNo, compareModel.getModelNo())
                        // 主数据集查询条件
                        .eq(CompareModelQueryCondition::getConditionType, CompareModelQueryConditionTypeEnum.COMPARE_DATASET.getValue())
                        .eq(CompareModelQueryCondition::getEnterpriseNo, compareModel.getEnterpriseNo())
                        .eq(CompareModelQueryCondition::getDeleted, DeletedEnum.UN_DELETE.getValue())
                        .orderByAsc(CompareModelQueryCondition::getSort)
        );

        if (CollectionUtils.isNotEmpty(queryConditionList)) {
            List<CompareModelRefQueryConditionRes> queryConditionResList = BeanCopyUtil.copyFieldsList(queryConditionList, CompareModelRefQueryConditionRes.class);
            compareModelGetRes.setDatasetQueryConditionList(queryConditionResList);
        }
    }

    /**
     * 填充基准指标查询条件信息
     * @param compareModelGetRes 返回结果对象
     * @param compareModel 主模型对象
     */
    @Override
    public void fillBaselineMetricQueryConditionList(CompareModelGetRes compareModelGetRes, CompareModel compareModel) {
        // 填充基准指标的查询条件到对应的基准指标中
        if (CollectionUtils.isNotEmpty(compareModelGetRes.getBaselineMetricList())) {
            // 收集所有基准指标的编号
            List<String> baselineMetricNos = compareModelGetRes.getBaselineMetricList().stream()
                    .map(CompareModelRefBaselineMetricRes::getCompareModelMetricNo)
                    .filter(StrUtil::isNotEmpty)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(baselineMetricNos)) {
                // 一次性查询所有基准指标的查询条件
                List<CompareModelQueryCondition> allQueryConditions = this.list(
                        Wrappers.lambdaQuery(CompareModelQueryCondition.class)
                                .eq(CompareModelQueryCondition::getCompareModelNo, compareModel.getModelNo())
                                .in(CompareModelQueryCondition::getCompareModelMetricNo, baselineMetricNos)
                                .eq(CompareModelQueryCondition::getEnterpriseNo, compareModel.getEnterpriseNo())
                                .eq(CompareModelQueryCondition::getDeleted, DeletedEnum.UN_DELETE.getValue())
                                .orderByAsc(CompareModelQueryCondition::getSort)
                );

                // 按基准指标编号分组查询条件
                Map<String, List<CompareModelQueryCondition>> metricNoToQueryConditionsMap = allQueryConditions.stream()
                        .collect(Collectors.groupingBy(CompareModelQueryCondition::getCompareModelMetricNo));

                // 为每个基准指标填充查询条件
                for (CompareModelRefBaselineMetricRes baselineMetricRes : compareModelGetRes.getBaselineMetricList()) {
                    String metricNo = baselineMetricRes.getCompareModelMetricNo();
                    if (StrUtil.isNotEmpty(metricNo) && metricNoToQueryConditionsMap.containsKey(metricNo)) {
                        List<CompareModelQueryCondition> queryConditionList = metricNoToQueryConditionsMap.get(metricNo);
                        List<CompareModelRefQueryConditionRes> queryConditionResList = BeanCopyUtil.copyFieldsList(queryConditionList, CompareModelRefQueryConditionRes.class);
                        baselineMetricRes.setDatasetQueryConditionList(queryConditionResList);
                    }
                }
            }
        }
    }

}
