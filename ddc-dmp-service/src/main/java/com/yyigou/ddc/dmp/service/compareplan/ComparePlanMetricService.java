package com.yyigou.ddc.dmp.service.compareplan;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlanMetric;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
public interface ComparePlanMetricService extends IService<ComparePlanMetric> {



    void savePlanRefMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan, Map<String, ComparePlanMetric> noToRecordMap);

    void updatePlanRefMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan);

    void fillMetricList(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan);
}
