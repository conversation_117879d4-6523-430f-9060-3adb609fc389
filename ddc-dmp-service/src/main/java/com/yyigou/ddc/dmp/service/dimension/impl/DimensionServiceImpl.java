package com.yyigou.ddc.dmp.service.dimension.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetFieldDimensionRef;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetFieldDimensionRefMapper;
import com.yyigou.ddc.dmp.dao.dimension.entity.Dimension;
import com.yyigou.ddc.dmp.dao.dimension.entity.DimensionField;
import com.yyigou.ddc.dmp.dao.dimension.mapper.DimensionFieldMapper;
import com.yyigou.ddc.dmp.dao.dimension.mapper.DimensionMapper;
import com.yyigou.ddc.dmp.dao.meta.entity.Tables;
import com.yyigou.ddc.dmp.dao.meta.mapper.MetaLoader;
import com.yyigou.ddc.dmp.model.req.dimension.DatasetFieldDimensionRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionFieldSaveReq;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionSaveReq;
import com.yyigou.ddc.dmp.service.dimension.DimensionService;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class DimensionServiceImpl extends ServiceImpl<DimensionMapper, Dimension> implements DimensionService {
    @Resource
    private DimensionMapper dimensionMapper;

    @Resource
    private DimensionFieldMapper dimensionFieldMapper;

    @Resource
    private DatasetFieldDimensionRefMapper datasetFieldDimensionRefMapper;

    @Resource
    private MetaLoader metaLoader;

    @Override
    @Transactional
    public String saveDimension(DimensionSaveReq dimensionSaveReq) {
        Tables table = metaLoader.getTable(dimensionSaveReq.getSchemaName(), dimensionSaveReq.getTableName());
        if (null == table) {
            throw new BusinessException("维度表不存在");
        }

        Dimension dimension = BeanCopyUtil.copyFields(dimensionSaveReq, Dimension.class);

        List<DimensionFieldSaveReq> dimensionFieldsReq = dimensionSaveReq.getDimensionFields();
        if (CollectionUtils.isEmpty(dimensionFieldsReq)) {
            throw new BusinessException("维度字段不能为空");
        }

        List<DimensionField> dimensionFields = BeanCopyUtil.copyFieldsList(dimensionFieldsReq, DimensionField.class);

        if (StringUtils.isEmpty(dimension.getDimensionNo())) {
            //新增
            dimension.setDimensionNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<Dimension>lambdaQuery()
                    .eq(Dimension::getEnterpriseNo, dimension.getEnterpriseNo())
                    .eq(Dimension::getDimensionNo, dimension.getDimensionNo())
                    .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("维度不存在");
            }

            Dimension toDeleteDimension = new Dimension();
            toDeleteDimension.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteDimension, Wrappers.<Dimension>lambdaQuery()
                    .eq(Dimension::getEnterpriseNo, dimension.getEnterpriseNo())
                    .eq(Dimension::getDimensionNo, dimension.getDimensionNo())
                    .eq(Dimension::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(dimension);

        DimensionField toDeleteDimensionField = new DimensionField();
        toDeleteDimensionField.setDeleted(DeletedEnum.DELETED.getValue());
        dimensionFieldMapper.update(toDeleteDimensionField, Wrappers.<DimensionField>lambdaQuery()
                .eq(DimensionField::getEnterpriseNo, dimension.getEnterpriseNo())
                .eq(DimensionField::getDimensionNo, dimension.getDimensionNo())
                .eq(DimensionField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        dimensionFieldMapper.insert(dimensionFields);

        return dimension.getDimensionNo();
    }

    @Override
    public Long saveDatasetFieldDimensionRef(DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq) {
        datasetFieldDimensionRefMapper.update(null, Wrappers.<DatasetFieldDimensionRef>lambdaUpdate()
                .set(DatasetFieldDimensionRef::getDeleted, DeletedEnum.DELETED.getValue())
                .eq(DatasetFieldDimensionRef::getEnterpriseNo, datasetFieldDimensionRefSaveReq.getEnterpriseNo())
                .eq(DatasetFieldDimensionRef::getDatasetNo, datasetFieldDimensionRefSaveReq.getDatasetNo())
                .eq(DatasetFieldDimensionRef::getDatasetFieldNo, datasetFieldDimensionRefSaveReq.getDatasetFieldNo())
                .eq(DatasetFieldDimensionRef::getDimensionNo, datasetFieldDimensionRefSaveReq.getDimensionNo())
                .eq(DatasetFieldDimensionRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        DatasetFieldDimensionRef datasetFieldDimensionRef = new DatasetFieldDimensionRef();
        datasetFieldDimensionRef.setEnterpriseNo(datasetFieldDimensionRefSaveReq.getEnterpriseNo());
        datasetFieldDimensionRef.setDatasetNo(datasetFieldDimensionRefSaveReq.getDatasetNo());
        datasetFieldDimensionRef.setDatasetFieldNo(datasetFieldDimensionRefSaveReq.getDatasetFieldNo());
        datasetFieldDimensionRef.setDimensionNo(datasetFieldDimensionRefSaveReq.getDimensionNo());
        datasetFieldDimensionRef.setDescription(datasetFieldDimensionRefSaveReq.getDescription());

        datasetFieldDimensionRefMapper.insert(datasetFieldDimensionRef);

        return datasetFieldDimensionRef.getId();
    }
}
