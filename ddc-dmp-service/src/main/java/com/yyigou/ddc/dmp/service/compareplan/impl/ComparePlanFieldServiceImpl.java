package com.yyigou.ddc.dmp.service.compareplan.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.enums.CompareModelDimTypeEnum;
import com.yyigou.ddc.dmp.common.enums.ComparePlanFieldSourceTypeEnum;
import com.yyigou.ddc.dmp.common.enums.ComparePlanFieldTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlanField;
import com.yyigou.ddc.dmp.dao.compareplan.mapper.ComparePlanFieldMapper;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanRefFieldReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefFieldRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefCalculatedMetricRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefFieldRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefMetricRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelDimService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanFieldService;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Service
@Slf4j
public class ComparePlanFieldServiceImpl extends DmpServiceImpl<ComparePlanFieldMapper, ComparePlanField> implements ComparePlanFieldService {


    @Resource
    private CompareModelDimService compareModelDimService;


    /**
     * 保存比价方案配置报表字段（行/列/值）
     * 极限场景：两个用户同时操作一个方案，可能前一个用户删除了某个已有维度，后一个用户还保留，导致no又插入一遍。这时会现场编号重复，因此表结构不要用唯一索引。
     * @param comparePlanSaveReq
     * @param comparePlan
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePlanRefField(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan) {
        List<ComparePlanField> addList = new ArrayList<>();

        processFieldReqList(comparePlanSaveReq.getRowList(), addList, comparePlan, ComparePlanFieldTypeEnum.ROW);
        processFieldReqList(comparePlanSaveReq.getColumnList(), addList, comparePlan, ComparePlanFieldTypeEnum.COLUMN);
        processFieldReqList(comparePlanSaveReq.getValueList(), addList, comparePlan, ComparePlanFieldTypeEnum.VALUE);

        this.saveBatch(addList);
    }

    /**
     * 直接删除之前的配置
     * @param comparePlanSaveReq
     * @param comparePlan
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanRefField(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan) {
        this.update(null, Wrappers.lambdaUpdate(ComparePlanField.class)
                .eq(ComparePlanField::getEnterpriseNo, comparePlan.getEnterpriseNo())
                .eq(ComparePlanField::getComparePlanNo, comparePlan.getPlanNo())
                .eq(ComparePlanField::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .set(ComparePlanField::getDeleted, DeletedEnum.DELETED.getValue())
                .set(ComparePlanField::getModifyTime, DateUtil.now())
                .set(ComparePlanField::getModifyNo, UserHandleUtils.getOperationModel().getEmployerNo())
                .set(ComparePlanField::getModifyName, UserHandleUtils.getOperationModel().getUserName())
        );

        this.savePlanRefField(comparePlanSaveReq, comparePlan);

    }



    private void processFieldReqList(List<ComparePlanRefFieldReq> fieldReqList, List<ComparePlanField> addList,  ComparePlan comparePlan, ComparePlanFieldTypeEnum comparePlanFieldTypeEnum) {
        int sort = 1;
        for (ComparePlanRefFieldReq comparePlanRefFieldReq : fieldReqList) {
            ComparePlanField comparePlanField = BeanCopyUtil.copyFields(comparePlanRefFieldReq, ComparePlanField.class);
            comparePlanField.setEnterpriseNo(comparePlan.getEnterpriseNo());
            comparePlanField.setComparePlanNo(comparePlan.getPlanNo());
            comparePlanField.setFieldType(comparePlanFieldTypeEnum.getValue());
            ComparePlanFieldSourceTypeEnum sourceTypeEnum = ComparePlanFieldSourceTypeEnum.getByValue(comparePlanRefFieldReq.getSourceType());
            if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.COMPARE_DIM || sourceTypeEnum == ComparePlanFieldSourceTypeEnum.COMPARE_OBJECT) {
                comparePlanField.setSourceNo(comparePlanRefFieldReq.getCompareModelDimNo());
            } else if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.COMPARE_METRIC || sourceTypeEnum == ComparePlanFieldSourceTypeEnum.BASELINE_METRIC) {
                comparePlanField.setSourceNo(comparePlanRefFieldReq.getComparePlanMetricNo());
            } else if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.CALCULATED_METRIC) {
                comparePlanField.setSourceNo(comparePlanRefFieldReq.getComparePlanCalMetricNo());
            } else {
                throw new RuntimeException("未知字段类型");
            }
            comparePlanField.setSort(sort++);
            addList.add(comparePlanField);
        }
    }


    /**
     * 填充报表配置
     * 1. 填充比对对象字段和比对维度字段
     * 2. 回显报表配置，前提：配置的源数据还存在
     * @param comparePlanGetRes
     * @param comparePlan
     */
    @Override
    public void fillFieldList(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan) {
        List<ComparePlanField> selectedPlanFieldList = this.list(Wrappers.lambdaQuery(ComparePlanField.class)
                .eq(ComparePlanField::getComparePlanNo, comparePlan.getPlanNo())
                .eq(ComparePlanField::getEnterpriseNo, comparePlan.getEnterpriseNo())
                .eq(ComparePlanField::getDeleted, DeletedEnum.UN_DELETE.getValue())
                .orderByAsc(ComparePlanField::getSort)
        );

        // 填充比对模型的比对对象和比对维度，可用于配置报表
        List<CompareModelRefFieldRes> modelDimList = compareModelDimService.getModelDimList(comparePlan.getEnterpriseNo(), comparePlan.getCompareModelNo());
        comparePlanGetRes.setCompareObjectList(modelDimList.stream()
                .filter(dim -> CompareModelDimTypeEnum.COMPARE_OBJECT.getValue().equals(dim.getDimType()))
                .collect(Collectors.toList()));
        comparePlanGetRes.setCompareDimList(modelDimList.stream()
                .filter(dim -> CompareModelDimTypeEnum.COMPARE_DIM.getValue().equals(dim.getDimType()))
                .collect(Collectors.toList()));

        List<ComparePlanRefFieldRes> rowList = new ArrayList<>();
        List<ComparePlanRefFieldRes> columnList = new ArrayList<>();
        List<ComparePlanRefFieldRes> valueList = new ArrayList<>();

        Map<String, CompareModelRefFieldRes> dimNoToCompareObjectMap = comparePlanGetRes.getCompareObjectList().stream().collect(Collectors.toMap(CompareModelRefFieldRes::getCompareModelDimNo, Function.identity()));
        Map<String, CompareModelRefFieldRes> dimNoToCompareDimMap = comparePlanGetRes.getCompareDimList().stream().collect(Collectors.toMap(CompareModelRefFieldRes::getCompareModelDimNo, Function.identity()));
        Map<String, ComparePlanRefMetricRes> compareMetricNoMap = comparePlanGetRes.getCompareMetricList().stream()
                .filter(e -> Objects.equals(e.getSelected(), true))
                .collect(Collectors.toMap(ComparePlanRefMetricRes::getComparePlanMetricNo, Function.identity()));
        Map<String, ComparePlanRefMetricRes> baselineMetricNoMap = comparePlanGetRes.getBaselineMetricList().stream()
                .filter(e -> Objects.equals(e.getSelected(), true))
                .collect(Collectors.toMap(ComparePlanRefMetricRes::getComparePlanMetricNo, Function.identity()));
        Map<String, ComparePlanRefCalculatedMetricRes> calcualtedMetricNoMap = comparePlanGetRes.getCalculatedMetricList().stream()
                .filter(e -> Objects.equals(e.getSelected(), true))
                .collect(Collectors.toMap(ComparePlanRefCalculatedMetricRes::getComparePlanCalMetricNo, Function.identity()));


        // 根据数据库中的已有报表配置回显。但加了一个校验，配置的指标或字段仍然存在
        for (ComparePlanField comparePlanField : selectedPlanFieldList) {
            boolean needAdd = false;
            ComparePlanRefFieldRes comparePlanRefFieldRes = BeanCopyUtil.copyFields(comparePlanField, ComparePlanRefFieldRes.class);
            ComparePlanFieldSourceTypeEnum sourceTypeEnum = ComparePlanFieldSourceTypeEnum.getByValue(comparePlanField.getSourceType());
            if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.COMPARE_OBJECT) {
                CompareModelRefFieldRes compareModelRefFieldRes = dimNoToCompareObjectMap.get(comparePlanField.getSourceNo());
                if (compareModelRefFieldRes != null) {
                    comparePlanRefFieldRes.setCompareModelDimNo(compareModelRefFieldRes.getCompareModelDimNo());
                    needAdd = true;
                }
            } else if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.COMPARE_DIM) {
                CompareModelRefFieldRes compareModelRefFieldRes = dimNoToCompareDimMap.get(comparePlanField.getSourceNo());
                if (compareModelRefFieldRes != null) {
                    comparePlanRefFieldRes.setCompareModelDimNo(compareModelRefFieldRes.getCompareModelDimNo());
                    needAdd = true;
                }
            } else if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.COMPARE_METRIC) {
                ComparePlanRefMetricRes comparePlanRefMetricRes = compareMetricNoMap.get(comparePlanField.getSourceNo());
                if (comparePlanRefMetricRes != null) {
                    comparePlanRefFieldRes.setComparePlanMetricNo(comparePlanRefMetricRes.getComparePlanMetricNo());
                    needAdd = true;
                }
            } else if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.BASELINE_METRIC) {
                ComparePlanRefMetricRes comparePlanRefMetricRes = baselineMetricNoMap.get(comparePlanField.getSourceNo());
                if (comparePlanRefMetricRes != null) {
                    comparePlanRefFieldRes.setComparePlanMetricNo(comparePlanRefMetricRes.getComparePlanMetricNo());
                    needAdd = true;
                }
            } else if (sourceTypeEnum == ComparePlanFieldSourceTypeEnum.CALCULATED_METRIC) {
                ComparePlanRefCalculatedMetricRes calculatedMetricRes = calcualtedMetricNoMap.get(comparePlanField.getSourceNo());
                if (calculatedMetricRes != null) {
                    comparePlanRefFieldRes.setComparePlanCalMetricNo(calculatedMetricRes.getComparePlanCalMetricNo());
                    needAdd = true;
                }
            }

            if (needAdd) {
                if (Objects.equals(comparePlanField.getFieldType(), ComparePlanFieldTypeEnum.ROW.getValue())) {
                    rowList.add(comparePlanRefFieldRes);
                } else if (Objects.equals(comparePlanField.getFieldType(), ComparePlanFieldTypeEnum.COLUMN.getValue())) {
                    columnList.add(comparePlanRefFieldRes);
                } else if (Objects.equals(comparePlanField.getFieldType(), ComparePlanFieldTypeEnum.VALUE.getValue())) {
                    valueList.add(comparePlanRefFieldRes);
                }
            }

        }

        comparePlanGetRes.setRowList(rowList);
        comparePlanGetRes.setColumnList(columnList);
        comparePlanGetRes.setValueList(valueList);
    }


    /**
     * 当两个用户同时修改报表配置时，可能出现 comparePlanMetricNo 先被删除了，后一个请求又保存的场景
     * 这时会生成一个新的comparePlanMetricNo，因此要将引用部分一并修改掉
     *
     */
    @Override
    public void replaceFieldRefComparePlanMetricNo(ComparePlanSaveReq comparePlanSaveReq, String oldComparePlanMetricNo, String newComparePlanMetricNo) {
        for (ComparePlanRefFieldReq comparePlanRefFieldReq : comparePlanSaveReq.getRowList()) {
            if (Objects.equals(comparePlanRefFieldReq.getComparePlanMetricNo(), oldComparePlanMetricNo)) {
                comparePlanRefFieldReq.setComparePlanMetricNo(newComparePlanMetricNo);
            }
        }
        for (ComparePlanRefFieldReq comparePlanRefFieldReq : comparePlanSaveReq.getColumnList()) {
            if (Objects.equals(comparePlanRefFieldReq.getComparePlanMetricNo(), oldComparePlanMetricNo)) {
                comparePlanRefFieldReq.setComparePlanMetricNo(newComparePlanMetricNo);
            }
        }
        for (ComparePlanRefFieldReq comparePlanRefFieldReq : comparePlanSaveReq.getValueList()) {
            if (Objects.equals(comparePlanRefFieldReq.getComparePlanMetricNo(), oldComparePlanMetricNo)) {
                comparePlanRefFieldReq.setComparePlanMetricNo(newComparePlanMetricNo);
            }
        }
    }

    @Override
    public void replaceFieldRefComparePlanCalMetricNo(ComparePlanSaveReq comparePlanSaveReq, String oldComparePlanCalMetricNo, String newComparePlanCalMetricNo) {
        for (ComparePlanRefFieldReq comparePlanRefFieldReq : comparePlanSaveReq.getRowList()) {
            if (Objects.equals(comparePlanRefFieldReq.getComparePlanCalMetricNo(), oldComparePlanCalMetricNo)) {
                comparePlanRefFieldReq.setComparePlanMetricNo(newComparePlanCalMetricNo);
            }
        }
        for (ComparePlanRefFieldReq comparePlanRefFieldReq : comparePlanSaveReq.getColumnList()) {
            if (Objects.equals(comparePlanRefFieldReq.getComparePlanCalMetricNo(), oldComparePlanCalMetricNo)) {
                comparePlanRefFieldReq.setComparePlanMetricNo(newComparePlanCalMetricNo);
            }
        }
        for (ComparePlanRefFieldReq comparePlanRefFieldReq : comparePlanSaveReq.getValueList()) {
            if (Objects.equals(comparePlanRefFieldReq.getComparePlanCalMetricNo(), oldComparePlanCalMetricNo)) {
                comparePlanRefFieldReq.setComparePlanMetricNo(newComparePlanCalMetricNo);
            }
        }
    }
}
