package com.yyigou.ddc.dmp.service.compareplan;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanChangeStatusReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanGetReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanPageQueryReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanPageRes;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
public interface ComparePlanService extends IService<ComparePlan> {

    ComparePlanPageRes save(ComparePlanSaveReq comparePlanSaveReq);

    Boolean update(ComparePlanSaveReq comparePlanSaveReq);

    Boolean delete(ComparePlanGetReq deleteReq);

    Boolean changeStatus(ComparePlanChangeStatusReq changeStatusReq);

    ComparePlanGetRes get(ComparePlanGetReq getReq);

    PageVo<ComparePlanPageRes> pageQuery(ComparePlanPageQueryReq pageReq);
}
