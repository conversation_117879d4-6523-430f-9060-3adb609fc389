package com.yyigou.ddc.dmp.service.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.dao.mapper.DmpBaseMapper;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
public class DmpServiceImpl<M extends DmpBaseMapper<T>, T> extends ServiceImpl<M, T> {

    /**
     * 用id和enterpriseNo 批量更新
     * @param list
     */
    public void updateBatchByIdAndEnterpriseNo(Collection<T> list) {
        if (CollectionUtil.isEmpty( list )) {
            return;
        }
        getBaseMapper().updateBatchByIdAndEnterpriseNo(ListUtil.toList(list), UserHandleUtils.getOperationModel().getTenantNo(), getMapperClass());
    }

}
