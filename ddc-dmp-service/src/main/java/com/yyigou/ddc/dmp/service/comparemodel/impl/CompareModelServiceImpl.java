package com.yyigou.ddc.dmp.service.comparemodel.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.constant.DmpConstant;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.EnableStatusEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelMapper;
import com.yyigou.ddc.dmp.manager.integration.log.BusinessLogService;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelChangeStatusReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelGetReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelPageQueryReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelPageRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.*;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Service
public class CompareModelServiceImpl extends DmpServiceImpl<CompareModelMapper, CompareModel> implements CompareModelService {

    /**
     * 平台租户编号
     */
    @Value("${dmp.platformEnterpriseNo:2000000}")
    private String platformEnterpriseNo;

    @Resource
    private CompareModelMapper compareModelMapper;

    @Resource
    private CompareModelDatasetService compareModelDatasetService;

    @Resource
    private CompareModelDimService compareModelDimService;

    @Resource
    private CompareModelMetricService compareModelMetricService;

    @Resource
    private CompareModelCalMetricService compareModelCalMetricService;

    @Resource
    private CompareModelQueryConditionService compareModelQueryConditionService;

    @Resource
    private BusinessLogService businessLogService;


    /**
     * 新增比对模型
     *
     * @param compareModelSaveReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompareModelPageRes save(CompareModelSaveReq compareModelSaveReq) {
        validateSaveModel(compareModelSaveReq);

        CompareModel compareModel = BeanCopyUtil.copyFields(compareModelSaveReq, CompareModel.class);
        compareModel.setModelNo(IdUtil.objectId());
        compareModelMapper.insert(compareModel);

        compareModelDatasetService.saveModelRefBaselineDataset(compareModelSaveReq, compareModel, null);
        compareModelDimService.saveModelRefDim(compareModelSaveReq, compareModel, null);
        compareModelMetricService.saveModelRefMetric(compareModelSaveReq, compareModel, null);
        compareModelCalMetricService.saveModelRefCalMetric(compareModelSaveReq, compareModel, null);
        compareModelQueryConditionService.saveModelRefDatasetQueryCondition(compareModelSaveReq, compareModel, null);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_MODEL_VIEW_NO, compareModel.getModelNo(), "新增比对模型", "新增比对模型", "", "");
            }
        });
        return BeanCopyUtil.copyFields(compareModel, CompareModelPageRes.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CompareModelSaveReq compareModelSaveReq) {
        validateSaveModel(compareModelSaveReq);
        ValidatorUtil.checkEmptyThrowEx(compareModelSaveReq.getModelNo(), "模型编号不能为空");
        CompareModel compareModel = this.getOne(Wrappers.lambdaQuery(CompareModel.class).eq(CompareModel::getModelNo, compareModelSaveReq.getModelNo())
                .eq(CompareModel::getEnterpriseNo, compareModelSaveReq.getEnterpriseNo())
                .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        ValidatorUtil.checkEmptyThrowEx(compareModel, "比对模型不存在");
        BeanUtils.copyProperties(compareModelSaveReq, compareModel);
        compareModelMapper.updateById(compareModel);

        compareModelDatasetService.updateModelRefBaselineDataset(compareModelSaveReq, compareModel);
        compareModelDimService.updateModelRefDim(compareModelSaveReq, compareModel);
        compareModelMetricService.updateModelRefMetric(compareModelSaveReq, compareModel);
        compareModelCalMetricService.updateModelRefCalMetric(compareModelSaveReq, compareModel);
        compareModelQueryConditionService.updateModelRefDatasetQueryCondition(compareModelSaveReq, compareModel);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_MODEL_VIEW_NO, compareModel.getModelNo(), "修改比对模型", "修改比对模型", "", "");
            }
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean delete(CompareModelGetReq deleteReq) {
        // 参数校验
        ValidatorUtil.validateParams(deleteReq);
        // 查询要删除的模型
        CompareModel compareModel = this.getOne(Wrappers.lambdaQuery(CompareModel.class)
                .eq(CompareModel::getModelNo, deleteReq.getModelNo())
                .eq(CompareModel::getEnterpriseNo, deleteReq.getEnterpriseNo())
                .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(compareModel, "比对模型不存在");

        // 逻辑删除模型
        compareModel.setDeleted(DeletedEnum.DELETED.getValue());
        return this.updateById(compareModel);
    }

    @Override
    public Boolean changeStatus(CompareModelChangeStatusReq changeStatusReq) {
        // 参数校验
        ValidatorUtil.validateParams(changeStatusReq);
        // 校验启用状态值是否合法
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.getByValue(changeStatusReq.getEnableStatus());
        String log = "停用比对模型";
        if (enableStatusEnum == null) {
            throw new BusinessException("启用状态值不正确");
        } else if (enableStatusEnum == EnableStatusEnum.ENABLE) {
            log = "启用比对模型";
        }
        String finalLog = log;

        // 查询要更新状态的模型
        CompareModel compareModel = this.getOne(Wrappers.lambdaQuery(CompareModel.class)
                .eq(CompareModel::getModelNo, changeStatusReq.getModelNo())
                .eq(CompareModel::getEnterpriseNo, changeStatusReq.getEnterpriseNo())
                .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(compareModel, "比对模型不存在");

        // 更新状态
        compareModel.setEnableStatus(changeStatusReq.getEnableStatus());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_MODEL_VIEW_NO, compareModel.getModelNo(), finalLog, finalLog, "", "");
            }
        });

        return this.updateById(compareModel);
    }

    @Override
    public CompareModelGetRes get(CompareModelGetReq getReq) {
        // 参数校验
        ValidatorUtil.validateParams(getReq);
        // 查询模型主信息
        CompareModel compareModel = this.getOne(Wrappers.lambdaQuery(CompareModel.class)
                .eq(CompareModel::getModelNo, getReq.getModelNo())
                .eq(CompareModel::getEnterpriseNo, getReq.getEnterpriseNo())
                .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(compareModel, "比对模型不存在");

        // 转换为返回结果
        CompareModelGetRes compareModelGetRes = BeanCopyUtil.copyFields(compareModel, CompareModelGetRes.class);

        // 填充关联信息
        fillRelatedData(compareModelGetRes, compareModel);

        return compareModelGetRes;
    }

    @Override
    public PageVo<CompareModelPageRes> pageQuery(CompareModelPageQueryReq pageReq) {
        // 参数校验
        ValidatorUtil.checkEmptyThrowEx(pageReq, "分页查询参数不能为空");
        ValidatorUtil.checkEmptyThrowEx(pageReq.getEnterpriseNo(), "企业编号不能为空");

        // 设置默认排序
        String orderBy = pageReq.getOrderBy();
        if (StrUtil.isEmpty(orderBy)) {
            orderBy = "id DESC";
        }

        // 使用PageHelper进行分页
        try (Page<CompareModel> pageInfo = PageHelper.startPage(pageReq.getPageIndex(), pageReq.getPageSize(), orderBy)) {
            // 构建查询条件
            LambdaQueryWrapper<CompareModel> queryWrapper = Wrappers.lambdaQuery(CompareModel.class)
                    .eq(CompareModel::getEnterpriseNo, pageReq.getEnterpriseNo())
                    .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .like(StrUtil.isNotEmpty(pageReq.getModelCode()), CompareModel::getModelCode, pageReq.getModelCode())
                    .like(StrUtil.isNotEmpty(pageReq.getModelName()), CompareModel::getModelName, pageReq.getModelName())
                    .eq(pageReq.getEnableStatus() != null, CompareModel::getEnableStatus, pageReq.getEnableStatus());

            // 执行查询
            List<CompareModel> compareModels = this.list(queryWrapper);

            // 转换为返回结果
            List<CompareModelPageRes> pageResList = BeanCopyUtil.copyFieldsList(compareModels, CompareModelPageRes.class);

            // 返回分页结果
            return new PageVo<>(
                    pageInfo.getPageNum(),
                    pageInfo.getPageSize(),
                    pageInfo.getTotal(),
                    pageResList
            );
        }
    }

    @Override
    public CompareModel checkModelExist(String compareModelNo) {
        CompareModel compareModel = this.getOne(Wrappers.lambdaQuery(CompareModel.class)
                .eq(CompareModel::getModelNo, compareModelNo)
                .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(compareModel, "模型不存在");
        return compareModel;
    }

    /**
     * 填充模型关联数据
     *
     * @param compareModelGetRes 返回结果对象
     * @param compareModel       主模型对象
     */
    private void fillRelatedData(CompareModelGetRes compareModelGetRes, CompareModel compareModel) {
        // 填充基准数据集列表
        compareModelDatasetService.fillBaselineDatasetList(compareModelGetRes, compareModel);

        // 填充维度信息（比对对象和比对维度）
        compareModelDimService.fillDimList(compareModelGetRes, compareModel);

        // 填充指标信息（比对指标和基准指标）
        compareModelMetricService.fillMetricList(compareModelGetRes, compareModel);

        // 填充计算指标信息
        compareModelCalMetricService.fillCalculatedMetricList(compareModelGetRes, compareModel);

        // 填充查询条件信息
        compareModelQueryConditionService.fillQueryConditionList(compareModelGetRes, compareModel);

        // 填充基准指标查询条件信息
        compareModelQueryConditionService.fillBaselineMetricQueryConditionList(compareModelGetRes, compareModel);
    }


    /**
     * 校验新增模型参数
     *
     * @param compareModelSaveReq
     */
    private void validateSaveModel(CompareModelSaveReq compareModelSaveReq) {
        ValidatorUtil.validateParams(compareModelSaveReq);
//        if (!Objects.equals(compareModelSaveReq.getEnterpriseNo(), platformEnterpriseNo)) {
//            throw new BusinessException("平台租户编号不正确");
//        }

        // todo-zyc 校验编码和名称是否唯一
        // 校验编码和名称是否唯一
        checkModelCodeAndNameUnique(
                compareModelSaveReq.getEnterpriseNo(),
                compareModelSaveReq.getModelCode(),
                compareModelSaveReq.getModelName(),
                compareModelSaveReq.getModelNo() // 新增时为null，更新时有值
        );

        // todo-zyc 校验启用状态，并设置默认值
        if (compareModelSaveReq.getEnableStatus() == null) {
            compareModelSaveReq.setEnableStatus(EnableStatusEnum.ENABLE.getValue());
        } else {
            EnableStatusEnum byValue = EnableStatusEnum.getByValue(compareModelSaveReq.getEnableStatus());
            if (byValue == null) {
                throw new BusinessException("启用状态不正确");
            }
        }

        // todo-zyc 校验比对数据集/基准数据集是否存在

        // todo-zyc 校验基准数据集连接方式

        // todo-zyc 校验比对对象/比对维度对应字段是否存在

        // todo-zyc 校验比对指标/基准指标是否存在；对应数据集是否正确

        // todo-zyc 校验数据集查询条件是否存在

        // todo-zyc 校验基准指标查询条件是否存在

        // todo-zyc 校验计算指标是否合法，指定的指标是否存在

    }

    /**
     * 校验同一租户下模型编码和名称是否唯一
     *
     * @param enterpriseNo 租户编号
     * @param modelCode    模型编码
     * @param modelName    模型名称
     * @param modelNo      模型编号（用于更新场景排除自身）
     */
    private void checkModelCodeAndNameUnique(String enterpriseNo, String modelCode, String modelName, String modelNo) {
        // 校验模型编码是否唯一
        LambdaQueryWrapper<CompareModel> codeQueryWrapper = Wrappers.lambdaQuery(CompareModel.class)
                .eq(CompareModel::getEnterpriseNo, enterpriseNo)
                .eq(CompareModel::getModelCode, modelCode)
                .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue());

        // 如果是更新场景，排除自身
        if (StrUtil.isNotEmpty(modelNo)) {
            codeQueryWrapper.ne(CompareModel::getModelNo, modelNo);
        }

        if (this.count(codeQueryWrapper) > 0) {
            throw new BusinessException("模型编码已存在");
        }

        // 校验模型名称是否唯一
        LambdaQueryWrapper<CompareModel> nameQueryWrapper = Wrappers.lambdaQuery(CompareModel.class)
                .eq(CompareModel::getEnterpriseNo, enterpriseNo)
                .eq(CompareModel::getModelName, modelName)
                .eq(CompareModel::getDeleted, DeletedEnum.UN_DELETE.getValue());

        // 如果是更新场景，排除自身
        if (StrUtil.isNotEmpty(modelNo)) {
            nameQueryWrapper.ne(CompareModel::getModelNo, modelNo);
        }

        if (this.count(nameQueryWrapper) > 0) {
            throw new BusinessException("模型名称已存在");
        }
    }
}
