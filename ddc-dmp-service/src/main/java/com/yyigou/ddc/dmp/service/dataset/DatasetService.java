package com.yyigou.ddc.dmp.service.dataset;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.model.req.dataset.*;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;

import java.util.List;

public interface DatasetService extends IService<Dataset> {
    String saveDataset(DatasetSaveReq datasetSaveReq);

    Long refDimension(DatasetFieldRefDimensionSaveReq datasetFieldRefDimensionSaveReq);

    List<DatasetDetailRes> queryDataset(DatasetQueryReq datasetQueryReq);

    DatasetDetailRes getDataset(DatasetGetReq datasetGetReq);

    List<DatasetDetailRes> getDatasetList(DatasetGetListReq datasetGetListReq);

    /**
     * 预览数据集数据
     *
     * @param datasetPreviewReq 预览请求参数
     * @return 预览数据结果
     */
    DatasetPreviewRes previewData(DatasetPreviewReq datasetPreviewReq);

    /**
     * 验证数据集配置
     *
     * @param datasetSaveReq 数据集配置请求参数
     * @return 验证结果
     */
    void validateDataset(DatasetSaveReq datasetSaveReq);
}