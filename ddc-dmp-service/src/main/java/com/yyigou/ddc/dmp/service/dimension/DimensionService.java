package com.yyigou.ddc.dmp.service.dimension;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.dimension.entity.Dimension;
import com.yyigou.ddc.dmp.model.req.dimension.DatasetFieldDimensionRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dimension.DimensionSaveReq;

public interface DimensionService extends IService<Dimension> {
    String saveDimension(DimensionSaveReq dimensionSaveReq);

    Long saveDatasetFieldDimensionRef(DatasetFieldDimensionRefSaveReq datasetFieldDimensionRefSaveReq);

}