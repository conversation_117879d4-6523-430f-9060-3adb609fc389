package com.yyigou.ddc.dmp.service.util;

import com.yyigou.ddc.common.error.ErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.SessionUtils;
import com.yyigou.ddc.dmp.common.enums.TenantTypeEnum;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.bo.session.OperationModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Slf4j
public class UserHandleUtils {

    public static <T extends OperationModel> void supplementOperationInfo(T params) {
        SessionUser session = ServiceBaseAbstract.currentRequest().getSession();
        params.setEnterpriseNo(session.getEnterpriseNo());
        params.setEnterpriseName(session.getEnterpriseName());
        params.setCompanyNo(session.getCompanyNo());
        params.setCompanyName(session.getCompanyName());
        params.setUserNo(session.getUserNo());
        params.setEmployerNo(session.getEmployerNo());
        params.setUserName(session.getUserName());
        Map<String, Object> extInfo = session.getExtInfo();
        // 获取集团租户编号，如果有
        String groupTenantNo = MapUtils.getString(extInfo, "groupTenantNo");
        if (StringUtils.isNotBlank(groupTenantNo) && !groupTenantNo.equals(session.getEnterpriseNo())) {
            params.setSubTenant(true);
        }
        params.setGroupTenantNo(groupTenantNo);
        params.setExtInfo(extInfo);

        params.setOrgNo(MapUtils.getString(extInfo, "orgNo"));
        params.setOrgCode(MapUtils.getString(extInfo, "orgCode"));
        params.setOrgName(MapUtils.getString(extInfo, "orgName"));
    }

    /**
     * 获取操作人信息
     *
     * @return
     */
    public static OperationModel getOperationModel() {
        SessionUser sessionUser = SessionUtils.getSessionIfExist();
        if (null == sessionUser) {
            log.error("用户会话信息为空");
            throw new BusinessException(ErrorCode.object_null_code, "用户会话信息为空");
        }        SessionUser session = ServiceBaseAbstract.currentRequest().getSession();
        OperationModel operationModel = new OperationModel();
        operationModel.setEnterpriseNo(session.getEnterpriseNo());
        operationModel.setEnterpriseName(session.getEnterpriseName());
        operationModel.setCompanyNo(session.getCompanyNo());
        operationModel.setCompanyName(session.getCompanyName());
        operationModel.setUserNo(session.getUserNo());
        operationModel.setEmployerNo(session.getEmployerNo());
        operationModel.setUserName(session.getUserName());
        operationModel.setTenantType(session.getTenantType());
        // 获取集团租户编号，如果有
        Map<String, Object> extInfo = session.getExtInfo();
        operationModel.setExtInfo(extInfo);
        // 集团租户编号先放自己
        operationModel.setGroupTenantNo(session.getEnterpriseNo());
        String groupTenantNo = MapUtils.getString(extInfo, "groupTenantNo");
        if (StringUtils.isNotBlank(groupTenantNo) && !groupTenantNo.equals(session.getEnterpriseNo())) {
            operationModel.setSubTenant(true);
            operationModel.setGroupTenantNo(groupTenantNo);
        }
        operationModel.setExtInfo(extInfo);

        operationModel.setOrgNo(MapUtils.getString(extInfo, "orgNo"));
        operationModel.setOrgCode(MapUtils.getString(extInfo, "orgCode"));
        operationModel.setOrgName(MapUtils.getString(extInfo, "orgName"));
        operationModel.setIsMaster(session.getIsMaster());

        if(TenantTypeEnum.GROUP.getValue().equals(session.getTenantType()) || TenantTypeEnum.SUB_ENTERPRISE.getValue().equals(session.getTenantType())) {
            operationModel.setTenantNo(operationModel.getGroupTenantNo());
            operationModel.setSingleOrgFlag(false);
        } else {
            operationModel.setTenantNo(session.getEnterpriseNo());
            operationModel.setSingleOrgFlag(true);
        }
        return operationModel;
    }

    public static OperationModel getOperationModelIfPreset() {
        SessionUser sessionIfExist = SessionUtils.getSessionIfExist();
        if (sessionIfExist == null) {
            return null;
        }
        return getOperationModel();
    }
}
