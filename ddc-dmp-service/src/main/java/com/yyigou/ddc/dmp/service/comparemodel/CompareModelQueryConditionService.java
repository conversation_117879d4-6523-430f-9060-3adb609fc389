package com.yyigou.ddc.dmp.service.comparemodel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelQueryCondition;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
public interface CompareModelQueryConditionService extends IService<CompareModelQueryCondition> {
    void updateModelRefDatasetQueryCondition(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel);

    void saveModelRefDatasetQueryCondition(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<String, CompareModelQueryCondition> noToDbModelQueryConditionMap);

    void fillQueryConditionList(CompareModelGetRes compareModelGetRes, CompareModel compareModel);

    void fillBaselineMetricQueryConditionList(CompareModelGetRes compareModelGetRes, CompareModel compareModel);
}
