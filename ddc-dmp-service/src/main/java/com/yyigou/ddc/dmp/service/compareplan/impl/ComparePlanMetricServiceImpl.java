package com.yyigou.ddc.dmp.service.compareplan.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.enums.CompareModelMetricTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlanMetric;
import com.yyigou.ddc.dmp.dao.compareplan.mapper.ComparePlanMetricMapper;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanRefMetricReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefMetricRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanRefMetricRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelMetricService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanFieldService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanMetricService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Service
@Slf4j
public class ComparePlanMetricServiceImpl extends DmpServiceImpl<ComparePlanMetricMapper, ComparePlanMetric> implements ComparePlanMetricService {

    @Resource
    private CompareModelMetricService compareModelMetricService;

    @Resource
    private ComparePlanFieldService comparePlanFieldService;

    /**
     * 保存比价方案引用的指标
     * @param comparePlanSaveReq
     * @param comparePlan
     * @param noToRecordMap
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePlanRefMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan, Map<String, ComparePlanMetric> noToRecordMap) {
        if (noToRecordMap == null) {
            noToRecordMap = new HashMap<>();
        }
        List<ComparePlanMetric> addList = new ArrayList<>();
        List<ComparePlanMetric> updateList = new ArrayList<>();

        processMetricReqList(comparePlanSaveReq.getCompareMetricList(), comparePlan, noToRecordMap, updateList, addList , comparePlanSaveReq);
        processMetricReqList(comparePlanSaveReq.getBaselineMetricList(), comparePlan, noToRecordMap, updateList, addList, comparePlanSaveReq);

        this.saveBatch(addList);
        this.updateBatchByIdAndEnterpriseNo(updateList);
        List<ComparePlanMetric> needDeleteList = ListUtil.toList(noToRecordMap.values());
        for (ComparePlanMetric needDelete : needDeleteList) {
            needDelete.setDeleted(DeletedEnum.DELETED.getValue());
        }
        this.updateBatchByIdAndEnterpriseNo(needDeleteList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanRefMetric(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan) {
        List<ComparePlanMetric> dbModelMetricList = this.list(Wrappers.lambdaQuery(ComparePlanMetric.class)
                .eq(ComparePlanMetric::getComparePlanNo, comparePlan.getPlanNo())
                .eq(ComparePlanMetric::getEnterpriseNo, comparePlan.getEnterpriseNo())
                .eq(ComparePlanMetric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, ComparePlanMetric> existingMetricMap = dbModelMetricList.stream()
                .collect(Collectors.toMap(ComparePlanMetric::getComparePlanMetricNo, Function.identity()));

        this.savePlanRefMetric(comparePlanSaveReq, comparePlan, existingMetricMap);

    }

    /**
     * 填充所有可用的指标信息，无轮是否选中
     * @param comparePlanGetRes
     * @param comparePlan
     */
    @Override
    public void fillMetricList(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan) {
        List<ComparePlanMetric> selectedPlanMetricList = this.list(Wrappers.lambdaQuery(ComparePlanMetric.class)
                .eq(ComparePlanMetric::getComparePlanNo, comparePlan.getPlanNo())
                .eq(ComparePlanMetric::getEnterpriseNo, comparePlan.getEnterpriseNo())
                .eq(ComparePlanMetric::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        Map<String, ComparePlanMetric> selectedModelMetricNoMap = selectedPlanMetricList.stream().collect(Collectors.toMap(ComparePlanMetric::getCompareModelMetricNo, Function.identity()));

        List<CompareModelRefMetricRes> modelMetricResList = compareModelMetricService.getModelMetricResList(comparePlan.getEnterpriseNo(), comparePlan.getCompareModelNo());

        List<ComparePlanRefMetricRes> compareMetricList = new ArrayList<>();
        List<ComparePlanRefMetricRes> baselineMetricList = new ArrayList<>();
        for (CompareModelRefMetricRes compareModelRefMetricRes : modelMetricResList) {
            ComparePlanRefMetricRes planRefMetricRes = BeanCopyUtil.copyFields(compareModelRefMetricRes, ComparePlanRefMetricRes.class);

            ComparePlanMetric comparePlanMetric = selectedModelMetricNoMap.get(planRefMetricRes.getCompareModelMetricNo());
            if (comparePlanMetric == null) {
                // 预先分配一个新编号
                planRefMetricRes.setSelected(false);
                planRefMetricRes.setComparePlanMetricNo(IdUtil.objectId());
            } else {
                planRefMetricRes.setSelected(true);
                planRefMetricRes.setComparePlanMetricNo(comparePlanMetric.getComparePlanMetricNo());
            }

            if (Objects.equals(compareModelRefMetricRes.getMetricType(), CompareModelMetricTypeEnum.COMPARE_METRIC.getValue())) {
                compareMetricList.add(planRefMetricRes);
            } else if (Objects.equals(compareModelRefMetricRes.getMetricType(), CompareModelMetricTypeEnum.BASELINE_METRIC.getValue())) {
                baselineMetricList.add(planRefMetricRes);
            }
        }
        comparePlanGetRes.setCompareMetricList(compareMetricList);
        comparePlanGetRes.setBaselineMetricList(baselineMetricList);
    }

    private void processMetricReqList(List<ComparePlanRefMetricReq> metricReqList, ComparePlan comparePlan, Map<String, ComparePlanMetric> noToRecordMap, List<ComparePlanMetric> updateList, List<ComparePlanMetric> addList, ComparePlanSaveReq comparePlanSaveReq) {
        if (CollectionUtils.isNotEmpty(metricReqList)) {
            int sort = 1;
            for (ComparePlanRefMetricReq planRefMetricReq : metricReqList) {
                if (!Objects.equals(planRefMetricReq.getSelected(), true)) {
                    continue;
                }
                ComparePlanMetric comparePlanMetric = BeanCopyUtil.copyFields(planRefMetricReq, ComparePlanMetric.class);
                comparePlanMetric.setEnterpriseNo(comparePlan.getEnterpriseNo());
                comparePlanMetric.setComparePlanNo(comparePlan.getPlanNo());
                comparePlanMetric.setSort(sort++);
                if (StrUtil.isNotEmpty(comparePlanMetric.getComparePlanMetricNo()) && noToRecordMap.containsKey(comparePlanMetric.getComparePlanMetricNo())) {
                    ComparePlanMetric remove = noToRecordMap.remove(comparePlanMetric.getComparePlanMetricNo());
                    comparePlanMetric.setId(remove.getId());
                    updateList.add(comparePlanMetric);
                } else {
                    String newComparePlanMetricNo = IdUtil.objectId();
                    if (StrUtil.isNotEmpty(comparePlanMetric.getComparePlanMetricNo())) {
                        // 报表配置可能引用了这里的编号,因此需要同时修改引用处
                        comparePlanFieldService.replaceFieldRefComparePlanMetricNo(comparePlanSaveReq, comparePlanMetric.getComparePlanMetricNo(), newComparePlanMetricNo);
                    }
                    comparePlanMetric.setComparePlanMetricNo(newComparePlanMetricNo);
                    addList.add(comparePlanMetric);
                }
            }
        }
    }
}
