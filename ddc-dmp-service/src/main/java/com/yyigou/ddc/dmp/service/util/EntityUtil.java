package com.yyigou.ddc.dmp.service.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
public class EntityUtil {

    /**
     * 填充修改人信息
     * 注意：设置的字段必须存在，否则会报错
     *
     * @param object
     */
    public static void fillModifyInfo(Object object) {

        ReflectUtil.setFieldValue(object, "modifyTime", DateUtil.now());
        // 从上下文中获取
        ReflectUtil.setFieldValue(object, "modifyName", UserHandleUtils.getOperationModel().getUserName());
        ReflectUtil.setFieldValue(object, "modifyNo", UserHandleUtils.getOperationModel().getEmployerNo());

    }

}
