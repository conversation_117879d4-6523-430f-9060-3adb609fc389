package com.yyigou.ddc.dmp.service.compareplan.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.constant.DmpConstant;
import com.yyigou.ddc.dmp.common.enums.ComparePlanFieldSourceTypeEnum;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.EnableStatusEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.mapper.ComparePlanMapper;
import com.yyigou.ddc.dmp.manager.integration.log.BusinessLogService;
import com.yyigou.ddc.dmp.model.req.compareplan.*;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanPageRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelCalMetricService;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelDimService;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelMetricService;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanCalculatedMetricService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanFieldService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanMetricService;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanService;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Service
@Slf4j
public class ComparePlanServiceImpl extends DmpServiceImpl<ComparePlanMapper, ComparePlan> implements ComparePlanService {


    @Resource
    private ComparePlanFieldService comparePlanFieldService;

    @Resource
    private ComparePlanMetricService comparePlanMetricService;

    @Resource
    private ComparePlanCalculatedMetricService comparePlanCalculatedMetricService;

    @Resource
    private BusinessLogService businessLogService;

    @Resource
    private CompareModelMetricService compareModelMetricService;

    @Resource
    private CompareModelService compareModelService;

    @Resource
    private CompareModelDimService compareModelDimService;

    @Resource
    private CompareModelCalMetricService compareModelCalMetricService;


    /**
     * 保存新增的模型
     * @param comparePlanSaveReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ComparePlanPageRes save(ComparePlanSaveReq comparePlanSaveReq) {
        validateSavePlan(comparePlanSaveReq);
        ComparePlan comparePlan = BeanCopyUtil.copyFields(comparePlanSaveReq, ComparePlan.class);
        comparePlan.setPlanNo(IdUtil.objectId());
        this.save(comparePlan);

        comparePlanMetricService.savePlanRefMetric(comparePlanSaveReq, comparePlan, null);
        comparePlanCalculatedMetricService.savePlanRefCalculatedMetric(comparePlanSaveReq, comparePlan, null);
        comparePlanFieldService.savePlanRefField(comparePlanSaveReq, comparePlan);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_PLAN_VIEW_NO, comparePlan.getPlanNo(), "新增比价方案", "新增比价方案", "", "");
            }
        });
        return BeanCopyUtil.copyFields(comparePlan, ComparePlanPageRes.class);
    }

    /**
     * 校验比价方案
     * 只校验到比对模型层，不继续往下校验数据集、指标等
     * @param comparePlanSaveReq
     */
    private void validateSavePlan(ComparePlanSaveReq comparePlanSaveReq) {
        ValidatorUtil.validateParams(comparePlanSaveReq);
        // todo-zyc 查询条件后续处理

        if (comparePlanSaveReq.getCompareMetricList().stream().noneMatch(e -> Objects.equals(e.getSelected(), true))) {
            throw new BusinessException("请选择至少一个比价指标");
        }

        // 校验编码和名称是否唯一
        checkModelCodeAndNameUnique(
                comparePlanSaveReq.getEnterpriseNo(),
                comparePlanSaveReq.getPlanCode(),
                comparePlanSaveReq.getPlanName(),
                comparePlanSaveReq.getPlanNo()
        );

        if (comparePlanSaveReq.getEnableStatus() == null) {
            comparePlanSaveReq.setEnableStatus(EnableStatusEnum.ENABLE.getValue());
        } else {
            EnableStatusEnum byValue = EnableStatusEnum.getByValue(comparePlanSaveReq.getEnableStatus());
            if (byValue == null) {
                throw new BusinessException("启用状态不正确");
            }
        }

        checkPlanRefField(comparePlanSaveReq.getRowList());
        checkPlanRefField(comparePlanSaveReq.getColumnList());
        checkPlanRefField(comparePlanSaveReq.getValueList());

        // 校验比对模型是否存在
        compareModelService.checkModelExist(comparePlanSaveReq.getCompareModelNo());
        // 校验模型指标是否存在
        compareModelMetricService.checkModelMetricExist(comparePlanSaveReq);
        // 校验模型字段是否存在
        compareModelDimService.checkModelDimExist(comparePlanSaveReq);
        //  校验计算指标是否存在
        compareModelCalMetricService.checkModelCalMetricExist(comparePlanSaveReq);


    }

    private void checkPlanRefField(List<ComparePlanRefFieldReq> fieldList) {
        if (CollectionUtils.isNotEmpty(fieldList)) {
            for (ComparePlanRefFieldReq fieldReq : fieldList) {
                if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_OBJECT.getValue())) {
                    if (fieldReq.getCompareModelDimNo() == null) {
                        throw new BusinessException("方案引用字段编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_DIM.getValue())) {
                    if (fieldReq.getCompareModelDimNo() == null) {
                        throw new BusinessException("方案引用字段编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.COMPARE_METRIC.getValue())) {
                    if (fieldReq.getComparePlanMetricNo() == null) {
                        throw new BusinessException("方案引用比对指标编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.BASELINE_METRIC.getValue())) {
                    if (fieldReq.getComparePlanMetricNo() == null) {
                        throw new BusinessException("方案引用基准指标编号不能为空");
                    }
                } else if (Objects.equals(fieldReq.getSourceType(), ComparePlanFieldSourceTypeEnum.CALCULATED_METRIC.getValue())) {
                    if (fieldReq.getComparePlanCalMetricNo() == null) {
                        throw new BusinessException("方案引用计算指标编号不能为空");
                    }
                } else {
                    throw new BusinessException("方案引用字段来源类型不支持");
                }

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(ComparePlanSaveReq comparePlanSaveReq) {
        validateSavePlan(comparePlanSaveReq);
        ValidatorUtil.checkEmptyThrowEx(comparePlanSaveReq.getPlanNo(), "方案编号不能为空");
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, comparePlanSaveReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, comparePlanSaveReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");
        BeanUtils.copyProperties(comparePlanSaveReq, comparePlan);
        this.updateById(comparePlan);
        comparePlanMetricService.updatePlanRefMetric(comparePlanSaveReq, comparePlan);
        comparePlanCalculatedMetricService.updatePlanRefCalculatedMetric(comparePlanSaveReq, comparePlan);
        comparePlanFieldService.updatePlanRefField(comparePlanSaveReq, comparePlan);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_PLAN_VIEW_NO, comparePlanSaveReq.getPlanNo(), "修改比价方案", "修改比价方案", "", "");
            }
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean delete(ComparePlanGetReq deleteReq) {
        // 参数校验
        ValidatorUtil.validateParams(deleteReq);
        // 查询要删除的模型
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, deleteReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, deleteReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");

        // 逻辑删除模型
        comparePlan.setDeleted(DeletedEnum.DELETED.getValue());
        return this.updateById(comparePlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(ComparePlanChangeStatusReq changeStatusReq) {
        // 参数校验
        ValidatorUtil.validateParams(changeStatusReq);
        // 校验启用状态值是否合法
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.getByValue(changeStatusReq.getEnableStatus());
        String log = "停用比价方案";
        if (enableStatusEnum == null) {
            throw new BusinessException("启用状态值不正确");
        } else if (enableStatusEnum == EnableStatusEnum.ENABLE) {
            log = "启用比价方案";
        }
        String finalLog = log;

        // 查询要更新状态的模型
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, changeStatusReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, changeStatusReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");

        // 更新状态
        comparePlan.setEnableStatus(changeStatusReq.getEnableStatus());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                businessLogService.saveLog(DLogLevel.INFO.name(), DmpConstant.COMPARE_PLAN_VIEW_NO, comparePlan.getPlanNo(), finalLog, finalLog, "", "");
            }
        });

        return this.updateById(comparePlan);
    }

    @Override
    public ComparePlanGetRes get(ComparePlanGetReq getReq) {
        // 参数校验
        ValidatorUtil.validateParams(getReq);
        // 查询模型主信息
        ComparePlan comparePlan = this.getOne(Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getPlanNo, getReq.getPlanNo())
                .eq(ComparePlan::getEnterpriseNo, getReq.getEnterpriseNo())
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        ValidatorUtil.checkEmptyThrowEx(comparePlan, "比价方案不存在");

        // 转换为返回结果
        ComparePlanGetRes comparePlanGetRes = BeanCopyUtil.copyFields(comparePlan, ComparePlanGetRes.class);

        // 填充关联信息
        fillRelatedData(comparePlanGetRes, comparePlan);

        return comparePlanGetRes;
    }

    private void fillRelatedData(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan) {

        comparePlanMetricService.fillMetricList(comparePlanGetRes, comparePlan);
        comparePlanCalculatedMetricService.fillCalculatedMetricList(comparePlanGetRes, comparePlan);
        comparePlanFieldService.fillFieldList(comparePlanGetRes, comparePlan);
    }

    @Override
    public PageVo<ComparePlanPageRes> pageQuery(ComparePlanPageQueryReq pageReq) {
        // 参数校验
        ValidatorUtil.checkEmptyThrowEx(pageReq, "分页查询参数不能为空");
        ValidatorUtil.checkEmptyThrowEx(pageReq.getEnterpriseNo(), "企业编号不能为空");

        // 设置默认排序
        String orderBy = pageReq.getOrderBy();
        if (StrUtil.isEmpty(orderBy)) {
            orderBy = "id DESC";
        }

        // 使用PageHelper进行分页
        try (Page<ComparePlan> pageInfo = PageHelper.startPage(pageReq.getPageIndex(), pageReq.getPageSize(), orderBy)) {
            // 构建查询条件
            LambdaQueryWrapper<ComparePlan> queryWrapper = Wrappers.lambdaQuery(ComparePlan.class)
                    .eq(ComparePlan::getEnterpriseNo, pageReq.getEnterpriseNo())
                    .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .like(StrUtil.isNotEmpty(pageReq.getPlanCode()), ComparePlan::getPlanCode, pageReq.getPlanCode())
                    .like(StrUtil.isNotEmpty(pageReq.getPlanName()), ComparePlan::getPlanName, pageReq.getPlanName())
                    .eq(pageReq.getEnableStatus() != null, ComparePlan::getEnableStatus, pageReq.getEnableStatus());

            // 执行查询
            List<ComparePlan> comparePlans = this.list(queryWrapper);

            // 转换为返回结果
            List<ComparePlanPageRes> pageResList = BeanCopyUtil.copyFieldsList(comparePlans, ComparePlanPageRes.class);

            // 返回分页结果
            return new PageVo<>(
                    pageInfo.getPageNum(),
                    pageInfo.getPageSize(),
                    pageInfo.getTotal(),
                    pageResList
            );
        }
    }


    /**
     * 校验同一租户下方案编码和名称是否唯一
     *
     * @param enterpriseNo 租户编号
     * @param planCode    方案编码
     * @param planName    方案名称
     * @param planNo      方案编号（用于更新场景排除自身）
     */
    private void checkModelCodeAndNameUnique(String enterpriseNo, String planCode, String planName, String planNo) {
        // 校验模型编码是否唯一
        LambdaQueryWrapper<ComparePlan> codeQueryWrapper = Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getEnterpriseNo, enterpriseNo)
                .eq(ComparePlan::getPlanCode, planCode)
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue());

        // 如果是更新场景，排除自身
        if (StrUtil.isNotEmpty(planNo)) {
            codeQueryWrapper.ne(ComparePlan::getPlanNo, planNo);
        }

        if (this.count(codeQueryWrapper) > 0) {
            throw new BusinessException("方案编码已存在");
        }

        // 校验模型名称是否唯一
        LambdaQueryWrapper<ComparePlan> nameQueryWrapper = Wrappers.lambdaQuery(ComparePlan.class)
                .eq(ComparePlan::getEnterpriseNo, enterpriseNo)
                .eq(ComparePlan::getPlanName, planName)
                .eq(ComparePlan::getDeleted, DeletedEnum.UN_DELETE.getValue());

        // 如果是更新场景，排除自身
        if (StrUtil.isNotEmpty(planNo)) {
            nameQueryWrapper.ne(ComparePlan::getPlanNo, planNo);
        }

        if (this.count(nameQueryWrapper) > 0) {
            throw new BusinessException("方案名称已存在");
        }
    }


}
