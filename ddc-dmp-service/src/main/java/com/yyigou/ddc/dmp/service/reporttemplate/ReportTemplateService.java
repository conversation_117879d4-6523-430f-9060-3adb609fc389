package com.yyigou.ddc.dmp.service.reporttemplate;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.dao.entity.ReportTemplate;
import com.yyigou.ddc.dmp.model.req.reporttemplate.*;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateGetRes;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateManagePageRes;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplatePageRes;

import java.util.List;
import java.util.Map;

/**
 * 报表模板表Service接口
 */
public interface ReportTemplateService extends IService<ReportTemplate> {

    ReportTemplateGetRes platformSave(ReportTemplateSaveReq saveReq);

    boolean update(ReportTemplateUpdateReq updateReq);

    ReportTemplateGetRes get(ReportTemplateGetReq getReq);

    boolean platformChangeStatus(ReportTemplateChangeStatusReq changeStatusReq);

    PageVo<ReportTemplatePageRes> pageQuery(ReportTemplatePageQueryReq pageReq);

    boolean delete(ReportTemplateDeleteReq deleteReq);

    PageVo<ReportTemplateManagePageRes> managePageQuery(ReportTemplatePageQueryReq pageReq);

    Boolean manageChangeStatus(ReportTemplateChangeStatusReq changeStatusReq);

    Boolean manageCopy(ReportTemplateManageCopyReq copyReq);

    List<Map<String, Object>> useQueryData(ReportTemplateQueryDataReq queryDataReq);

    Boolean manageAuth(ReportTemplateAuthReq authReq);
}