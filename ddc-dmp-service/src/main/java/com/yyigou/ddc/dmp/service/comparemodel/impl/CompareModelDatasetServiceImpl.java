package com.yyigou.ddc.dmp.service.comparemodel.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModel;
import com.yyigou.ddc.dmp.dao.comparemodel.entity.CompareModelDataset;
import com.yyigou.ddc.dmp.dao.comparemodel.mapper.CompareModelDatasetMapper;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelRefDatasetReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefDatasetRes;
import com.yyigou.ddc.dmp.service.common.impl.DmpServiceImpl;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelDatasetService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Service
public class CompareModelDatasetServiceImpl extends DmpServiceImpl<CompareModelDatasetMapper, CompareModelDataset> implements CompareModelDatasetService {


    /**
     * 保存关联的基准数据集
     * @param compareModelSaveReq
     * @param compareModel
     */
    @Transactional
    @Override
    public void saveModelRefBaselineDataset(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel, Map<Long, CompareModelDataset> idToDbModelDatasetMap) {
        if (idToDbModelDatasetMap == null) {
            idToDbModelDatasetMap = new HashMap<>();
        }
        List<CompareModelDataset> addCompareModelDatasetList = new ArrayList<>();
        List<CompareModelDataset> updateCompareModelDatasetList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(compareModelSaveReq.getBaselineDatasetList())) {
            int sort = 1;
            for (CompareModelRefDatasetReq compareModelRefDatasetReq : compareModelSaveReq.getBaselineDatasetList()) {
                CompareModelDataset compareModelDataset = BeanCopyUtil.copyFields(compareModelRefDatasetReq, CompareModelDataset.class);
                compareModelDataset.setEnterpriseNo(compareModelSaveReq.getEnterpriseNo());
                compareModelDataset.setCompareModelNo(compareModel.getModelNo());
                compareModelDataset.setSort(sort++);
                compareModelDataset.setDatasetType(1);
                if (compareModelDataset.getId() != null && idToDbModelDatasetMap.containsKey(compareModelDataset.getId())) {
                    idToDbModelDatasetMap.remove(compareModelDataset.getId());
                    updateCompareModelDatasetList.add(compareModelDataset);
                } else {
                    compareModelDataset.setId(null);
                    addCompareModelDatasetList.add(compareModelDataset);
                }
            }
        }

        this.saveBatch(addCompareModelDatasetList);
        this.updateBatchByIdAndEnterpriseNo(updateCompareModelDatasetList);
        List<CompareModelDataset> needDeleteModelDatasetList = ListUtil.toList(idToDbModelDatasetMap.values());
        for (CompareModelDataset needDeleteModelDataset : needDeleteModelDatasetList) {
            needDeleteModelDataset.setDeleted(DeletedEnum.DELETED.getValue());
        }
        this.updateBatchByIdAndEnterpriseNo(needDeleteModelDatasetList);

    }





    /**
     * 比对哪些需要插入，哪些需要更新
     * @param compareModelSaveReq
     * @param compareModel
     */
    @Transactional
    @Override
    public void updateModelRefBaselineDataset(CompareModelSaveReq compareModelSaveReq, CompareModel compareModel) {
        List<CompareModelDataset> existCompareModelDatasetList = this.list(Wrappers.lambdaUpdate(CompareModelDataset.class)
                .eq(CompareModelDataset::getCompareModelNo, compareModel.getModelNo())
                .eq(CompareModelDataset::getEnterpriseNo, compareModel.getEnterpriseNo())
                .eq(CompareModelDataset::getDatasetType, 1)
                .eq(CompareModelDataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        Map<Long, CompareModelDataset> idToDbModelDatasetMap = existCompareModelDatasetList.stream().collect(Collectors.toMap(CompareModelDataset::getId, Function.identity()));

        this.saveModelRefBaselineDataset(compareModelSaveReq, compareModel, idToDbModelDatasetMap);
    }


    /**
     * 填充基准数据集列表
     * @param compareModelGetRes 返回结果对象
     * @param compareModel 主模型对象
     */
    @Override
    public void fillBaselineDatasetList(CompareModelGetRes compareModelGetRes, CompareModel compareModel) {
        List<CompareModelDataset> datasetList = this.list(
                Wrappers.lambdaQuery(CompareModelDataset.class)
                        .eq(CompareModelDataset::getCompareModelNo, compareModel.getModelNo())
                        .eq(CompareModelDataset::getEnterpriseNo, compareModel.getEnterpriseNo())
                        .eq(CompareModelDataset::getDatasetType, 1) // 基准数据集
                        .eq(CompareModelDataset::getDeleted, DeletedEnum.UN_DELETE.getValue())

        );

        if (CollectionUtils.isNotEmpty(datasetList)) {
            List<CompareModelRefDatasetRes> baselineDatasetList = BeanCopyUtil.copyFieldsList(datasetList, CompareModelRefDatasetRes.class);
            compareModelGetRes.setBaselineDatasetList(baselineDatasetList);
        }
    }

}
