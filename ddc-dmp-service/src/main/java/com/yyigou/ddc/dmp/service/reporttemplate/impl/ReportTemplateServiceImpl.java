package com.yyigou.ddc.dmp.service.reporttemplate.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.dmp.common.constant.GenerateNoConstant;
import com.yyigou.ddc.dmp.common.context.SqlExecuteContext;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.enums.EnableStatusEnum;
import com.yyigou.ddc.dmp.common.enums.PresetEnum;
import com.yyigou.ddc.dmp.common.enums.SqlOperatorEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.entity.ReportTemplate;
import com.yyigou.ddc.dmp.dao.entity.ReportTemplateAuth;
import com.yyigou.ddc.dmp.dao.entity.ReportTemplateRef;
import com.yyigou.ddc.dmp.dao.mapper.ReportTemplateAuthMapper;
import com.yyigou.ddc.dmp.dao.mapper.ReportTemplateMapper;
import com.yyigou.ddc.dmp.dao.mapper.ReportTemplateRefMapper;
import com.yyigou.ddc.dmp.manager.integration.number.NumberService;
import com.yyigou.ddc.dmp.model.bo.reporttemplate.ReportTemplateBizConfigBO;
import com.yyigou.ddc.dmp.model.bo.reporttemplate.ReportTemplateCustomTreeBO;
import com.yyigou.ddc.dmp.model.bo.reporttemplate.ReportTemplateFieldBO;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.SqlFieldBO;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.SqlTableBO;
import com.yyigou.ddc.dmp.model.bo.sqlbuild.SqlWhereBO;
import com.yyigou.ddc.dmp.model.req.reporttemplate.*;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateGetRes;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateManagePageRes;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplatePageRes;
import com.yyigou.ddc.dmp.service.common.DorisDataAccessService;
import com.yyigou.ddc.dmp.service.reporttemplate.ReportTemplateService;
import com.yyigou.ddc.dmp.service.util.EntityUtil;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表模板表Service实现类
 */
@Service
public class ReportTemplateServiceImpl extends ServiceImpl<ReportTemplateMapper, ReportTemplate> implements ReportTemplateService {

    @Autowired
    private NumberService numberService;

    @Autowired
    private ReportTemplateRefMapper reportTemplateRefMapper;

    @Autowired
    private ReportTemplateAuthMapper reportTemplateAuthMapper;


    /**
     * 保存报表模板
     *
     * @param saveReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReportTemplateGetRes platformSave(ReportTemplateSaveReq saveReq) {
        ValidatorUtil.validateParams(saveReq);
        EnableStatusEnum enableStatusEnum = EnableStatusEnum.getByValue(saveReq.getEnableStatus());
        if (enableStatusEnum == null) {
            if (saveReq.getEnableStatus() == null) {
                saveReq.setEnableStatus(EnableStatusEnum.ENABLE.getValue());
            } else {
                throw new BusinessException("启用状态不正确");
            }
        }

        checkTemplateNameUniqueInEnterpriseNo(saveReq.getEnterpriseNo(), saveReq.getTemplateName(), null);
        // todo-zyc 生成编码、编号、校验名称是否重复，校验数据集是否存在

        ReportTemplateBizConfigBO bizConfigBO = saveReq.getBizConfigBO();
        SqlFieldLists result = getSqlFieldLists(bizConfigBO);

        String templateNo = numberService.generateNo(GenerateNoConstant.DMP_REPORT_TEMPLATE_NO);

        ReportTemplate reportTemplate = BeanCopyUtil.copyFields(saveReq, ReportTemplate.class);
        reportTemplate.setColumnFields(JSONUtil.toJsonStr(result.columnList()));
        reportTemplate.setRowFields(JSONUtil.toJsonStr(result.rowList()));
        reportTemplate.setValueFields(JSONUtil.toJsonStr(result.valueList()));
        reportTemplate.setBizConfig(JSONUtil.toJsonStr(bizConfigBO));

        reportTemplate.setTemplateMode(1);
        reportTemplate.setTemplateNo(templateNo);
        reportTemplate.setTemplateCode(templateNo);
        reportTemplate.setVersion(1);
        // 表示平台端的模板
        reportTemplate.setPreset(1);
        super.save(reportTemplate);
        return BeanCopyUtil.copyFields(reportTemplate, ReportTemplateGetRes.class);
    }

    /**
     * 校验租户下的模板名称是否重复（不会从平台端引用的模板名称）
     * @param enterpriseNo
     * @param templateName
     * @param templateCode
     */
    private void checkTemplateNameUniqueInEnterpriseNo(String enterpriseNo,String templateName, String templateCode) {
        long count = this.count(Wrappers.lambdaQuery(ReportTemplate.class).eq(ReportTemplate::getTemplateName, templateName)
                .eq(ReportTemplate::getEnterpriseNo, enterpriseNo)
                .ne(StrUtil.isNotEmpty(templateCode), ReportTemplate::getTemplateCode, templateCode)
                .eq(ReportTemplate::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if ( count > 0 ){
            throw new BusinessException("模板名称重复");
        }
    }

    /**
     * 将业务配置解析成行列指标维度的sql字段
     *
     * @param bizConfigBO
     * @return
     */
    private static SqlFieldLists getSqlFieldLists(ReportTemplateBizConfigBO bizConfigBO) {
        List<SqlFieldBO> rowList = new ArrayList<>();
        List<SqlFieldBO> columnList = new ArrayList<>();
        List<SqlFieldBO> valueList = new ArrayList<>();
        if (Objects.equals(bizConfigBO.getTreeType(), 0)) {
            rowList = fieldListToSqlFieldList(bizConfigBO.getRowFieldList());
            columnList = fieldListToSqlFieldList(bizConfigBO.getColumnFieldList());
            valueList = fieldListToSqlFieldList(bizConfigBO.getValueFieldList());
        } else if (Objects.equals(bizConfigBO.getTreeType(), 1)) {
            // 行维度是固定的自定义树
            columnList = fieldListToSqlFieldList(bizConfigBO.getColumnFieldList());
            rowList = recursionCollectSqlField(bizConfigBO.getRowTree());
        } else if (Objects.equals(bizConfigBO.getTreeType(), 2)) {
            // 列维度是固定的自定义树
            rowList = fieldListToSqlFieldList(bizConfigBO.getRowFieldList());
            columnList = recursionCollectSqlField(bizConfigBO.getColumnTree());
        }
        return new SqlFieldLists(rowList, columnList, valueList);
    }

    private record SqlFieldLists(List<SqlFieldBO> rowList, List<SqlFieldBO> columnList, List<SqlFieldBO> valueList) {
    }

    /**
     * 业务配置提取成sql字段
     *
     * @param fieldBOList
     * @return
     */
    private static List<SqlFieldBO> fieldListToSqlFieldList(List<ReportTemplateFieldBO> fieldBOList) {
        if (CollectionUtil.isEmpty(fieldBOList)) {
            return new ArrayList<>();
        }
        List<SqlFieldBO> sqlFieldBOList = new ArrayList<>();
        for (ReportTemplateFieldBO field : fieldBOList) {
            // 虚拟字段不保存到sql维度信息中
            if (Objects.equals(field.getFieldType(), 1)) {
                SqlFieldBO sqlFieldBO = SqlFieldBO.builder()
                        .fieldCode(field.getField())
                        .fieldAlias(field.getAlias())
                        .modelNo(field.getModelNo())
                        .build();
                sqlFieldBOList.add(sqlFieldBO);
                ReportTemplateFieldBO hiddenFiled = field.getHiddenField();
                if (hiddenFiled != null && Objects.equals(hiddenFiled.getFieldType(), 1)) {
                    SqlFieldBO hiddenSqlFieldBO = SqlFieldBO.builder()
                            .fieldCode(hiddenFiled.getField())
                            .fieldAlias(hiddenFiled.getAlias())
                            .modelNo(hiddenFiled.getModelNo())
                            .build();
                    sqlFieldBOList.add(hiddenSqlFieldBO);
                }
            }
        }
        return sqlFieldBOList;
    }

    /**
     * 递归收集SqlFieldBO
     *
     * @param treeBOList
     * @return
     */
    private static List<SqlFieldBO> recursionCollectSqlField(List<ReportTemplateCustomTreeBO> treeBOList) {
        if (CollectionUtil.isEmpty(treeBOList)) {
            throw new BusinessException("自定义节点为空");
        }

        List<SqlFieldBO> fieldList = new ArrayList<>();

        for (ReportTemplateCustomTreeBO treeBO : treeBOList) {
            recursionCollectSqlField(treeBO, fieldList);
        }
        return fieldList;
    }

    private static void recursionCollectSqlField(ReportTemplateCustomTreeBO treeBO, List<SqlFieldBO> fieldList) {
        if (treeBO == null) {
            throw new BusinessException("自定义节点为空");
        }

        if (Objects.equals(treeBO.getFieldType(), 1)) {
            // 加入sql字段
            SqlFieldBO sqlFieldBO = SqlFieldBO.builder()
                    .fieldCode(treeBO.getField())
                    .fieldAlias(treeBO.getAlias())
                    .modelNo(treeBO.getModelNo())
                    .build();
            fieldList.add(sqlFieldBO);
        }
        // 遍历叶子节点
        if (CollectionUtil.isNotEmpty(treeBO.getChildren())) {
            for (ReportTemplateCustomTreeBO tree : treeBO.getChildren()) {
                recursionCollectSqlField(tree, fieldList);
            }
        }
    }

    /**
     * 更新报表模板
     *
     * @param updateReq
     * @return
     */
    @Override
    public boolean update(ReportTemplateUpdateReq updateReq) {
        ValidatorUtil.validateParams(updateReq);
        checkTemplateNameUniqueInEnterpriseNo(updateReq.getEnterpriseNo(), updateReq.getTemplateName(), updateReq.getTemplateCode());
        // 需要同租户才可修改
        ReportTemplate templateByCode = getEnterpriseTemplate(updateReq.getEnterpriseNo(), updateReq.getTemplateCode());
        if (templateByCode == null) {
            throw new BusinessException("报表模板不存在");
        }
        BeanUtil.copyProperties(updateReq, templateByCode, "enableStatus");

        ReportTemplateBizConfigBO bizConfigBO = updateReq.getBizConfigBO();
        SqlFieldLists result = getSqlFieldLists(bizConfigBO);
        BeanUtil.copyProperties(updateReq, templateByCode);
        templateByCode.setColumnFields(JSONUtil.toJsonStr(result.columnList()));
        templateByCode.setRowFields(JSONUtil.toJsonStr(result.rowList()));
        templateByCode.setValueFields(JSONUtil.toJsonStr(result.valueList()));
        templateByCode.setBizConfig(JSONUtil.toJsonStr(bizConfigBO));
        EntityUtil.fillModifyInfo(templateByCode);

        return getBaseMapper().updateAllById(updateReq.getEnterpriseNo(), templateByCode) > 0;
    }

    @Override
    public ReportTemplateGetRes get(ReportTemplateGetReq getReq) {
        ValidatorUtil.validateParams(getReq);
        ReportTemplate reportTemplate = getCanUseTemplateByCode(getReq.getEnterpriseNo(), getReq.getTemplateCode());
        return BeanCopyUtil.copyFields(reportTemplate, ReportTemplateGetRes.class);
    }

    @Override
    public boolean platformChangeStatus(ReportTemplateChangeStatusReq changeStatusReq) {
        ValidatorUtil.validateParams(changeStatusReq);
        ReportTemplate templateByCode = getEnterpriseTemplate(changeStatusReq.getEnterpriseNo(), changeStatusReq.getTemplateCode());
        if (templateByCode == null) {
            throw new BusinessException("报表模板不存在");
        }
        if (Objects.equals(changeStatusReq.getEnableStatus(), templateByCode.getEnableStatus())) {
            return false;
        }
        templateByCode.setEnableStatus(changeStatusReq.getEnableStatus());
        EntityUtil.fillModifyInfo(templateByCode);

        return getBaseMapper().updateAllById(templateByCode.getEnterpriseNo(), templateByCode) > 0;
    }

    /**
     * 平台端分页查询
     *
     * @param pageReq
     * @return
     */
    @Override
    public PageVo<ReportTemplatePageRes> pageQuery(ReportTemplatePageQueryReq pageReq) {
        ValidatorUtil.checkEmptyThrowEx(pageReq.getEnterpriseNo(), "租户编号不能为空");

        String orderBy = pageReq.getOrderBy();
        if (StrUtil.isEmpty(orderBy)) {
            orderBy = "id desc";
        }
        try (Page<ReportTemplate> pageInfo = PageHelper.startPage(pageReq.getPageIndex(), pageReq.getPageSize(), orderBy)) {
            LambdaQueryWrapper<ReportTemplate> queryWrapper = Wrappers.lambdaQuery(ReportTemplate.class)
                    .eq(ReportTemplate::getEnterpriseNo, pageReq.getEnterpriseNo())
                    .eq(ReportTemplate::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    .eq(ReportTemplate::getPreset, PresetEnum.PRESET.getValue());

            List<ReportTemplate> reportTemplates = this.getBaseMapper().selectList(queryWrapper);

            return new PageVo<>(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), BeanCopyUtil.copyFieldsList(reportTemplates, ReportTemplatePageRes.class));
        }
    }

    @Override
    public boolean delete(ReportTemplateDeleteReq deleteReq) {
        ValidatorUtil.validateParams(deleteReq);
        ReportTemplate templateByCode = getEnterpriseTemplate(deleteReq.getEnterpriseNo(), deleteReq.getTemplateCode());
        if (templateByCode == null) {
            throw new BusinessException("报表模板不存在");
        }
        templateByCode.setDeleted(DeletedEnum.DELETED.getValue());
        EntityUtil.fillModifyInfo(templateByCode);
        return getBaseMapper().updateAllById(templateByCode.getEnterpriseNo(), templateByCode) > 0;
    }

    /**
     * 数据可能来自两块：1-当前租户自己的报表模板；2-平台共享的模板
     *
     * @param pageReq
     * @return
     */
    @Override
    public PageVo<ReportTemplateManagePageRes> managePageQuery(ReportTemplatePageQueryReq pageReq) {
        ValidatorUtil.checkEmptyThrowEx(pageReq.getEnterpriseNo(), "租户编号不能为空");

        String orderBy = pageReq.getOrderBy();
        if (StrUtil.isEmpty(orderBy)) {
            orderBy = "id desc";
        }
        try (Page<ReportTemplate> pageInfo = PageHelper.startPage(pageReq.getPageIndex(), pageReq.getPageSize(), orderBy)) {
            List<ReportTemplateManagePageRes> reportTemplateManagePageRes = reportTemplateRefMapper.selectListByManage(pageReq);
            return new PageVo<>(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), reportTemplateManagePageRes);
        }
    }

    /**
     * 管理端启用/禁用是控制引用表的状态
     *
     * @param changeStatusReq
     * @return
     */
    @Override
    public Boolean manageChangeStatus(ReportTemplateChangeStatusReq changeStatusReq) {
        ValidatorUtil.validateParams(changeStatusReq);
        ReportTemplate templateByCode = getCanUseTemplateByCode(changeStatusReq.getEnterpriseNo(), changeStatusReq.getTemplateCode());
        if (templateByCode == null) {
            throw new BusinessException("报表模板不存在");
        }

        ReportTemplateRef reportTemplateRef = reportTemplateRefMapper.selectOne(Wrappers.lambdaQuery(ReportTemplateRef.class)
                .eq(ReportTemplateRef::getTemplateCode, changeStatusReq.getTemplateCode())
                .eq(ReportTemplateRef::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if (reportTemplateRef == null) {
            if (Objects.equals(templateByCode.getPreset(), PresetEnum.PRESET.getValue())) {
                // 说明是租户端第一次启用平台的报表模板
                reportTemplateRef = new ReportTemplateRef();
                reportTemplateRef.setTemplateCode(templateByCode.getTemplateCode());
                reportTemplateRef.setEnterpriseNo(changeStatusReq.getEnterpriseNo());
                reportTemplateRef.setEnableStatus(changeStatusReq.getEnableStatus());
                reportTemplateRefMapper.insert(reportTemplateRef);
                return true;
            }
            throw new BusinessException("报表模板不存在");
        }


        if (Objects.equals(changeStatusReq.getEnableStatus(), reportTemplateRef.getEnableStatus())) {
            return false;
        }
        reportTemplateRef.setEnableStatus(changeStatusReq.getEnableStatus());
        EntityUtil.fillModifyInfo(templateByCode);

        return reportTemplateRefMapper.updateAllById(reportTemplateRef.getEnterpriseNo(), reportTemplateRef) > 0;
    }

    @Override
    public Boolean manageCopy(ReportTemplateManageCopyReq copyReq) {
        // todo-zyc
        return null;
    }


    @Autowired
    private DorisDataAccessService dorisDataAccessService;

    /**
     * 查询报表数据
     *
     * @param queryDataReq
     * @return
     */
    @Override
    public List<Map<String, Object>> useQueryData(ReportTemplateQueryDataReq queryDataReq) {
        ValidatorUtil.validateParams(queryDataReq);
        ReportTemplate templateByCode = getCanUseTemplateByCode(queryDataReq.getEnterpriseNo(), queryDataReq.getTemplateCode());
        if (templateByCode == null) {
            throw new BusinessException("报表模板不存在");
        }

        SqlExecuteContext sqlExecuteContext = new SqlExecuteContext();
        buildSqlFieldsFromTemplate(templateByCode, sqlExecuteContext);
        sqlExecuteContext.setVariableMap(queryDataReq.getVariableMap());


        // todo-zyc 下游拼接sql，实际进行查询
        try {
            return dorisDataAccessService.executeQuery(sqlExecuteContext);
        } catch (SQLException e) {
            log.error("数据查询异常", e);
            throw new BusinessException("数据查询异常");
        }

    }

    /**
     * 租户管理段授权
     * @param authReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean manageAuth(ReportTemplateAuthReq authReq) {
        ValidatorUtil.validateParams(authReq);
        ReportTemplate templateByCode = getCanUseTemplateByCode(authReq.getEnterpriseNo(), authReq.getTemplateCode());
        if (templateByCode == null) {
            throw new BusinessException("报表模板不存在");
        }

        List<ReportTemplateAuth> existAuthList = reportTemplateAuthMapper.selectList(Wrappers.lambdaQuery(ReportTemplateAuth.class)
                .eq(ReportTemplateAuth::getEnterpriseNo, authReq.getEnterpriseNo())
                .eq(ReportTemplateAuth::getTemplateCode, authReq.getTemplateCode())
                .eq(ReportTemplateAuth::getAuthToUserNo, authReq.getAuthToUserNo())
                .eq(ReportTemplateAuth::getDeleted, DeletedEnum.UN_DELETE.getValue()));

        // 对比existAuthList中的组织编号和authReq中的组织编号，找到需要新增和删除的数据
        Set<String> requestedOrgNos = CollectionUtil.isEmpty(authReq.getAuthOrgNoList()) ? new HashSet<>() : new HashSet<>(authReq.getAuthOrgNoList());
        List<ReportTemplateAuth> toDeleteList = new ArrayList<>();
        List<ReportTemplateAuth> toAddList = new ArrayList<>();

        // 遍历已存在的授权记录，进行对比
        for (ReportTemplateAuth existingAuth : existAuthList) {
            if (!requestedOrgNos.contains(existingAuth.getAuthOrgNo())) {
                // 不存在于请求中：需要删除
                toDeleteList.add(existingAuth);
            }
        }

        Set<String> existAuthOrgNos = existAuthList.stream().map(ReportTemplateAuth::getAuthOrgNo).collect(Collectors.toSet());
        for (String requestedOrgNo : requestedOrgNos) {
            if (!existAuthOrgNos.contains(requestedOrgNo)) {
                ReportTemplateAuth newAuth = new ReportTemplateAuth();
                newAuth.setEnterpriseNo(authReq.getEnterpriseNo());
                newAuth.setTemplateCode(authReq.getTemplateCode());
                newAuth.setAuthToUserNo(authReq.getAuthToUserNo());
                newAuth.setAuthToUserName(authReq.getAuthToUserName());
                newAuth.setAuthOrgNo(requestedOrgNo);
                toAddList.add(newAuth);
            }
        }

        for (ReportTemplateAuth reportTemplateAuth : toAddList) {
            reportTemplateAuthMapper.insert(reportTemplateAuth);
        }
        if (!toDeleteList.isEmpty()) {
            reportTemplateAuthMapper.update(null, Wrappers.lambdaUpdate(ReportTemplateAuth.class)
                    .set(ReportTemplateAuth::getDeleted, DeletedEnum.DELETED.getValue())
                    .set(ReportTemplateAuth::getModifyTime, DateUtil.now())
                    .set(ReportTemplateAuth::getModifyNo, UserHandleUtils.getOperationModel().getEmployerNo())
                    .set(ReportTemplateAuth::getModifyName, UserHandleUtils.getOperationModel().getUserName())
                    .eq(ReportTemplateAuth::getId, toDeleteList.stream().map(ReportTemplateAuth::getId).collect(Collectors.toSet()))
            );
        }

        return true;
    }

    /**
     * 解析报表模板配置，然后将select和where条件设置到sql执行上下文中
     *
     * @param reportTemplate
     * @param sqlExecuteContext
     */
    public void buildSqlFieldsFromTemplate(ReportTemplate reportTemplate, SqlExecuteContext sqlExecuteContext) {
        if (reportTemplate == null) {
            throw new BusinessException("报表模板不能为空");
        }

        List<SqlFieldBO> rowFieldList = StrUtil.isBlank(reportTemplate.getRowFields()) ? CollectionUtil.newArrayList() : JSONUtil.toList(reportTemplate.getRowFields(), SqlFieldBO.class);
        List<SqlFieldBO> columnFieldList = StrUtil.isBlank(reportTemplate.getColumnFields()) ? CollectionUtil.newArrayList() : JSONUtil.toList(reportTemplate.getRowFields(), SqlFieldBO.class);
        List<SqlFieldBO> indicatorFieldList = StrUtil.isBlank(reportTemplate.getValueFields()) ? CollectionUtil.newArrayList() : JSONUtil.toList(reportTemplate.getValueFields(), SqlFieldBO.class);

        if (CollectionUtil.isEmpty(rowFieldList) && CollectionUtil.isEmpty(columnFieldList)) {
            throw new BusinessException("行维度和列维度不能同时为空");
        }

        List<SqlFieldBO> selectList = CollectionUtil.newArrayList();
        List<SqlFieldBO> groupbyList = CollectionUtil.newArrayList();

        for (SqlFieldBO reportTemplateFieldBO : rowFieldList) {
            SqlFieldBO sqlTableFieldModel = BeanUtil.copyProperties(reportTemplateFieldBO, SqlFieldBO.class);
            selectList.add(sqlTableFieldModel);
            groupbyList.add(sqlTableFieldModel);
        }
        for (SqlFieldBO reportTemplateFieldBO : columnFieldList) {
            SqlFieldBO sqlTableFieldModel = BeanUtil.copyProperties(reportTemplateFieldBO, SqlFieldBO.class);
            selectList.add(sqlTableFieldModel);
            groupbyList.add(sqlTableFieldModel);
        }
        for (SqlFieldBO reportTemplateFieldBO : indicatorFieldList) {
            SqlFieldBO sqlTableFieldModel = BeanUtil.copyProperties(reportTemplateFieldBO, SqlFieldBO.class);
            selectList.add(sqlTableFieldModel);
            if (!Objects.equals(reportTemplateFieldBO.getIsAggregation(), Boolean.TRUE)) {
                groupbyList.add(sqlTableFieldModel);
            }
        }

        sqlExecuteContext.setGroupByList(groupbyList);
        sqlExecuteContext.setSelectList(selectList);


        // todo-zyc 处理主表、join表、join条件（直接从模型中取解析好的关系，和上面的字段是否用到没关系）
        // todo-zyc 根据数据集编号查询得到数据集中的主表、以及其他表的关联关系.注意：无论数据集中的表的字段是否用到，查询时都要连该表
        SqlTableBO mainTable = SqlTableBO.builder().build();
        sqlExecuteContext.setMainTable(mainTable);

        // todo-zyc 处理where条件。主表必须有租户字段
        if (sqlExecuteContext.getWhereList() == null) {
            sqlExecuteContext.setWhereList(new ArrayList<>());
        }
        // 必须添加租户数据过滤条件
        sqlExecuteContext.getWhereList().add(SqlWhereBO.builder()
                .fieldCode("enterprise_no")
                .modelNo(sqlExecuteContext.getMainTable().getModelNo())
                .operator(SqlOperatorEnum.EQ.getOperator())
                .value(reportTemplate.getEnterpriseNo())
                .build()
        );

        sqlExecuteContext.setEnterpriseNo(reportTemplate.getEnterpriseNo());

    }


    /**
     * 查询当前租户下的报表模板
     *
     * @param templateCode
     * @return
     */
    public ReportTemplate getEnterpriseTemplate(String enterpriseNo, String templateCode) {
        LambdaQueryWrapper<ReportTemplate> queryWrapper = Wrappers.lambdaQuery(ReportTemplate.class)
                .eq(ReportTemplate::getTemplateCode, templateCode)
                .eq(ReportTemplate::getEnterpriseNo, enterpriseNo)
                .eq(ReportTemplate::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return super.getOne(queryWrapper);
    }

    /**
     * 查询当前用户可见报表模板（有两种情况：1. 当前租户的 2. 平台预置且启用的）
     *
     * @param templateCode
     * @return
     */
    public ReportTemplate getCanUseTemplateByCode(String enterpriseNo, String templateCode) {
        LambdaQueryWrapper<ReportTemplate> queryWrapper = Wrappers.lambdaQuery(ReportTemplate.class)
                .eq(ReportTemplate::getTemplateCode, templateCode)
                .and(andWrapper -> {
                    andWrapper.and(nestedAndWrapper -> {
                                nestedAndWrapper
                                        .eq(ReportTemplate::getPreset, PresetEnum.PRESET.getValue())
                                        .eq(ReportTemplate::getEnableStatus, EnableStatusEnum.ENABLE.getValue());

                            })
                            .or()
                            .eq(ReportTemplate::getEnterpriseNo, enterpriseNo)
                    ;
                })
                .eq(ReportTemplate::getDeleted, DeletedEnum.UN_DELETE.getValue());
        return super.getOne(queryWrapper);
    }


}