package com.yyigou.ddc.dmp.service.compareplan;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlan;
import com.yyigou.ddc.dmp.dao.compareplan.entity.ComparePlanField;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
public interface ComparePlanFieldService extends IService<ComparePlanField> {
    void savePlanRefField(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan);

    void updatePlanRefField(ComparePlanSaveReq comparePlanSaveReq, ComparePlan comparePlan);

    void fillFieldList(ComparePlanGetRes comparePlanGetRes, ComparePlan comparePlan);

    void replaceFieldRefComparePlanMetricNo(ComparePlanSaveReq comparePlanSaveReq, String oldComparePlanMetricNo, String newComparePlanMetricNo);

    void replaceFieldRefComparePlanCalMetricNo(ComparePlanSaveReq comparePlanSaveReq, String oldComparePlanCalMetricNo, String newComparePlanCalMetricNo);
}
