package com.yyigou.ddc.dmp.model.res.dataset;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DatasetDetailRes implements Serializable {
    private String enterpriseNo;
    private String datasetNo;
    private String datasetCode;
    private String datasetName;
    private String drivingCatalogName;
    private String drivingSchemaName;
    private String drivingTableName;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;

    private List<DatasetJoinRelDetailRes> datasetJoinRels;
    private List<DatasetFieldsDetailRes> datasetFields;
}

