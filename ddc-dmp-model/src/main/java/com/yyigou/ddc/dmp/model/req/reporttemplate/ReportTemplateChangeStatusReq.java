package com.yyigou.ddc.dmp.model.req.reporttemplate;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateChangeStatusReq {

    /**
     * 租户id
     */
    @NotNull(message = "租户编号不能为空")
    private String enterpriseNo;

    @NotNull(message = "报表模板编码不能为空")
    @Length(max = 32, message = "报表模板编码长度不能超过32")
    private String templateCode;


    /**
     * 启用状态 1-启用，2-禁用
     */
    @NotNull(message = "启用状态不能为空")
    @Max( value = 2, message = "启用状态值不正确")
    @Min( value = 1, message = "启用状态值不正确")
    private Integer enableStatus;





}
