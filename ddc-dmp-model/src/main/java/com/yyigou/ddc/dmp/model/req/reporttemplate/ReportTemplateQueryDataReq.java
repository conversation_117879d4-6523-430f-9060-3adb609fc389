package com.yyigou.ddc.dmp.model.req.reporttemplate;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateQueryDataReq {

    /**
     * 租户id
     */
    @NotNull(message = "租户编号不能为空")
    private String enterpriseNo;

    @NotNull(message = "报表模板编码不能为空")
    @Length(max = 32, message = "报表模板编码长度不能超过32")
    private String templateCode;


    private Map<String,Object> variableMap;





}
