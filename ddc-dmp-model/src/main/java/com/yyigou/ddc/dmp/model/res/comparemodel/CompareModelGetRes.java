package com.yyigou.ddc.dmp.model.res.comparemodel;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelGetRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模型唯一编号.更新场景必传
     */
    private String modelNo;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 启用状态
     */
    private Integer enableStatus;

    /**
     * 比对数据集编号
     */
    private String compareDatasetNo;

    /**
     * 比对数据集名称
     */
    private String compareDatasetName;

    /**
     * 基准数据集集合
     */
    private List<CompareModelRefDatasetRes> baselineDatasetList;

    /**
     * 比对对象字段集合
     */
    private List<CompareModelRefFieldRes> compareObjectList;

    /**
     * 比对维度字段集合
     */
    private List<CompareModelRefFieldRes> compareDimList;

    /**
     * 比对指标集合
     */
    private List<CompareModelRefMetricRes> compareMetricList;

    /**
     * 比对数据集查询条件集合
     */
    private List<CompareModelRefQueryConditionRes> datasetQueryConditionList;

    /**
     * 基准指标集合
     */
    private List<CompareModelRefBaselineMetricRes> baselineMetricList;

    /**
     * 计算指标集合
     */
    private List<CompareModelRefCalculatedMetricRes> calculatedMetricList;
}
