package com.yyigou.ddc.dmp.model.req.reporttemplate;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateUpdateReq extends ReportTemplateSaveReq {

    /**
     * 报表模板编号
     */
    @NotNull(message = "报表模板编号不能为空")
    @Length(max = 32, message = "报表模板编码长度不能超过32")
    private String templateCode;



}
