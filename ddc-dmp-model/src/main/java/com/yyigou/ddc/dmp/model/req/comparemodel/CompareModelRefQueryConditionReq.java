package com.yyigou.ddc.dmp.model.req.comparemodel;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefQueryConditionReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识，用于更新
     */
    private String compareModelQueryConditionNo;

    /**
     * 查询条件编号
     */
    @NotBlank(message = "查询条件编号不能为空")
    @Length(max = 64, message = "查询条件编号长度不能超过64")
    private String datasetQueryConditionNo;


    /**
     * 是否是基准指标的公共查询条件。1-是，0-否
     */
    private Integer isCommon;



}
