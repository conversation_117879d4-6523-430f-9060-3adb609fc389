package com.yyigou.ddc.dmp.model.req.comparemodel;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelChangeStatusReq implements Serializable {

    /**
     * 模型编号
     */
    @NotBlank(message = "模型编号不能为空")
    private String modelNo;

    /**
     * 启用状态
     */
    @NotBlank(message = "启用状态不能为空")
    private Integer enableStatus;


    @NotBlank(message = "租户编号不能为空")
    private String enterpriseNo;



}
