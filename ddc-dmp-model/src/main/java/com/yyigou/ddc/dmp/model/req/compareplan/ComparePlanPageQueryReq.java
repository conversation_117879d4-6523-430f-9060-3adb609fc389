package com.yyigou.ddc.dmp.model.req.compareplan;

import com.yyigou.ddc.dmp.model.req.PageQueryReq;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanPageQueryReq extends PageQueryReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 方案编码
     */
    private String planCode;

    /**
     * 比价方案名称
     */
    private String planName;

    /**
     * 启用状态 1-启用，2-禁用
     */
    private Integer enableStatus;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空")
    private String enterpriseNo;

}
