package com.yyigou.ddc.dmp.model.req.comparemodel;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelSaveReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    private String enterpriseNo;

    /**
     * 更新场景必传
     */
    private String modelNo;

    /**
     * 模型名称
     */
    @NotBlank(message = "模型名称不能为空")
    @Length(max = 100, message = "模型名称不能超过100")
    private String modelName;

    /**
     * 模型编码
     */
    @NotBlank(message = "模型编码不能为空")
    @Length(max = 32, message = "模型编码长度不能超过32")
    private String modelCode;

    /**
     * 描述信息
     */
    @NotBlank(message = "描述信息不能为空")
    @Length(max = 255, message = "描述信息长度不能超过255")
    private String description;

    /**
     * 启用状态
     */
    private Integer enableStatus;

    /**
     * 比对数据集编号
     */
    @NotBlank(message = "比对数据集编号不能为空")
    private String compareDatasetNo;

    /**
     * 基准数据集集合
     */
    @Valid
    private List<CompareModelRefDatasetReq> baselineDatasetList;

    /**
     * 比对对象字段集合
     */
    @Valid
    @NotEmpty(message = "比对对象不能为空")
    private List<CompareModelRefFieldReq> compareObjectList;

    /**
     * 比对维度字段集合
     */
    @Valid
    @NotEmpty(message = "比对维度不能为空")
    private List<CompareModelRefFieldReq> compareDimList;

    /**
     * 比对指标集合
     */
    @Valid
    @NotEmpty(message = "比对指标不能为空")
    private List<CompareModelRefMetricReq> compareMetricList;

    /**
     * 比对数据集查询条件集合
     */
    @Valid
    private List<CompareModelRefQueryConditionReq> datasetQueryConditionList;

    /**
     * 基准指标集合
     */
    @Valid
    private List<CompareModelRefBaselineMetricReq> baselineMetricList;

    /**
     * 计算指标集合
     */
    @Valid
    private List<CompareModelRefCalculatedMetricReq> calculatedMetricList;



}
