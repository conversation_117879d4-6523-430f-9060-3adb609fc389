package com.yyigou.ddc.dmp.model.res.comparemodel;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class CompareModelRefMetricRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    private String compareModelMetricNo;

    /**
     * 指标编号
     */
    private String metricNo;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 指标编码
     */
    private String metricCode;

    /**
     * 关联指标类型。1-比对指标，2-基准指标
     */
    private Integer metricType;

    /**
     * 排序
     */
    private Integer sort;
}
