package com.yyigou.ddc.dmp.model.bo.reporttemplate;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
public class ReportTemplateFieldBO {

    /**
     * 模型字段唯一标识
     */
    private String no;

    /**
     * 维度字段编码
     */
    private String field;


    /**
     * 字段别名
     */
    private String alias;

    /**
     * 字段来源的模型编号
     */
    private String modelNo;


    /**
     * 维度字段展示名称
     */
    private String name;


    /**
     * 字段类型。
     * 1-普通字段，2-虚拟字段（不参与sql），3-固定分组
     */
    private Integer fieldType;


    /**
     * 是否隐藏。
     * 0-不隐藏，1-可显示，但默认隐藏，2-始终隐藏
     */
    private Integer hiddenType;

    /**
     * 表达式。默认值是后端返回的，但有些维度的值是前端计算的，类似MySQL中的窗口函数。
     * 如：DIMENSION_NUMBER ：所有（行+列）显示维度分组后的序号(如果是有refFieldNo的隐藏维度，则是否参与分组由refFieldNo的维度决定)
     */
    private String expression;

//    /**
//     * 关联字段。比如隐藏系统类型维度-产品编码，本质是跟着产品名称走的
//     * 如果使用了关联字段，则该字段不可独立移动
//     */
//    private String referFieldNo;

    /**
     * 隐藏维度。比如存货分类名称维度可以配置一个隐藏维度-存货分类编号，隐藏维度和父维度一起生效。只是这个维度是隐藏的。
     */
    private ReportTemplateFieldBO hiddenField;

    /**
     * 是否可移动.比如一些价格指标，不允许用户移动到行、列维度中
     */
    private Boolean canMove;


    /**
     * 自定义树中父级节点
     */
    private String parentNo;

    /**
     * 描述信息
     */
    private String description;

}
