package com.yyigou.ddc.dmp.model.req.compareplan;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanGetReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案编号
     */
    @NotBlank(message = "方案编号不能为空")
    private String planNo;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空")
    private String enterpriseNo;
}
