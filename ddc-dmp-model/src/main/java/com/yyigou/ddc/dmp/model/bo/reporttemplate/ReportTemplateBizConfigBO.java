package com.yyigou.ddc.dmp.model.bo.reporttemplate;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
public class ReportTemplateBizConfigBO {

    /**
     * 0- 不用rowTree，1- 使用rowTree，2-使用columnTree
     */
    private Integer treeType;

    /**
     * 行维度
     */
    private List<ReportTemplateFieldBO> rowFieldList;

    /**
     * 列维度
     */
    private List<ReportTemplateFieldBO> columnFieldList;

    /**
     * 值维度
     */
    private List<ReportTemplateFieldBO> valueFieldList;

    /**
     * 指标虚拟维度列展示成列维度，还是行维度。antv配置
     */
    private Boolean valueInCols;

    /**
     * 自定义指标维度在行列头中的层级顺序 （即 values 的 顺序，从 0 开始）
     */
    private Integer customValueOrder;

    /**
     * 用于行维度固定的场景
     */
    private List<ReportTemplateCustomTreeBO> rowTree;

    /**
     * 用于列维度固定的场景
     */
    private List<ReportTemplateCustomTreeBO> columnTree;



}
