package com.yyigou.ddc.dmp.model.req.compareplan;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanRefCalculatedMetricReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    private String comparePlanCalMetricNo;


    /**
     * 模型计算指标编号
     */
    @NotBlank(message = "模型计算指标编号不能为空")
    private String compareModelCalMetricNo;

    /**
     * 基准指标编码
     */
    @NotBlank(message = "基准指标编号不能为空")
    private String baselineMetricNo;

    /**
     * 比对指标编码
     */
    @NotBlank(message = "比对指标编号不能为空")
    private String compareMetricNo;

    /**
     * 是否选中
     */
    private Boolean selected;
}
