package com.yyigou.ddc.dmp.model.req.compareplan;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanRefFieldReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 字段来源类型。1-比对对象，2-比对维度，3-比对指标，4-基准指标，5-计算指标
     */
    @NotNull(message = "字段来源类型不能为空")
    @Max(value = 5, message = "字段来源类型不正确")
    @Min(value = 1, message = "字段来源类型不正确")
    private Integer sourceType;

    /**
     * 比价方案指标唯一标识
     */
    private String comparePlanMetricNo;

    /**
     * 比价方案计算指标唯一标识
     */
    private String comparePlanCalMetricNo;

    /**
     * 模型字段唯一标识
     */
    private String compareModelDimNo;
}
