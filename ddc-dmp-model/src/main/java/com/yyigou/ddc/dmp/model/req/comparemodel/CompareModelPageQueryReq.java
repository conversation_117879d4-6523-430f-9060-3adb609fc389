package com.yyigou.ddc.dmp.model.req.comparemodel;

import com.yyigou.ddc.dmp.model.req.PageQueryReq;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelPageQueryReq extends PageQueryReq implements Serializable {

    @NotBlank(message = "租户编号不能为空")
    private String enterpriseNo;


    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 启用状态
     */
    private Integer enableStatus;

}
