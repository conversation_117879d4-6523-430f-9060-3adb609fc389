package com.yyigou.ddc.dmp.model.res.compareplan;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanRefCalculatedMetricRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    private String comparePlanCalMetricNo;

    /**
     * 计算指标编号
     */
    private String compareModelCalMetricNo;

    /**
     * 是否选中
     */
    private Boolean selected;

    /**
     * 计算指标编码
     */
    private String calMetricCode;

    /**
     * 计算指标名称
     */
    private String calMetricName;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 基准指标编号
     */
    private String baselineMetricNo;


    /**
     * 比对指标编号
     */
    private String compareMetricNo;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 单位编码。NONE-无格式，YUAN-元
     */
    private String unitCode;
}
