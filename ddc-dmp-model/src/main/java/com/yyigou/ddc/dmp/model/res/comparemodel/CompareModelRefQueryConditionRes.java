package com.yyigou.ddc.dmp.model.res.comparemodel;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelRefQueryConditionRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识，用于更新
     */
    private String compareModelQueryConditionNo;

    /**
     * 查询条件
     */
    private String datasetQueryConditionNo;

    /**
     * 查询条件名称
     */
    private String datasetQueryConditionName;

    /**
     * 是否是基准指标的公共查询条件
     */
    private Integer isCommon = 0;
}
