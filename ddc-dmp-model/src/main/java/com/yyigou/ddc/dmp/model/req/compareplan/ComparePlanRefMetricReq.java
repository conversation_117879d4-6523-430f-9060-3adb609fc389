package com.yyigou.ddc.dmp.model.req.compareplan;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanRefMetricReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    private String comparePlanMetricNo;



    /**
     * 比对模型的指标编号
     */
    @NotBlank(message = "比对模型指标编号不能为空")
    private String compareModelMetricNo;

    /**
     * 校验过程中赋值
     */
    private Integer metricType;

    /**
     * 是否选中
     */
    private Boolean selected;
}
