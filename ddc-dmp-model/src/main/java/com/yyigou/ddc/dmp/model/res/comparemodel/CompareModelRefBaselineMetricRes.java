package com.yyigou.ddc.dmp.model.res.comparemodel;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelRefBaselineMetricRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    private String compareModelMetricNo;

    /**
     * 基准指标编号
     */
    private String metricNo;

    /**
     * 基准指标名称
     */
    private String metricName;

    /**
     * 基准指标编码
     */
    private String metricCode;

    /**
     * 基准指标所属数据集编号
     */
    private String datasetNo;

    /**
     * 基准指标所属数据集名称
     */
    private String datasetName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 基准指标所属数据集查询条件集合
     */
    private List<CompareModelRefQueryConditionRes> datasetQueryConditionList;
}
