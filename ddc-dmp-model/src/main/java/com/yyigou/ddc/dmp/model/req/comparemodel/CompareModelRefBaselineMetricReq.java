package com.yyigou.ddc.dmp.model.req.comparemodel;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefBaselineMetricReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    private String compareModelMetricNo;

    /**
     * 基准指标编号
     */
    @NotBlank(message = "基准指标编号不能为空")
    @Length(max = 64, message = "基准指标编号长度不能超过64")
    private String metricNo;

    /**
     * 基准指标所属数据集编号
     */
    @NotBlank(message = "基准指标所属数据集编号不能为空")
    @Length(max = 64, message = "基准指标所属数据集编号长度不能超过64")
    private String datasetNo;

    /**
     * 基准指标所属数据集查询条件集合
     */
    private List<CompareModelRefQueryConditionReq> datasetQueryConditionList;
}
