package com.yyigou.ddc.dmp.model.res.reporttemplate;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
public class ReportTemplatePageRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 唯一编号
     */
    private String templateNo;

    /**
     * 租户id
     */
    private String enterpriseNo;

    /**
     * 租户记录平台复制时的来源报表编号
     */
    private String sourceTemplateNo;

    /**
     * 报表编码，系统生成，升级版本时该字段不变
     */
    private String templateCode;

    /**
     * 报表名称
     */
    private String templateName;

    /**
     * 报表来源模式。1-独立报表，2-比价方案
     */
    private Integer templateMode;

    /**
     * 数据集编号
     */
    private String datasetNo;

    /**
     * 比价方案编号
     */
    private String planNo;

    /**
     * 行配置
     */
    private String rowFields;

    /**
     * 列配置
     */
    private String columnFields;

    /**
     * 指标配置
     */
    private String valueFields;

    /**
     * 报表业务配置
     */
    private String bizConfig;

    /**
     * 数据筛选条件
     */
    private String filterConfig;

    /**
     * antv渲染配置信息
     */
    private String configJson;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    private Integer status;

    /**
     * 启用状态 1-启用，2-禁用
     */
    private Integer enableStatus;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 操作人编号
     */
    private String modifyNo;

    /**
     * 操作人名称
     */
    private String modifyName;

    /**
     * 操作时间
     */
    private String modifyTime;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否平台预置 0-否，1-是
     */
    private Integer preset;


}
