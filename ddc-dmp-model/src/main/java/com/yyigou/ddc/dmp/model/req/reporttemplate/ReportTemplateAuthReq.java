package com.yyigou.ddc.dmp.model.req.reporttemplate;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateAuthReq {

    /**
     * 租户id
     */
    @NotNull(message = "租户编号不能为空")
    private String enterpriseNo;

    @NotNull(message = "报表模板编码不能为空")
    @Length(max = 32, message = "报表模板编码长度不能超过32")
    private String templateCode;


    /**
     * 被授权人的用户编号
     */
    @NotNull(message = "被授权人的用户编号不能为空")
    private String authToUserNo;


    private String authToUserName;

    /**
     * 被授权的组织编号集合
     */
    private List<String> authOrgNoList;




}
