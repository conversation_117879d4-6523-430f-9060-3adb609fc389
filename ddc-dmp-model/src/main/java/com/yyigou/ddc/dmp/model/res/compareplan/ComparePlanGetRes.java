package com.yyigou.ddc.dmp.model.res.compareplan;

import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelRefFieldRes;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanGetRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案编号
     */
    private String planNo;

    /**
     * 模型编号
     */
    private String compareModelNo;

    /**
     * 方案编码
     */
    private String planCode;

    /**
     * 比价方案名称
     */
    private String planName;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 启用状态 1-启用，2-禁用
     */
    private Integer enableStatus;

    private String enterpriseNo;


    /**
     * 比价指标
     */
    private List<ComparePlanRefMetricRes> compareMetricList;

    /**
     * 基准指标
     */
    private List<ComparePlanRefMetricRes> baselineMetricList;

    /**
     * 计算指标集合
     */
    private List<ComparePlanRefCalculatedMetricRes> calculatedMetricList;

    /**
     * 报表行维度集合
     */
    private List<ComparePlanRefFieldRes> rowList;

    /**
     * 报表列维度集合
     */
    private List<ComparePlanRefFieldRes> columnList;

    /**
     * 报表值维度集合
     */
    private List<ComparePlanRefFieldRes> valueList;


    /**
     * 比对对象字段集合
     */
    private List<CompareModelRefFieldRes> compareObjectList;

    /**
     * 比对维度字段集合
     */
    private List<CompareModelRefFieldRes> compareDimList;
}
