package com.yyigou.ddc.dmp.model.req.dimension;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class DimensionFieldSaveReq implements Serializable {
    private String enterpriseNo;
    private String dimensionNo;
    private String dimensionCode;
    private String dimensionName;
    private String schemaName;
    private String tableName;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}
