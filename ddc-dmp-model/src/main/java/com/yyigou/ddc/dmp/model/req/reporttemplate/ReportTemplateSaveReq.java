package com.yyigou.ddc.dmp.model.req.reporttemplate;

import com.yyigou.ddc.dmp.model.bo.reporttemplate.ReportTemplateBizConfigBO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateSaveReq {


    /**
     * 租户id
     */
    @NotNull(message = "租户编号不能为空")
    private String enterpriseNo;

    /**
     * 租户记录平台复制时的来源报表编号
     */
    private String sourceTemplateNo;


    /**
     * 报表模板名称
     */
    @NotNull(message = "报表模板名称不能为空")
    @Length(max = 100, message = "报表模板名称长度不能超过100")
    private String templateName;

    /**
     * 报表来源模式。1-独立报表，2-比价方案
     */
    private Integer templateMode;

    /**
     * 数据集编号
     */
    @NotNull(message = "数据集不能为空")
    @Length(max = 32, message = "数据集编号长度不能超过32")
    private String datasetNo;

    /**
     * 比价方案编号
     */
    private String planNo;



    private ReportTemplateBizConfigBO bizConfigBO;

    /**
     * 数据筛选条件
     */
    private String filterConfig;

    /**
     * antv渲染配置信息
     */
    private String configJson;

    /**
     * 描述信息
     */
    @Length(max = 255 ,message = "描述信息长度不能超过255")
    private String description;

    /**
     * 启用状态 1-启用，2-禁用
     */
    private Integer enableStatus;


}
