package com.yyigou.ddc.dmp.model.res.comparemodel;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelRefCalculatedMetricRes implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 计算指标编号。用于编辑场景
     */
    private String calMetricNo;

    /**
     * 计算指标编码
     */
    private String calMetricCode;

    /**
     * 计算指标名称
     */
    private String calMetricName;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 绑定的基准指标范围类型.1-全部，2-指定
     */
    private Integer baselineMetricScopeType;

    /**
     * 指定的基准指标
     */
    private List<String> baselineMetricScope;

    /**
     * 绑定的比对指标范围类型.1-全部，2-指定
     */
    private Integer compareMetricScopeType;

    /**
     * 指定的比对指标
     */
    private List<String> compareMetricScope;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 格式化配置
     */
    private CalculatedMetricFormatConfigRes formatConfig;
}
