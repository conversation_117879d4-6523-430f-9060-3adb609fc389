package com.yyigou.ddc.dmp.model.req.compareplan;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanSaveReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案编号
     */
    private String planNo;

    /**
     * 模型编号
     */
    @NotBlank(message = "模型编号不能为空")
    private String compareModelNo;

    /**
     * 方案编码
     */
    @NotBlank(message = "方案编码不能为空")
    @Length(max = 32, message = "方案编码长度不能超过32")
    private String planCode;

    /**
     * 比价方案名称
     */
    @NotBlank(message = "方案名称不能为空")
    @Length(max = 100, message = "方案名称长度不能超过100")
    private String planName;

    /**
     * 描述信息
     */
    @Length(max = 255, message = "描述信息长度不能超过255")
    private String description;

    /**
     * 启用状态 1-启用，2-禁用
     */
    private Integer enableStatus;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空")
    private String enterpriseNo;

    /**
     * 比价指标
     */
    @Valid
    @NotEmpty(message = "比价指标不能为空")
    private List<ComparePlanRefMetricReq> compareMetricList;

    /**
     * 基准指标
     */
    @Valid
    private List<ComparePlanRefMetricReq> baselineMetricList;

    /**
     * 计算指标集合
     */
    @Valid
    private List<ComparePlanRefCalculatedMetricReq> calculatedMetricList;

    /**
     * 报表行维度集合
     */
    @Valid
    @NotEmpty(message = "报表行维度不能为空")
    private List<ComparePlanRefFieldReq> rowList;

    /**
     * 报表列维度集合
     */
    @Valid
    @NotEmpty(message = "报表列维度不能为空")
    private List<ComparePlanRefFieldReq> columnList;

    /**
     * 报表值维度集合
     */
    @Valid
    @NotEmpty(message = "报表值维度不能为空")
    private List<ComparePlanRefFieldReq> valueList;
}
