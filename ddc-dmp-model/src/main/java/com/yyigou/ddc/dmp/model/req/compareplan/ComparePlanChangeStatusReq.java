package com.yyigou.ddc.dmp.model.req.compareplan;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanChangeStatusReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案编号
     */
    @NotBlank(message = "方案编号不能为空")
    private String planNo;

    /**
     * 启用状态 1-启用，2-禁用
     */
    @NotNull(message = "启用状态不能为空")
    private Integer enableStatus;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空")
    private String enterpriseNo;
}
