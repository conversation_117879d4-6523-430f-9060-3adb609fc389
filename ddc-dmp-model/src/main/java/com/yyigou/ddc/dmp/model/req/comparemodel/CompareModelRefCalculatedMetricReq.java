package com.yyigou.ddc.dmp.model.req.comparemodel;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefCalculatedMetricReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 计算指标编号。用于编辑场景
     */
    private String calMetricNo;

    /**
     * 计算指标编码
     */
    @NotBlank(message = "计算指标编码不能为空")
    @Length(max = 64, message = "计算指标编码长度不能超过64")
    private String calMetricCode;

    /**
     * 计算指标名称
     */
    @NotBlank(message = "计算指标名称不能为空")
    @Length(max = 64, message = "计算指标名称长度不能超过64")
    private String calMetricName;

    /**
     * 描述信息
     */
    @NotBlank(message = "描述信息不能为空")
    @Length(max = 255, message = "描述信息长度不能超过64")
    private String description;

    /**
     * 绑定的基准指标范围类型.1-全部，2-指定
     */
    @Max( value = 2, message = "绑定的基准指标范围类型值不正确")
    @Min( value = 1, message = "绑定的基准指标范围类型值不正确")
    private Integer baselineMetricScopeType;

    /**
     * 指定的基准指标
     */
    private List<String> baselineMetricScope;

    /**
     * 绑定的比对指标范围类型.1-全部，2-指定
     */
    @Max( value = 2, message = "绑定的比对指标范围类型值不正确")
    @Min( value = 1, message = "绑定的比对指标范围类型值不正确")
    private Integer compareMetricScopeType;

    /**
     * 指定的比对指标
     */
    private List<String> compareMetricScope;

    /**
     * 表达式
     */
    @NotBlank(message = "表达式不能为空")
    @Length(max = 255, message = "表达式长度不能超过64")
    private String expression;

    /**
     * 格式化配置
     */
    @NotNull(message = "格式化配置不能为空")
    private CalculatedMetricFormatConfigReq formatConfig;



}
