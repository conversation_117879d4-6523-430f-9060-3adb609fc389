package com.yyigou.ddc.dmp.model.req.comparemodel;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefFieldReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    private String compareModelDimNo;

    /**
     * 字段编码
     */
    @NotBlank(message = "数据集字段编码不能为空")
    private String fieldCode;

}
