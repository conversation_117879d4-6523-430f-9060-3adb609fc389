# 比价方案第一阶段项目说明

## 项目结构

```
/Users/<USER>/Documents/project/company/cloudyigou/service-ddc-dmp/
├── ddc-dmp-common/        # 公共模块
├── ddc-dmp-dao/           # 数据访问层
├── ddc-dmp-service/       # 业务逻辑层
├── ddc-dmp-web/           # 控制器层
├── pom.xml                # 项目父POM
└── 比价方案第一阶段设计.md  # 设计文档
```

## 模块用途

### 1. ddc-dmp-common

- 存放公共工具类、常量定义
- 提供全局异常处理
- 封装通用返回结果

### 2. ddc-dmp-dao

- 实体类定义（Entity）
- MyBatis Mapper接口及XML文件
- 数据库访问相关配置

### 3. ddc-dmp-service

- 业务逻辑接口（Service）
- 业务逻辑实现（ServiceImpl）
- 事务管理

### 4. ddc-dmp-web

- RESTful API控制器
- 请求参数验证
- 接口文档（如需集成Swagger）

## 技术栈及版本

| 框架/组件           | 版本      | 说明       |
|-----------------|---------|----------|
| JDK             | 21      | jdk版本    |
| Spring Boot     | 3.5.3   | 基础框架     |
| MyBatis-Plus    | 3.5.12  | ORM框架    |
| Lombok          | 1.18.30 | 简化Java代码 |
| MySQL Connector | 8.0.28  | 数据库驱动    |
| Spring Web      | 6.2.8   | Web开发    |
| hutool          | 5.8.25  | 工具类      |

## 快速启动

1. 配置数据库连接信息
2. 执行SQL脚本创建表结构
3. 运行`mvn clean install`构建项目
4. 启动ddc-dmp-web模块的Application类

## 注意事项

- 所有实体类使用Lombok注解简化代码
- 数据库表名与实体类名通过@TableName注解映射
- 接口返回统一使用ResultBean封装