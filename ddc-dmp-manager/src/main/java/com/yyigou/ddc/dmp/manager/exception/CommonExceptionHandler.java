package com.yyigou.ddc.dmp.manager.exception;

import com.yyigou.ddc.common.exception.APIErrorCode;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.threadlocal.CommonThreadLocalUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;

/**
 * 通用的异常处理, 主要处理BusinessException跟其他异常
 */
public class CommonExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(CommonExceptionHandler.class);
    //用于隐藏异常细节, 用于前端返回信息
    private static final String simeplizedMessage = "后端处理异常";
    // save it static to have it available on every call
//    private static Method m;

    static {
//        try {
//            m = Throwable.class.getDeclaredMethod("getStackTraceElement",
//                    int.class);
//            m.setAccessible(true);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 高性能的获取调用方法名的实现 (m已经被缓存)
     *
     * @param depth: 0, current method name, 1, invoker method name
     * @return 方法名
     */
    public static String getInvokerInfo(final int depth) {
        try {
            StackTraceElement[] stackTrace = new Throwable().getStackTrace();
            if (stackTrace.length > depth + 1) {
                StackTraceElement element = stackTrace[depth + 1];
                return element.getClassName() + "." + element.getMethodName();
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 处理异常的通用方法, 主要用于dubbo provider端
     * 参数不含有非业务异常的候选信息, 如果费业务异常 统一返回 14300, 服务异常
     *
     * @param e   异常对象
     * @param <E>
     * @return
     */
    public static <E> CallResult<E> handleException(Exception e) {
        String invokerName = getInvokerInfo(1); // get invokerName who call handleException
        String msg = String.format("调用%s方法异常: ", invokerName);
        return doHandle(e, APIErrorCode.API_ERROR, msg);
    }

    /**
     * 处理异常的通用方法, 主要用于dubbo provider端
     * <p>
     * 自定义非业务异常errorCode, 用于备选异常编码
     *
     * @param e         异常对象
     * @param errorCode 异常对象中可能没有errorCode, 这里设置, 用于封装CallResult
     * @param <E>
     * @return
     */
    public static <E> CallResult<E> handleException(Exception e, String errorCode) {
        String invokerName = getInvokerInfo(1); // get invokerName who call handleException
        String msg = String.format("调用%s方法异常: ", invokerName);
        return doHandle(e, errorCode, msg);
    }

    /**
     * 处理异常的通用方法, 主要用于dubbo provider端
     * <p>
     * 自定义非业务异常errorCode, 备选异常编码, 用于前端显示
     * errorMessage 用于后端日志记录
     *
     * @param e         异常对象
     * @param errorCode 异常对象中可能没有errorCode, 这里设置, 用于封装CallResult
     * @param <E>
     * @return
     */
    public static <E> CallResult<E> handleException(Exception e, String errorCode, String errorMessage) {
        String invokerName = getInvokerInfo(1); // get invokerName who call handleException
        String msg = String.format("调用%s方法异常: %s ", invokerName, errorMessage);
        return doHandle(e, errorCode, msg);
    }

    /**
     * 异常处理的最终逻辑
     *
     * @param e
     * @param errorCode
     * @param errorMessage
     * @param <E>
     * @return
     */
    private static <E> CallResult<E> doHandle(Exception e, String errorCode, String errorMessage) {
        log.error(errorMessage, e);
        if (e instanceof BusinessException) {
            return CallResult.failed(((BusinessException) e).getCode(), e.getMessage(), null);
        } else {
            String detailMessage = StringUtils.isEmpty(e.getMessage()) ? simeplizedMessage : e.getMessage();
            boolean simple = needSimplifyErrorMessage(e);
            // 删前校验的异常命中这里
            if(e instanceof DataAccessException && !simple){ // 是数据异常, 但是不要简单显示 (特殊场景, 比如删前校验)
                detailMessage = e.getCause().getMessage();
                detailMessage = extractMybatisSystemExceptionMsg(detailMessage);
                return CallResult.failed(errorCode, detailMessage);
            }
            // 其他的异常走这里
            return CallResult.failed(errorCode, simple ? simeplizedMessage : detailMessage, null);
        }
    }

    /**
     * 根据异常类型来判断是否需要简化异常信息, 主要用于敏感信息泄露, 比如数据库表结构
     *
     * @param e
     * @return
     */
    private static boolean needSimplifyErrorMessage(Exception e) {
        if (e instanceof DataAccessException) {
            // 虽然是mybatis的异常, 但是有特例, 要返回给前端这个异常的信息
            Boolean myBatisExceptionEnable = CommonThreadLocalUtil.getMyBatisExceptionEnable();
            if(myBatisExceptionEnable != null && myBatisExceptionEnable){ // mybatis拦截器捕获异常, 但是不要简化异常
                return false;
            }
            return true;
        } else {
            // add other exception here
        }
        return false;
    }


    /**
     * 提取mybatis异常的核心信息, 去掉caused by
     * @param msg
     * @return
     */
    public static String extractMybatisSystemExceptionMsg(String msg) {
        if(StringUtils.isEmpty(msg)){ // 无需处理
            return msg;
        }
        // 找到最后个业务异常提升
        String keyword = "com.yyigou.ddc.common.exception.BusinessException: ";
        int i = msg.lastIndexOf(keyword);
        if(i > 0){
            return msg.substring(i+keyword.length());
        }
        return msg;
    }
}
