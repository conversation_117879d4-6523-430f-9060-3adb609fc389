package com.yyigou.ddc.dmp.manager.integration.log;

import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.dlog.dto.DLogLevel;
import com.yyigou.ddc.services.dlog.dto.ulog.ULogBusinessLogDTO;
import com.yyigou.ddc.services.dlog.dto.ulog.ULogDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Component
@Slf4j
public class BusinessLogService {

    public String clientName;
    public String localIPAddress;

    @Resource
    private KafkaTemplate<Object, Object> kafkaTemplate;

    public BusinessLogService() {
        String application = System.getProperty("user.dir");
        String applicationName = null;
        if (application != null) {
            File thisFile = new File(application);
            applicationName = thisFile.getName();
            if (applicationName.contains("-")) {
                applicationName = applicationName.replaceAll("-", "_");
            }
        }

        this.clientName = applicationName;

        try {
            InetAddress localhost = InetAddress.getLocalHost();
            this.localIPAddress = localhost.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }

    }

    public void saveLog(String logLv, String businessType, String businessKey, String businessContext, String msg, String businessValue, String businessRemark) {
        ULogBusinessLogDTO logDTO = new ULogBusinessLogDTO();
        logDTO.fromSessionUser(ServiceBaseAbstract.currentRequest().getSession());
        logDTO.setLogLevel(logLv);
        logDTO.setBusinessType(businessType); // 业务类型
        logDTO.setBusinessKey(businessKey); // 业务流水号
        logDTO.setBusinessContext(businessContext); // 业务操作说明
        logDTO.setMessage(msg); // 错误信息, 如果错误日志, 建议填写
        logDTO.setBusinessValue(businessValue); // 业务数据, 可选, 为方便分析建议填写, 这里填写入参对象
        logDTO.setBusinessRemark(businessRemark); // 业务备注, 可选, 预留给业务侧自定义
        this.saveLog(logDTO);
    }


    public void saveLog(ULogBusinessLogDTO logDTO) {
        if (StringUtils.isEmpty(logDTO.getLogLevel())) {
            logDTO.setLogLevel(DLogLevel.INFO.name());
        }

        ValidatorUtils.checkEmptyThrowEx(logDTO.getEnterpriseNo(), "调用方租户编码必填");
        ValidatorUtils.checkEmptyThrowEx(logDTO.getBusinessType(), "业务单据类型必填");
        ValidatorUtils.checkEmptyThrowEx(logDTO.getBusinessKey(), "业务关键单据号必填");
        ValidatorUtils.checkEmptyThrowEx(logDTO.getBusinessContext(), "业务操作上下文必填");
        ValidatorUtils.checkEmptyThrowEx(logDTO.getLogLevel(), "日志级别必填");
        if (!logDTO.getBusinessKey().contains(",") && !logDTO.getBusinessKey().contains(";")) {
            this.saveLog(logDTO, "business_log", "后端业务日志");
        } else {
            if (log.isInfoEnabled()) {
                log.info("业务关键字不支持多个,业务关键字类型:{}, 客户端{}", logDTO.getBusinessType(), logDTO.getClientName() == null ? "" : logDTO.getClientName());
            }

        }
    }

    void saveLog(ULogDTO logDTO, String topic, String logName) {
        if (StringUtils.isEmpty(logDTO.getClientName())) {
            logDTO.setClientName(this.clientName);
        }

        if (StringUtils.isEmpty(logDTO.getIp())) {
            logDTO.setIp(this.localIPAddress);
        }

        try {
            this.kafkaTemplate.send(topic, logDTO).get();
        } catch (Exception e) {
            log.error("发送{}: 到kafka topic: {}失败, 日志内容为: {}, 失败原因为: {}", new Object[]{logName, topic, JSONObject.toJSONString(logDTO), e.getMessage()});
        }

    }

}
