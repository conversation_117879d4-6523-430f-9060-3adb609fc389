package com.yyigou.ddc.dmp.manager.integration.registry.zookeeper;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.URL;
import com.alibaba.dubbo.common.logger.Logger;
import com.alibaba.dubbo.common.logger.LoggerFactory;
import com.alibaba.dubbo.common.utils.ClassHelper;
import com.alibaba.dubbo.common.utils.ConcurrentHashSet;
import com.alibaba.dubbo.common.utils.UrlUtils;
import com.alibaba.dubbo.config.spring.extension.SpringExtensionFactory;
import com.alibaba.dubbo.registry.NotifyListener;
import com.alibaba.dubbo.registry.support.FailbackRegistry;
import com.alibaba.dubbo.remoting.zookeeper.ChildListener;
import com.alibaba.dubbo.remoting.zookeeper.StateListener;
import com.alibaba.dubbo.remoting.zookeeper.ZookeeperClient;
import com.alibaba.dubbo.remoting.zookeeper.ZookeeperTransporter;
import com.alibaba.dubbo.rpc.RpcException;
import com.google.common.collect.Maps;
import com.yyigou.ddc.common.dubbo.openapi.OpenapiConfiguration;
import com.yyigou.ddc.common.dubbo.register.entry.*;
import com.yyigou.ddc.common.dubbo.register.exception.DuplicateAPIAliasException;
import com.yyigou.ddc.common.dubbo.registry.zookeeper.APIDocumentConfig;
import com.yyigou.ddc.common.dubbo.registry.zookeeper.SgpServiceInfoSerializer;
import com.yyigou.ddc.common.service.annotation.Entity;
import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Parameter;
import com.yyigou.ddc.common.util.ObjectUtil;
import com.yyigou.ddc.common.util.ValidatorUtils;
import javassist.Modifier;
import lombok.extern.slf4j.Slf4j;
import org.I0Itec.zkclient.ZkClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.util.ClassUtils;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.net.InetAddress;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ZookeeperRegistry
 *
 * <AUTHOR>
 */
@Slf4j
public class ZookeeperRegistryInDev extends FailbackRegistry {

    private final static Logger logger = LoggerFactory.getLogger(ZookeeperRegistryInDev.class);

    private final static int DEFAULT_ZOOKEEPER_PORT = 2181;

    private final static String DEFAULT_ROOT = "dubbo";

    /**
     * 受解析支持的包名前
     */
    private final static List<String> TARGET_PACKAGE_PREFIX = new ArrayList<String>();
    /**
     * 日志锚点
     */
    private final static String LOGNAME = "com.yyigou.ddc.common.dubbo";
    /**
     * SOP平台服务信息的根跟径
     */
    private final static String SGP_SERVICE_INFO_ROOT = "/ddc-sgp-cache";
    /**
     * SGP类信息(APIDOC使用)
     */
    private final static String SGP_SERVICE_OBJECT_INFO_ROOT = "/ddc-sgp-object-cache";

    static {
        TARGET_PACKAGE_PREFIX.add("com.yyigou.");
//        TARGET_PACKAGE_PREFIX.add("com.slip.");
    }

    private final String root;
    private final Set<String> anyServices = new ConcurrentHashSet<String>();
    private final ConcurrentMap<URL, ConcurrentMap<NotifyListener, ChildListener>> zkListeners =
            new ConcurrentHashMap<URL, ConcurrentMap<NotifyListener, ChildListener>>();
    /**
     * dubbo 专用 zk 客户端
     */
    private final ZookeeperClient zkClient;
    private final List<String> baseTypes = Arrays.asList(new String[]{"int", "boolean", "float", "double", "short",
            "long", "byte", "char", "string", "java.lang.Boolean", "java.lang.Float", "java.lang.Double",
            "java.lang.Short", "java.lang.Long", "java.lang.String", "java.lang.Integer", "java.math.BigDecimal"});

    /**
     * 注册 sgp cache 信息专用客户端
     */
    private ZkClient zkClient4SgpInfoReg;
//    private LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();

    private StandardReflectionParameterNameDiscoverer discoverer = new StandardReflectionParameterNameDiscoverer();
    /**
     * 构造器
     *
     * @param url
     * @param zookeeperTransporter
     */
    public ZookeeperRegistryInDev(URL url, ZookeeperTransporter zookeeperTransporter) {
        super(url);
        if (url.isAnyHost()) {
            throw new IllegalStateException("registry address == null");
        }
        String group = url.getParameter(Constants.GROUP_KEY, DEFAULT_ROOT);
        if (!group.startsWith(Constants.PATH_SEPARATOR)) {
            group = Constants.PATH_SEPARATOR + group;
        }
        this.root = group;
        zkClient = zookeeperTransporter.connect(url);
        zkClient.addStateListener(new StateListener() {
            @Override
            public void stateChanged(int state) {
                if (state == RECONNECTED) {
                    try {
                        recover();
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                }
            }
        });

    }

    /**
     * 是否受解析支持的包名
     *
     * @param targetPackage 包名前辍
     * @return
     */
    public static boolean isTargetPackage(String targetPackage) {

        for (String prefix : TARGET_PACKAGE_PREFIX) {
            if (targetPackage.startsWith(prefix)) {
                return true;
            } else {
                return false;
            }
        }

        return false;
    }

    static String appendDefaultPort(String address) {
        if (address != null && address.length() > 0) {
            int i = address.indexOf(':');
            if (i < 0) {
                return address + ":" + DEFAULT_ZOOKEEPER_PORT;
            } else if (Integer.parseInt(address.substring(i + 1)) == 0) {
                return address.substring(0, i + 1) + DEFAULT_ZOOKEEPER_PORT;
            }
        }
        return address;
    }

    private static String convertZkUrl(String zkUrl) {
        if (!StringUtils.isEmpty(zkUrl)) {
            if (zkUrl.contains("zookeeper://")) {
                zkUrl = zkUrl.replace("zookeeper://", "");
            }
            if (zkUrl.contains("?backup=")) {
                zkUrl = zkUrl.replace("?backup=", ",");
            }
        }
        return zkUrl;
    }

    /**
     * 遍历所有可能的spring context获取bean
     *
     * @param beanName
     * @return
     */
    private Object getBean(String beanName) {
        Set<ApplicationContext> applicationContexts = SpringExtensionFactory.getContexts();

        for (ApplicationContext applicationContext : applicationContexts) {
            if (applicationContext.containsBean(beanName)) {
                return applicationContext.getBean(beanName);
            }
        }
        return null;

    }

    /**
     * 遍历所有可能的spring context获取bean
     *
     * @param cls
     * @return
     */
    private Object getBean(Class<?> cls) {
        Set<ApplicationContext> applicationContexts = SpringExtensionFactory.getContexts();

        for (ApplicationContext applicationContext : applicationContexts) {
            try {
                Object obj = applicationContext.getBean(cls);
                return obj;
            } catch (NoSuchBeanDefinitionException ignored) {
            }
        }
        return null;
    }

    @Override
    public void destroy() {
        super.destroy();
        try {
            // 关闭所有的zk客户端
            if (zkClient != null) {
                zkClient.close();
            }
            if (zkClient4SgpInfoReg != null) {
                zkClient4SgpInfoReg.close();
            }

        } catch (Exception e) {
            logger.warn("Failed to close zookeeper client " + getUrl() + ", cause: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean isAvailable() {
        return zkClient.isConnected();
    }

    /**
     * 改写后的注册服务逻辑
     *
     * @param url
     */
    @Override
    protected void doRegister(URL url) {
        try {
            boolean ephemeral = url.getParameter(Constants.DYNAMIC_KEY, true);

            zkClient.create(toUrlPath(url), ephemeral);

            if (!url.getProtocol().equals("consumer")) {
                String interfaceName = url.getParameter(Constants.INTERFACE_KEY, "");


                APIDocumentConfig config = (APIDocumentConfig) getBean("apiDocumentConfig");
                if (config != null && config.getIsPublish()) {
                    // 检查发布者IP, 如果设置了发布者IP, 则比较当前IP来进一步判断是否要
                    boolean isContinue = true;
                    String hostAddress = InetAddress.getLocalHost().getHostAddress();
                    if (!StringUtils.isBlank(config.getPublisherIPAddress())
                            && !hostAddress.equals(config.getPublisherIPAddress())) {
                        isContinue = false;
                    }
                    // 如果注册中心为测试环境, 且启动 ip 为本地机器, 则不注册
                    if (!StringUtils.isEmpty(config.getDiscoveryUrl()) &&
                            config.getDiscoveryUrl().equals("zookeeper://*************:2181") &&
                            (hostAddress.startsWith("192.168.36.") || hostAddress.startsWith("192.168.38."))) {
                        isContinue = false;
                    }

                    if (isContinue) {
                        String version = url.getParameter(Constants.VERSION_KEY, "");
                        try {
                            this.registrySgpServiceInfo(interfaceName, version, url, config);
                        } catch (DuplicateAPIAliasException dex) {
                            throw new RpcException(dex.getMessage());
                        } catch (Exception ex) {
                            logger.error("服务[" + interfaceName + "]的API信息发布失败", ex);
                        }
                    }
                } else {
                    logger.warn("服务未配置APIDocumentConfig的bean, 将无法发布服务到sgp服务治理平台");
                }
            }

        } catch (Throwable e) {
            throw new RpcException(
                    "Failed to register " + url + " to zookeeper " + getUrl() + ", cause: " + e.getMessage(), e);
        }
    }

    /**
     * 向网关注册当前接口启动的通知
     *
     * @param interfaceName api接口名
     */
    private void notifyGateway(String interfaceName) {
        // todo 这里硬编码了网关配置节点的路径 miaoyc
        String path = "/ddc-config/api-gateway/api-events/" + interfaceName;
        // 删除旧的, 再创建, 否则不会覆盖创建
        if (zkClient4SgpInfoReg.exists(path)) {
            zkClient4SgpInfoReg.delete(path);
        }
        zkClient.create(path, true);
    }

    @Override
    protected void doUnregister(URL url) {
        try {
            zkClient.delete(toUrlPath(url));
        } catch (Throwable e) {
            throw new RpcException(
                    "Failed to unregister " + url + " to zookeeper " + getUrl() + ", cause: " + e.getMessage(), e);
        }
    }

    @Override
    protected void doSubscribe(final URL url, final NotifyListener listener) {
        try {
            if (Constants.ANY_VALUE.equals(url.getServiceInterface())) {
                String root = toRootPath();
                ConcurrentMap<NotifyListener, ChildListener> listeners = zkListeners.get(url);
                if (listeners == null) {
                    zkListeners.putIfAbsent(url, new ConcurrentHashMap<NotifyListener, ChildListener>());
                    listeners = zkListeners.get(url);
                }
                ChildListener zkListener = listeners.get(listener);
                if (zkListener == null) {
                    listeners.putIfAbsent(listener, new ChildListener() {
                        @Override
                        public void childChanged(String parentPath, List<String> currentChilds) {
                            for (String child : currentChilds) {
                                child = URL.decode(child);
                                if (!anyServices.contains(child)) {
                                    anyServices.add(child);
                                    subscribe(url.setPath(child).addParameters(Constants.INTERFACE_KEY, child,
                                            Constants.CHECK_KEY,
                                            String.valueOf(false)
                                    ), listener);
                                }
                            }
                        }
                    });
                    zkListener = listeners.get(listener);
                }
                zkClient.create(root, false);
                List<String> services = zkClient.addChildListener(root, zkListener);
                if (services != null && services.size() > 0) {
                    for (String service : services) {
                        service = URL.decode(service);
                        anyServices.add(service);
                        subscribe(url.setPath(service).addParameters(Constants.INTERFACE_KEY, service,
                                Constants.CHECK_KEY, String.valueOf(false)
                        ), listener);
                    }
                }
            } else {
                List<URL> urls = new ArrayList<URL>();
                for (String path : toCategoriesPath(url)) {
                    ConcurrentMap<NotifyListener, ChildListener> listeners = zkListeners.get(url);
                    if (listeners == null) {
                        zkListeners.putIfAbsent(url, new ConcurrentHashMap<NotifyListener, ChildListener>());
                        listeners = zkListeners.get(url);
                    }
                    ChildListener zkListener = listeners.get(listener);
                    if (zkListener == null) {
                        listeners.putIfAbsent(listener, new ChildListener() {

                            @Override
                            public void childChanged(String parentPath, List<String> currentChilds) {
                                ZookeeperRegistryInDev.this
                                        .notify(url, listener, toUrlsWithEmpty(url, parentPath, currentChilds));
                            }
                        });
                        zkListener = listeners.get(listener);
                    }
                    zkClient.create(path, false);

                    List<String> children = zkClient.addChildListener(path, zkListener);
                    if (children != null) {
                        urls.addAll(toUrlsWithEmpty(url, path, children));
                    }
                }
                notify(url, listener, urls);
            }
        } catch (
                Throwable e) {
            throw new RpcException(
                    "Failed to subscribe " + url + " to zookeeper " + getUrl() + ", cause: " + e.getMessage(), e);
        }
    }

    @Override
    protected void doUnsubscribe(URL url, NotifyListener listener) {
        ConcurrentMap<NotifyListener, ChildListener> listeners = zkListeners.get(url);
        if (listeners != null) {
            ChildListener zkListener = listeners.get(listener);
            if (zkListener != null) {
                zkClient.removeChildListener(toUrlPath(url), zkListener);
            }
        }
    }

    @Override
    public List<URL> lookup(URL url) {
        if (url == null) {
            throw new IllegalArgumentException("lookup url == null");
        }
        try {
            List<String> providers = new ArrayList<String>();
            for (String path : toCategoriesPath(url)) {
                List<String> children = zkClient.getChildren(path);
                if (children != null) {
                    providers.addAll(children);
                }
            }
            return toUrlsWithoutEmpty(url, providers);
        } catch (Throwable e) {
            throw new RpcException(
                    "Failed to lookup " + url + " from zookeeper " + getUrl() + ", cause: " + e.getMessage(), e);
        }
    }

    private String toRootDir() {
        if (root.equals(Constants.PATH_SEPARATOR)) {
            return root;
        }
        return root + Constants.PATH_SEPARATOR;
    }

    private String toRootPath() {
        return root;
    }

    private String toServicePath(URL url) {
        String name = url.getServiceInterface();
        if (Constants.ANY_VALUE.equals(name)) {
            return toRootPath();
        }
        return toRootDir() + URL.encode(name);
    }

    private String[] toCategoriesPath(URL url) {
        String[] categories;
        if (Constants.ANY_VALUE.equals(url.getParameter(Constants.CATEGORY_KEY))) {
            categories = new String[]{Constants.PROVIDERS_CATEGORY, Constants.CONSUMERS_CATEGORY,
                    Constants.ROUTERS_CATEGORY, Constants.CONFIGURATORS_CATEGORY};
        } else {
            categories = url.getParameter(Constants.CATEGORY_KEY, new String[]{Constants.DEFAULT_CATEGORY});
        }
        String[] paths = new String[categories.length];
        for (int i = 0; i < categories.length; i++) {
            paths[i] = toServicePath(url) + Constants.PATH_SEPARATOR + categories[i];
        }
        return paths;
    }

    private String toCategoryPath(URL url) {
        return toServicePath(url) + Constants.PATH_SEPARATOR
                + url.getParameter(Constants.CATEGORY_KEY, Constants.DEFAULT_CATEGORY);
    }

    private String toUrlPath(URL url) {
        return toCategoryPath(url) + Constants.PATH_SEPARATOR + URL.encode(url.toFullString());
    }

    private List<URL> toUrlsWithoutEmpty(URL consumer, List<String> providers) {
        List<URL> urls = new ArrayList<URL>();
        if (providers != null && providers.size() > 0) {
            for (String provider : providers) {
                provider = URL.decode(provider);
                if (provider.contains("://")) {
                    URL url = URL.valueOf(provider);
                    if (UrlUtils.isMatch(consumer, url)) {
                        urls.add(url);
                    }
                }
            }
        }
        return urls;
    }

//    public static void main(String[] args) {
//        String url = "zookeeper://dev001:2181?backup=dev002:2181,dev003:2181";
//        System.out.println(convertZkUrl(url));
//        url = "zookeeper://dev001:2181";
//        System.out.println(convertZkUrl(url));
//        url = "dev001:2181";
//        System.out.println(convertZkUrl(url));
//        url = "dev001:2181,dev002:2181";
//        System.out.println(convertZkUrl(url));
//    }

    private List<URL> toUrlsWithEmpty(URL consumer, String path, List<String> providers) {
        List<URL> urls = toUrlsWithoutEmpty(consumer, providers);
        if (urls.isEmpty()) {
            int i = path.lastIndexOf('/');
            String category = i < 0 ? path : path.substring(i + 1);
            URL empty = consumer.setProtocol(Constants.EMPTY_PROTOCOL).addParameter(Constants.CATEGORY_KEY, category);
            urls.add(empty);
        }
        return urls;
    }

    /**
     * 初始化用于注册sgo cache的zk客户端工具, 避免一个Provider创建一个zk连接
     *
     * @param serverUrls
     * <AUTHOR>
     */
    private synchronized void initZKClient4SgpInfoReg(String serverUrls) {
        if (this.zkClient4SgpInfoReg == null) {
            zkClient4SgpInfoReg = new ZkClient(serverUrls);
            SgpServiceInfoSerializer bse = new SgpServiceInfoSerializer();
            zkClient4SgpInfoReg.setZkSerializer(bse);
        }
    }

    private Map<String, Boolean> handledAPIs = Maps.newHashMap();

    /**
     * 注册服务方法信息到ZK，与SGP平台对接
     *
     * @param interfaceName
     * @param version
     * @param url
     * @param config
     * @throws ClassNotFoundException
     * @throws IllegalArgumentException
     * @throws SecurityException
     * @throws IOException
     */
    private void registrySgpServiceInfo(String interfaceName, String version, URL url, APIDocumentConfig config) throws ClassNotFoundException,
            SecurityException, IllegalArgumentException, IOException {
        if (StringUtils.isEmpty(interfaceName)) {
            LogFactory.getLog(LOGNAME).warn("无法解析服务信息[URL = " + toUrlPath(url) + "]");
            return;
        }
        if (handledAPIs.get(interfaceName) != null) {
            String msg = String.format("接口%s已经完成注册, 无需再次注册", interfaceName);
            LogFactory.getLog(LOGNAME).warn(msg);
            return;
        }
        // 标记已注册
        handledAPIs.put(interfaceName, true);

        ClassLoader cl = ClassHelper.getCallerClassLoader(this.getClass());
        Class<?> cls = Class.forName(interfaceName, false, cl);

        config.setDiscoveryUrl(convertZkUrl(config.getDiscoveryUrl()));

        Map<String, String> apiCodes = new HashMap<String, String>();

        initZKClient4SgpInfoReg(config.getDiscoveryUrl());

        if (!zkClient4SgpInfoReg.exists(SGP_SERVICE_INFO_ROOT)) {
            zkClient4SgpInfoReg.createPersistent(SGP_SERVICE_INFO_ROOT);
        }

        Object obj = getBean(cls);
        // openapi的领域配置, 可能为null

        OpenapiConfiguration openapiConfiguration = (OpenapiConfiguration) getBean(OpenapiConfiguration.class);
        if (obj != null) {
            // provider 实例
            Class<?> providerCls = obj.getClass();
            // 避免provider类被aop增强后注解丢失, 这里要找target bean的类
            providerCls = ClassUtils.getUserClass(providerCls);

            InterfaceEntity interfaceEntity = new InterfaceEntity();
            interfaceEntity.setPublishTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            interfaceEntity.setInterfaceName(interfaceName);
            interfaceEntity.setMethods(new ArrayList<>());
            interfaceEntity.setVersion(version);

            if (providerCls.getAnnotation(Interface.class) != null) {
                interfaceEntity
                        .setInterfaceAnno(ObjectUtil.Object2String(providerCls.getAnnotation(Interface.class)));
            }

            // 需更详尽的解析
            // 需拿到所有的方法、方法参数、方法参数对象中的属性，一层层下去
            // 解析完后放入数据库，需考虑覆盖重复的问题
            // 如果是Map、List
            Method[] methods = providerCls.getDeclaredMethods();
            for (Method method : methods) {
                interfaceEntity.setObjects(new ArrayList<>());
                MethodEntity methodEntity = new MethodEntity();
                methodEntity.setMethodName(method.getName());
                methodEntity.setParameters(new ArrayList<ParameterEntity>());

                // 接口方法注解
                if (method.isAnnotationPresent(com.yyigou.ddc.common.service.annotation.Method.class)) {
                    com.yyigou.ddc.common.service.annotation.Method methodAnno =
                            (com.yyigou.ddc.common.service.annotation.Method) method
                                    .getAnnotation(com.yyigou.ddc.common.service.annotation.Method.class);

                    // 验证别名是否重复
                    String apiCode = methodAnno.aliasName();
                    if (!StringUtils.isBlank(apiCode)) {
                        if (apiCodes.containsKey(apiCode)) {
                            throw new DuplicateAPIAliasException(
                                    "已有重复的API别名[" + apiCode + "]位于[" + apiCodes.get(apiCode) + "]，当前位置["
                                            + interfaceName + "." + method.getName() + "]");
                        } else {
                            apiCodes.put(apiCode, interfaceName + "." + method.getName());
                        }
                    }
                    methodEntity.setVersion(methodAnno.version());
                    methodEntity.setApiCode(apiCode);

                    // 处理openapi开放接口 (标准规范里应该配置link_domain.properties文件里维护领域信息作为全局, @Method注解里配置的domainCode, subDomainCode优先级更高)
                    try {
                        com.yyigou.ddc.common.service.annotation.Method.InvokeRange invokeRange = methodAnno.invokeRange();
                        com.yyigou.ddc.common.service.annotation.Method.InvokeRange[] invokeRanges = methodAnno.invokeRanges(); // 优先选数组配置
                        if (invokeRanges == null || invokeRanges.length == 0) {
                            invokeRanges = new com.yyigou.ddc.common.service.annotation.Method.InvokeRange[]{invokeRange}; // 数组配置为空, 则退化为独立配置
                        }
                        if (ValidatorUtils.supportOpenApiInvoke(invokeRanges)) {
                            String domainCodeInAnno = methodAnno.domainCode();
                            String subdomainCodeInAnno = methodAnno.subDomainCode();
                            if (StringUtils.isEmpty(domainCodeInAnno) && (openapiConfiguration == null || StringUtils.isEmpty(openapiConfiguration.getDomainCode()))) {
                                String msg = "开放接口[" + interfaceName + "." + method.getName() + "]未发现领域信息domainCode";
                                log.error(msg);
                            }
                            if (StringUtils.isEmpty(subdomainCodeInAnno) && (openapiConfiguration == null || StringUtils.isEmpty(openapiConfiguration.getSubDomainCode()))) {
                                String msg = "开放接口[" + interfaceName + "." + method.getName() + "]未发现子领域信息subDomainCode";
                                log.error(msg);
                            }
                            // 植入全局领域配置, 方法级领域配置
                            methodEntity.setGlobalDomainCode(openapiConfiguration != null ? openapiConfiguration.getDomainCode() : null);
                            methodEntity.setGlobalSubDomainCode(openapiConfiguration != null ? openapiConfiguration.getSubDomainCode() : null);
                        }
                    } catch (Exception e) {
                        log.error("处理接口{}注册异常:{}", apiCode, e.getMessage());
                    }
                    // 设置方法注解字符串
                    methodEntity.setMethodAnno(ObjectUtil.Object2String(methodAnno));
                } else {
                    // 无 Method 注解的直接跳过, 不需要更新SGP
                    continue;
                }

                if (method.isAnnotationPresent(com.yyigou.ddc.common.service.annotation.MethodDescForApiDoc.class)) {
                    com.yyigou.ddc.common.service.annotation.MethodDescForApiDoc methodAnno =
                            (com.yyigou.ddc.common.service.annotation.MethodDescForApiDoc) method
                                    .getAnnotation(com.yyigou.ddc.common.service.annotation.MethodDescForApiDoc.class);
                    // 设置 doc 注解
                    methodEntity.setMethodDescForApiDoc(ObjectUtil.Object2String(methodAnno));
                }
                methodEntity.setMethodName(method.getName());

                String[] parameterNames = this.discoverer.getParameterNames(method);

                // 偶尔会有 null 的情况
                if (parameterNames == null) {
                    continue;
                }

                Type[] paramTypes = method.getGenericParameterTypes();

                String returnTypeName = this.getParameterizedTypeName(method.getGenericReturnType());
                List<Class<?>> subClazz = this.getParameterizedTypeSubClazz(method.getGenericReturnType());

                // 返回类型中的实体属性
                if (config != null && config.getIsPublishObjectInfo()) {
                    this.parseObjects(subClazz, interfaceEntity, methodEntity);
                }

                TypeEntity returnType = new TypeEntity();
                returnType.setTypeName(returnTypeName);

                // 返回类型中如果是泛型，需要拿到业务实体对象的描述
                List<TypeEntity> subTypes = new ArrayList<TypeEntity>();
                for (Class<?> subClass : subClazz) {
                    if (!subClass.getCanonicalName().startsWith("java.")
                            && !subClass.getCanonicalName().startsWith("com.sun")) {
                        TypeEntity typeEntity = new TypeEntity();
                        typeEntity.setTypeName(subClass.getCanonicalName());
                        subTypes.add(typeEntity);
                    }
                }

                TypeEntity[] _subTypes = new TypeEntity[subTypes.size()];
                subTypes.toArray(_subTypes);

                methodEntity.setReturnType(returnType);

                String methodMeta = returnTypeName + " " + method.getName() + "(";
                String parameterString = "";

                for (int i = 0; i < parameterNames.length; i++) {

                    String parameterName = parameterNames[i];
                    String parameterTypeName = this.getParameterizedTypeName(paramTypes[i]);

                    ParameterEntity parameterEntity = new ParameterEntity();
                    parameterEntity.setParameterName(parameterName);
                    TypeEntity parameterTypeEntity = new TypeEntity();
                    parameterTypeEntity.setTypeName(parameterTypeName);

                    // 泛型子类型
                    subClazz = this.getParameterizedTypeSubClazz(paramTypes[i]);

                    // 解析实体类
                    if (config != null && config.getIsPublishObjectInfo()) {
                        this.parseObjects(subClazz, interfaceEntity, methodEntity);
                    }

                    // 取返回类型中的实体对象
                    subTypes = new ArrayList<TypeEntity>();
                    for (Class<?> subClass : subClazz) {
                        if (!subClass.getCanonicalName().startsWith("java.")
                                && !subClass.getCanonicalName().startsWith("com.sun")) {
                            TypeEntity typeEntity = new TypeEntity();
                            typeEntity.setTypeName(subClass.getCanonicalName());
                            subTypes.add(typeEntity);
                        }
                    }

                    _subTypes = new TypeEntity[subTypes.size()];
                    subTypes.toArray(_subTypes);
                    parameterEntity.setParameterType(parameterTypeEntity);

                    // 查到它的注解
                    Annotation[][] pans = method.getParameterAnnotations();
                    Annotation[] subPans = pans[i];

                    for (int b = 0; b < subPans.length; b++) {
                        if (subPans[b] instanceof Parameter) {
                            parameterEntity.setParameterAnno(ObjectUtil.Object2String(
                                    (Parameter) subPans[b]));
                            break;
                        }
                    }

                    methodEntity.getParameters().add(parameterEntity);

                    if (!parameterString.equals("")) {
                        parameterString += ",";
                    }

                    parameterString += parameterTypeName + " " + parameterName;
                }

                methodMeta += parameterString + ")";

                methodEntity.setMetaName(methodMeta);

                interfaceEntity.getMethods().add(methodEntity);

                // 删除旧的Provider接口信息
                String _path = String.format("%s/%s_%s", SGP_SERVICE_OBJECT_INFO_ROOT, methodEntity.getApiCode(), methodEntity.getVersion());
                if (zkClient4SgpInfoReg.exists(_path)) {
                    zkClient4SgpInfoReg.delete(_path);
                }
                // 保存新的Provider接口信息 (序列化的同时将信息压缩, 保障单个provider类支持更多的接口)
                zkClient4SgpInfoReg.createPersistent(_path, interfaceEntity.getObjects());

            }
            interfaceEntity.setObjects(new ArrayList<>());
            // 删除旧的Provider接口信息
            String _path = String.format("%s/%s", SGP_SERVICE_INFO_ROOT, interfaceName);
            if (zkClient4SgpInfoReg.exists(_path)) {
                zkClient4SgpInfoReg.delete(_path);
            }
            // 保存新的Provider接口信息 (序列化的同时将信息压缩, 保障单个provider类支持更多的接口)
            zkClient4SgpInfoReg.createPersistent(_path, interfaceEntity);
            // 通知网关接口信息发生变更
            notifyGateway(interfaceName);

        }


    }

    /**
     * @param subClazz
     * @param interfaceEntity
     * @throws IOException
     */
    private void parseObjects(List<Class<?>> subClazz, InterfaceEntity interfaceEntity, MethodEntity methodEntity) throws IOException {

        List<Class<?>> deepSubClazz = new ArrayList<Class<?>>();

        for (Class<?> subClass : subClazz) {

            if (this.baseTypes.contains(subClass.getCanonicalName())) {
                continue;
            }

            if (subClass.getCanonicalName().startsWith("java.")) {
                continue;
            }

            if (StringUtils.isEmpty(methodEntity.getApiCode())) {
                continue;
            }

            ObjectEntity objectEntity = new ObjectEntity();
            objectEntity.setObjectName(subClass.getName());
            objectEntity.setApiCode(methodEntity.getApiCode());
            objectEntity.setApiVersion(methodEntity.getVersion());

            boolean isBreak = false;
            for (ObjectEntity _objectEntity : interfaceEntity.getObjects()) {
                if (_objectEntity.equals(objectEntity)) {
                    isBreak = true;
                    break;
                }
            }

            if (isBreak) {
                continue;
            }

            objectEntity.setFields(new ArrayList<FieldEntity>());

            Map<String, Field> fieldMap = this.getAllField(subClass);
            for (String fieldKey : fieldMap.keySet()) {
                Field field = fieldMap.get(fieldKey);
                FieldEntity fieldEntity = new FieldEntity();
                fieldEntity.setFieldName(field.getName());
                TypeEntity fieldTypeEntity = new TypeEntity();
                fieldTypeEntity.setTypeName(this.getParameterizedTypeName(field.getGenericType()));

                if (field.getGenericType() instanceof ParameterizedType) {
                    deepSubClazz.addAll(this.getParameterizedTypeSubClazz(field.getGenericType()));
                }

                fieldEntity.setFieldType(fieldTypeEntity);

                // 查到它的注解
                Annotation[] fans = field.getAnnotations();
                for (int b = 0; b < fans.length; b++) {
                    if (fans[b] instanceof EntityField) {
                        fieldEntity.setFieldAnno(ObjectUtil
                                .Object2String((EntityField) fans[b]));
                        break;
                    }
                }

                objectEntity.getFields().add(fieldEntity);

                if (isTargetPackage(field.getType().getName())) {
                    deepSubClazz.add(field.getType());
                }
            }

            // 查到它的注解
            Annotation[] eans = subClass.getAnnotations();
            for (int b = 0; b < eans.length; b++) {
                if (eans[b] instanceof Entity) {
                    objectEntity.setEntityAnno(
                            ObjectUtil.Object2String((Entity) eans[b]));
                    break;
                }
            }

            interfaceEntity.getObjects().add(objectEntity);
        }

        if (deepSubClazz.size() > 0) {
            this.parseObjects(deepSubClazz, interfaceEntity, methodEntity);
        }
    }

    /**
     * 获取类及所有父类的所有属性
     *
     * @param cls
     * @return
     */
    public Map<String, Field> getAllField(Class<?> cls) {
        Map<String, Field> fieldMap = new HashMap<String, Field>();

        List<Field> fieldList = new ArrayList<Field>();
        Field[] fields;
        Class<?> _tcls = cls;
        while (_tcls != null && !_tcls.getName().equals(Object.class.getName())) {
            fields = _tcls.getDeclaredFields();
            fieldList.addAll(Arrays.asList(fields));
            _tcls = _tcls.getSuperclass();
        }

        for (Field field : fieldList) { // 静态属性不考虑
            if (!Modifier.isStatic(field.getModifiers())) {
                if (!fieldMap.containsKey(field.getName()) && !field.getName().equals("serialVersionUID")) {
                    fieldMap.put(field.getName(), field);
                }
            }
        }

        return fieldMap;
    }

    /**
     * 获取泛型变量的具体类型
     *
     * @param type
     * @return
     */
    public List<Class<?>> getParameterizedTypeSubClazz(Type type) {
        List<Class<?>> subClazz = new ArrayList<Class<?>>(10);
        this.getParameterizedTypeSubClazz(type, subClazz);
        return subClazz;
    }

    /**
     * 获取泛型变量的具体类型
     *
     * @param type
     * @param subClazz
     */
    public void getParameterizedTypeSubClazz(Type type, List<Class<?>> subClazz) {

        if (type instanceof ParameterizedType) {

            subClazz.add((Class<?>) ((ParameterizedType) type).getRawType());

            Type[] subTypes = ((ParameterizedType) type).getActualTypeArguments();
            if (subTypes.length > 0) {
                for (Type subType : subTypes) {
                    if (subType instanceof ParameterizedType) {
                        // subClazz.addAll(this.getParameterizedTypeSubClazz(subType));
                        this.getParameterizedTypeSubClazz(subType, subClazz); // 下级类型
                    } else {
                        if (subType instanceof Class) {
                            if (!this.baseTypes.contains(((Class<?>) subType).getName())) {
                                subClazz.add((Class<?>) subType);
                            }
                        }  //

                    }
                }
            }
        } else {
            subClazz.add((Class<?>) type);
        }
    }

    public String getParameterizedTypeName(Type type) {

        String typeName;

        if (type instanceof ParameterizedType) { // 参数化类型
            ParameterizedType _type = (ParameterizedType) type;
            Type[] _subTypes = _type.getActualTypeArguments();
            typeName = _type.toString().substring(0, _type.toString().indexOf("<"));
            if (_subTypes.length > 0) {
                typeName += "<";
            }
            int _subTypeIndex = 0;
            for (Type _subType : _subTypes) {
                typeName += (_subTypeIndex == 0 ? "" : ",") + getParameterizedTypeName(_subType);
                _subTypeIndex++;
            }
            if (_subTypes.length > 0) {
                typeName += ">";
            }
        } else if (type instanceof GenericArrayType) { // 数组类型
            GenericArrayType _type = (GenericArrayType) type;
            typeName = _type.toString();
        } else if (type instanceof TypeVariable) { // 泛类型
            TypeVariable<?> _type = (TypeVariable<?>) type;
            typeName = _type.toString();
        } else if (type instanceof WildcardType) { // 通配符类型
            WildcardType _type = (WildcardType) type;
            typeName = _type.toString();
        } else {
            typeName = ((Class<?>) type).getCanonicalName();
        }

        return typeName;

    }

}
