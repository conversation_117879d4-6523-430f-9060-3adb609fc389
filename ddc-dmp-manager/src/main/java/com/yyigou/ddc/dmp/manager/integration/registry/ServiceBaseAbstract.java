package com.yyigou.ddc.dmp.manager.integration.registry;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yyigou.ddc.common.enums.FuncVisibleType;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.message.MessageVO;
import com.yyigou.ddc.common.service.*;
import com.yyigou.ddc.common.uap.UapBaseDTO;
import com.yyigou.ddc.common.uap.UapBaseParams;
import com.yyigou.ddc.common.util.JSONParseUtil;
import com.yyigou.ddc.common.util.SessionUtils;
import com.yyigou.ddc.common.util.ValidatorUtils;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.dozer.DozerBeanMapper;
import org.dozer.Mapper;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Pattern;

/**
 * 后端服务顶层接口的抽象类
 * <p>
 * 业务接口的实现需继承本类，以支持WEB网关的统一调用入口
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ServiceBaseAbstract implements ServiceBase {

    /**
     * map 到 JAVA对象的转换工具
     */
    private static final Mapper mapper = new DozerBeanMapper();
    /**
     * WEB网关传递过来的请求信息
     */
//    private static final ThreadLocal<RequestHeader> threadSession = new ThreadLocal<RequestHeader>();
    private static final Field tsf = FieldUtils.getField(com.yyigou.ddc.common.service.ServiceBaseAbstract.class, "threadSession", true);
    private static final ThreadLocal<RequestHeader> threadSession;
    static {
        try {
            threadSession = (ThreadLocal<RequestHeader>) tsf.get(null);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }


    private static final Pattern qcPattern = Pattern.compile( "QC\\d{8}(|_dim|_list|_start|_end|_all)$", Pattern.CASE_INSENSITIVE);
    /**
     * 可直接转换的基本类型，非JAVA的基础类型概念
     */
    private static final String[] baseTypes = new String[]{
            "int",
            "boolean",
            "float",
            "double",
            "short",
            "long",
            "byte",
            "char",
            "string",
            "java.lang.Float",
            "java.lang.Double",
            "java.lang.Short",
            "java.lang.Long",
            "java.lang.String",
            "java.lang.Integer",
            "java.math.BigDecimal"
    };

    /**
     * call 方法前置后置处理接口, 可由服务内自主添加, 但是要注意顺序
     */
    private static final List<ServiceBaseOperatorWrapper> serviceBaseOperatorWrappers = Lists.newCopyOnWriteArrayList();

    /**
     * provider的所有方法信息, init()方法会初始化后, 被网关使用()
     */
    protected  final Map<String, ApiMethod> methods = Maps.newHashMapWithExpectedSize(50);

    /**
     * provider的所有方法信息, init()方法会初始化后, call 方法中使用
     */
    protected  final Map<String, Method> methodCache = Maps.newHashMapWithExpectedSize(50);

    /**
     * 请求信息
     *
     * @return
     */
    public static RequestHeader currentRequest() {

        RequestHeader header = threadSession.get();

        if (header == null) {
            throw new RequestHeaderIsNullException("请求信息为空");
        }

        return header;
    }

    /**
     * 返回请求头
     * @return null if no
     */
    public static RequestHeader getRequestHeader(){
        RequestHeader header = threadSession.get();
        return header;
    }

    /**
     * 获取类属性
     *
     * @param theClass  要查的类
     * @param fieldName 要查的属性名称
     * @return
     */
    public static Field findField(Class<?> theClass, String fieldName) {
        Field field = ReflectionUtils.findField(theClass, fieldName);
        if (field != null && !field.isAccessible()) {
            field.setAccessible(true);
        }
        return field;
    }

    /**
     * 公共调用方法
     *
     * <p>
     * 所有的服务需 extends 此类，供前端WEB网关调用
     * </p>
     *
     * @param header 请求参数
     * @return 调用结果
     */
    @Override
    @SuppressWarnings("unchecked")
    public CallResult<Object> call(RequestHeader header) {

        CallResult<Object> result = new CallResult<Object>();
        result.setCode(MessageVO.SUCCESS);
        if (header == null) {
            result.setCode("101");
            result.setMessage("请求参数不能为空");
            return result;
        }

        String exceptionMessage = "";

        Class<? extends ServiceBaseAbstract> cls = this.getClass();

        Method targetMethod = methodCache.get(header.getApiName());

        // @Method 注解中记录的接口信息
        ApiMethod apiMethod = methods.get(header.getApiName());

//        Method[] methods = cls.getMethods();
//        for (Method method : methods) {
//            if (method.getName().equals(header.getApiName())) {
//                targetMethod = method;
//                break;
//            }
//        }

        if (targetMethod == null) {
            exceptionMessage = "目标接口方法[" + header.getApiName() + "]找不到";
            log.warn(exceptionMessage + " , 入参：" + JSONObject.toJSONString(header.getParams()));
            result.setCode("102");
            result.setMessage(exceptionMessage);
            return result;
        }

        Class<?> returnType = targetMethod.getReturnType();
        Type[] returnTypes = targetMethod.getGenericParameterTypes();

        if (!returnType.getName().equals(MessageVO.class.getName())
                && !returnType.getName().equals(CallResult.class.getName())
                && (returnType.getSuperclass() != null && !returnType.getSuperclass().getName()
                .equals(CallResult.class.getName()))) {
            exceptionMessage = "目标接口方法返回类型[" + targetMethod.getReturnType().getName() + "]不符合调用规范";
            log.warn(exceptionMessage);
            result.setCode("102");
            result.setMessage(exceptionMessage);
            return result;
        }

        Class<?> paramClss[] = targetMethod.getParameterTypes();

        int paramClssCount = paramClss.length;
        for (Class<?> paramCls : paramClss) {
            if (paramCls.getName().equals(RequestHeader.class.getName())) {
                paramClssCount--;
            }
        }

        if (paramClssCount > 0 && paramClssCount != header.getParams().size()) {
            result.setCode("103");
            result.setMessage("传入的参数与目标接口方法参数个数不匹配");
            return result;
        }

        // 检查并重构请求参数中的map对象
        int paramIndex = 0;
        for (Map<String, Object> param : header.getParams()) {
            paramCheck(param, paramIndex);
            paramIndex++;
        }


        // 处理 uap 查询方案
        try{
            handleUapBaseParam(header, header.getParams());
        } catch (Exception e){
            result.setCode("106");
            result.setMessage(e.getMessage());
            log.warn("处理查询方案参数异常: " + e.getMessage(), e);
            return result;
        } finally {
            // 清除会话
            threadSession.remove();
        }

        List<Object> params = new ArrayList<Object>();

        paramIndex = 0;
        Object paramObj = null;
        HashMap<String, Object> paramMap = null;
        Object tmpObj = null;
        List<Object> tmpList = null;
        Class<?> tmpCls;

        for (Class<?> paramCls : paramClss) {

            if (paramCls.getName().equals(RequestHeader.class.getName())) {
                params.add(header);
            } else {
                paramObj = header.getParams().get(paramIndex);
                if (paramObj != null && paramObj instanceof HashMap<?, ?>) {
                    paramMap = (HashMap<String, Object>) paramObj;
                    if (paramMap.isEmpty()) {
                        result.setCode("103");
                        result.setMessage("入参[" + paramIndex + "]不能为空");
                        return result;
                    } else {
                        try {
                            paramObj = paramMap.values().toArray()[0];

                            if (Arrays.asList(baseTypes).contains(paramCls.getName())) {
                                params.add(this.baseTypeParse(paramCls, paramObj));
                            } else {

                                if (paramObj instanceof List || paramObj.getClass().isArray()) {

                                    if (paramObj.getClass().isArray()) {
                                        tmpList = new ArrayList<Object>();
                                        for (Object _tmpObj : (Object[]) paramObj) {
                                            tmpList.add(_tmpObj);
                                        }
                                    } else {
                                        tmpList = (ArrayList<Object>) paramObj;
                                    }

                                    tmpCls = this.getSubType(targetMethod, paramIndex);

                                    for (int i = 0; i < tmpList.size(); i++) {
                                        tmpObj = tmpList.get(i);

                                        Object destObject;
                                        if (Arrays.asList(baseTypes).contains(tmpObj.getClass().getName())) {
                                            destObject = this.baseTypeParse(tmpCls, tmpObj);
                                        } else {
                                            destObject = mapper.map(tmpObj, tmpCls);
                                        }

                                        tmpList.remove(i);
                                        tmpList.add(i, destObject);
                                    }

                                    if (paramCls.isArray()) {
                                        params.add(((List<?>) tmpList).toArray());
                                    /*}else if(paramCls.getName().equals(java.util.Set.class.getName()))
									{
										Constructor con = HashSet.class.getDeclaredConstructor(new Class[]{tmpCls});
										Set<?> paramsSet = (Set<?>)con.newInstance(); */
                                    } else {
                                        params.add(tmpList);
                                    }

                                } else if (paramObj instanceof Map && paramCls.getName().toLowerCase()
                                        .equals("java.util.map")) {
                                    params.add(paramObj);
                                } else {
                                    // todo 这里有个性能隐患, 如果dto对象很大, 用dozer的实现map转pojo效率很低
                                    Object destObject = mapper.map(paramObj, paramCls);
                                    params.add(destObject);
                                }
                            }
                        } catch (Exception e) {
                            log.warn(e.getMessage(), e);
                            result.setCode("103");
                            result.setMessage("入参[" + paramIndex + "]转换异常：" + e.getMessage() + ", 调用接口: " + header.getApiName());
                            return result;
                        }
                    }
                } else {
                    params.add(paramObj);
                }
                paramIndex++;
            }
        }
        // 注入会话
        threadSession.set(header);

        try {
            // 前置处理
            for (ServiceBaseOperator serviceBaseAround : serviceBaseOperatorWrappers) {
                params = serviceBaseAround.beforeCall(params, apiMethod);
            }

            // 执行方法
            Object calledResult = targetMethod.invoke(this, params.toArray());
            result = calledResult instanceof MessageVO ?
                    new CallResult<Object>((MessageVO) calledResult) :
                    (CallResult<Object>) calledResult;

            // 后置处理
            for (int i = serviceBaseOperatorWrappers.size() - 1; i >= 0; i--) {
                result = serviceBaseOperatorWrappers.get(i).afterCall(result, apiMethod);
            }

        } catch (BusinessException bex) {
            result.setCode(bex.getCode());
            result.setMessage(bex.getMessage());
        } catch (IllegalAccessException e) {
            result.setCode("106");
            result.setMessage("目标接口不允许外部调用");
            log.warn(e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            result.setCode("107");
            result.setMessage("参数不匹配");
            log.warn(e.getMessage(), e);
        } catch (InvocationTargetException e) {
            Throwable t = e.getTargetException();
            if (t != null) {
                if (t instanceof BusinessException) {
                    BusinessException bex = (BusinessException) t;
                    result.setCode(bex.getCode());
                    result.setMessage(bex.getMessage());
                } else if (t.toString().contains("BusinessException")) {
                    BusinessException bex = (BusinessException) t;
                    result.setCode(bex.getCode());
                    result.setMessage(bex.getMessage());
                } else {
                    result.setCode("108");
                    result.setMessage("目标接口调用时产生未处理的异常(" + t.toString() + ")");
                    log.error("目标接口调用时产生未处理的异常：", e);
                }
            } else {
                result.setCode("108");
                result.setMessage("目标接口调用时产生未处理的异常");
                log.error(e.getMessage(), e);
                if (e.getStackTrace() != null) {
                    log.error(e.getStackTrace().toString());
                }
            }
        } catch (Exception e) {
            result.setCode("109");
            result.setMessage("目标接口调用时生产未明确的异常");
            log.error(e.getMessage(), e);
            if (e.getStackTrace() != null) {
                log.error(e.getStackTrace().toString());
            }
        } finally {
            // 清除会话
            threadSession.remove();
        }

        return result;
    }


    /**
     * 处理 uap 查询方案
     * @param header
     */
    private void handleUapBaseParam(RequestHeader header, List<Map<String,Object>> params) {
        if (header.getUapBaseParam() != null){
            // 注入 uapBaseDTO
            UapBaseParams uapBaseParams = mapper.map(header.getUapBaseParam(), UapBaseParams.class);
            String planNo = uapBaseParams.getPlanNo();
            if(StrUtil.isNotEmpty(uapBaseParams.getUniqueKey())){
                SessionUser session = header.getSession();
                if( session != null && session.getExtInfo() != null ){
                    session.getExtInfo().put(SessionUtils.EXT_MAP_BILL_VIEW_PAGEQUERY_KEY, uapBaseParams.getUniqueKey());
                    session.getExtInfo().put(SessionUtils.EXT_MAP_BILL_VIEW_PAGEQUERY_PLAN_NO, planNo);
                }
            }
            Map<String, Object> queryConditionMap = uapBaseParams.getQueryConditionMap();
            Map<String, Map<String, Object>> codeQueryConditionMap = uapBaseParams.getCodeQueryConditionMap();
            // 两个都不为空
            if (planNo != null){
                // 如果代码查询条件不为空
                if (codeQueryConditionMap != null && !codeQueryConditionMap.isEmpty()){
                    SessionUser session = header.getSession();
                    session.setCodeQueryConditionMap(codeQueryConditionMap);
                    for (Map<String, Object> value : codeQueryConditionMap.values()) {
                        if (value != null) {
                            String fieldPath = (String) value.get("fieldPath");
                            Object fieldValue = value.get("fieldValue");
                            // 如果存在查询条件
                            if (!ValidatorUtils.checkEmpty(fieldValue) && !StringUtils.isEmpty(fieldPath)) {
                                // 如: params.used
                                String[] fieldPathArr = fieldPath.split("\\.");
                                if (fieldPathArr.length == 2) {
                                    String dtoName = fieldPathArr[0];
                                    String fieldName = fieldPathArr[1];
                                    // 获取参数 params
                                    for (Map<String, Object> paramObj : params) {
                                        if (paramObj instanceof HashMap<?, ?>) {
                                            Map<String, Object> paramMap = (HashMap<String, Object>) paramObj;
                                            if (!paramMap.isEmpty()) {
                                                String paramName = paramMap.keySet().stream().findFirst().get();
                                                if (paramName.equals(dtoName)){
                                                    Object paramObjTemp = paramMap.get(paramName);
                                                    if (paramObjTemp instanceof Map){
                                                        // 设置 used 为指定值
                                                        ((Map<String, Object>) paramObjTemp).put(fieldName, fieldValue);
                                                    } else {
                                                        throw new BusinessException("查询方案回写属性对象不合法!");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }else {
                                    throw new BusinessException("查询方案回写属性路径设置异常!");
                                }
                            }
                        }
                    }
                }

                if (queryConditionMap != null && !queryConditionMap.isEmpty()){
                    UapBaseDTO uapBaseDTO = new UapBaseDTO();
                    ConcurrentMap<String, Object> queryPlanConditions = Maps.newConcurrentMap();
                    uapBaseDTO.setQueryPlanConditions(queryPlanConditions);
                    uapBaseDTO.setQueryPlanId(planNo);

                    // 前端会传入 QC00000032_Dim, QC00000032_List, QC00000032_start, QC00000032_end
                    for (String conditionNoKeyFront : queryConditionMap.keySet()) {
                        Object object = queryConditionMap.get(conditionNoKeyFront);
                        if (ValidatorUtils.checkEmpty(object)){
                            continue;
                        }
                        if (!qcPattern.matcher(conditionNoKeyFront).matches()){
                            continue;
                        }
                        String[] split = conditionNoKeyFront.split("_");
                        String conditionNo = null;
                        if (split.length == 1){
                            conditionNo = split[0];
                            queryPlanConditions.put(conditionNo, object);
                        } else if (split.length == 2){
                            conditionNo = split[0];
                            String conditionNoQueryKey = split[1];
                            if ("list".equalsIgnoreCase(conditionNoQueryKey) && !(object instanceof Collection)){
                                queryPlanConditions.put(conditionNo, Lists.newArrayList(object));
                            } else if ("start".equalsIgnoreCase(conditionNoQueryKey)) {
                                if (!queryPlanConditions.containsKey(conditionNo)){
                                    queryPlanConditions.put(conditionNo, Maps.newHashMap());
                                }
                                HashMap<String, Object> o = (HashMap<String, Object>) queryPlanConditions.get(conditionNo);
                                o.put("start", object);
                            } else if ("end".equalsIgnoreCase(conditionNoQueryKey)) {
                                if (!queryPlanConditions.containsKey(conditionNo)){
                                    queryPlanConditions.put(conditionNo, Maps.newHashMap());
                                }
                                HashMap<String, Object> o = (HashMap<String, Object>) queryPlanConditions.get(conditionNo);
                                o.put("end", object);
                            } else if ("all".equalsIgnoreCase(conditionNoQueryKey)){
                                // 如果是 all, 说明是 list 的附属, list 为空时使用 all
                                if (!queryPlanConditions.containsKey(conditionNo)){
                                    queryPlanConditions.put(conditionNo, object);
                                }
                            } else {
                                queryPlanConditions.put(conditionNo, object);
                            }
                        }else {
                            throw new BusinessException(String.format("查询方案条件编号【%s】不正确!", conditionNoKeyFront));
                        }
                    }
                    // 如果存在查询条件
                    if (!queryPlanConditions.isEmpty()){
                        SessionUser session = header.getSession();
                        if (FuncVisibleType.GENERAL.getCode().equals(header.getFuncVisibleTenantType())){
                            uapBaseDTO.setMenuOrgFlag(0);
                        } else if (FuncVisibleType.SINGLE_ORG.getCode().equals(header.getFuncVisibleTenantType())){
                            uapBaseDTO.setMenuOrgFlag(1);
                        } else if (FuncVisibleType.MULTI_ORG.getCode().equals(header.getFuncVisibleTenantType())){
                            uapBaseDTO.setMenuOrgFlag(2);
                        }
                        session.setUapBaseDTO(uapBaseDTO);
                    }
                }


            }
            header.setUapBaseParam(null);
        }
    }

    /**
     * 返回当前实例的方法及方法参数信息
     */
    @Override
    public CallResult<Map<String, ApiMethod>> getMethodAndParameters(RequestHeader requestHeader) {
        CallResult<Map<String, ApiMethod>> result = new CallResult<Map<String, ApiMethod>>(
                MessageUtil.buildSuccessMessage());
        try {
            result.setData(getMethodAndParameters(this.getClass(), requestHeader));
        } catch (Exception ex) {
            // nothing to do ....
            log.warn("解析方法信息时出现异常", ex);
        }
        return result;
    }

    /**
     * 固化provider的方法列表, 避免后端服务频繁创建heap对象, 这里初始化接口信息的好处有:
     * 1. 解决网关接口10秒过期后会调用getMethodAndParameters方法, 实际在服务启动后接口信息无任何变化, (不排除字节码被修改场景但不考虑)
     * 2. 在网关接口缓存过期的场景, 能提升接口调用性能
     */
    @PostConstruct
    public void init() {
        Class<?> clz = this.getClass();

        // 从别的地方移过来的
        for (Method method : clz.getMethods()) {
            if (!methodCache.containsKey(method.getName())) {
                methodCache.put(method.getName(), method);
            }
        }

        Method[] tmpMhs = clz.getDeclaredMethods();
        List<Method> clsMethods = Lists.newArrayList();

        // 当前类方法
        Collections.addAll(clsMethods, tmpMhs);

        // 父类方法
        if (!clz.getSuperclass().getName().equals(ServiceBaseAbstract.class.getName())) {
            tmpMhs = clz.getSuperclass().getDeclaredMethods();
            Collections.addAll(clsMethods, tmpMhs);
        }

        clsMethods = Collections.unmodifiableList(clsMethods);

//        LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
        StandardReflectionParameterNameDiscoverer discoverer = new StandardReflectionParameterNameDiscoverer();



        ApiMethod apiMethod = null;
        ApiParameter apiParameter = null;
        ApiParameter[] apiParameters = null;
        String paramTypes = "";

        for (Method method : clsMethods) {

            boolean webGatewayVisiabled = false;
            boolean requestSession = false;
            boolean requestVerifyCode = false;
            String aliasName = "";
            String note = "";
            boolean genericServiceBoolean = false;
            boolean requestAuthentication = true;
            boolean requestDomainAuthentication = true;
            String viewNo = "";
            String billNo = "";
            boolean needOrgAuth = false;

            if (clz.getSuperclass() != null &&
                    clz.getSuperclass().getName().equals(ServiceBaseAbstract.class.getName())) {
                requestSession = true;
                genericServiceBoolean = true;
            }

            if (method.isAnnotationPresent(com.yyigou.ddc.common.service.annotation.Method.class)) {
                com.yyigou.ddc.common.service.annotation.Method methodAnno =
                        (com.yyigou.ddc.common.service.annotation.Method)
                                method.getAnnotation(com.yyigou.ddc.common.service.annotation.Method.class);

                // 是否需要登录用户信息
                requestSession = methodAnno.requestSession();

                requestAuthentication = methodAnno.requestAuthentication();

                // 是否WEB网关可见
                webGatewayVisiabled = methodAnno.webGatewayVisiabled();

                // 是否需要请求验证码
                requestVerifyCode = methodAnno.requestVerifyCode();

                // 是否需要域鉴权
                requestDomainAuthentication = methodAnno.requestDomainAuthentication();

                // 取别名
                aliasName = methodAnno.aliasName();

                // 简介
                note = methodAnno.note();

                // uap 信息
                viewNo = methodAnno.viewNo();
                billNo = methodAnno.billNo();
                needOrgAuth = methodAnno.orgAuth();

            }

            String methodName = method.getName();
            Type[] types = method.getGenericParameterTypes();
            Class<?>[] pclzz = method.getParameterTypes();

            // 利用spring的工具类查找本地变量表中的参数名称
            String[] paramNames = discoverer.getParameterNames(method);

            if (paramNames != null) {
                apiParameters = new ApiParameter[paramNames.length];
                for (int i = 0; i < paramNames.length; i++) {
                    apiParameter = new ApiParameter();
                    apiParameter.setName(paramNames[i]);

                    if (types[i] instanceof ParameterizedType) {
                        ParameterizedType pType = (ParameterizedType) types[i];
                        paramTypes = pType.toString();
                    } else {
                        paramTypes = pclzz[i].getCanonicalName();
                    }

                    apiParameter.setTypeName(paramTypes);
                    apiParameters[i] = apiParameter;
                }

            } else {
                apiParameters = new ApiParameter[0];
            }

            apiMethod = new ApiMethod();
            apiMethod.setName(methodName);
            apiMethod.setAliasName(aliasName);
            apiMethod.setNote(note);
            apiMethod.setParameters(apiParameters);
            apiMethod.setBillNo(billNo);
            apiMethod.setViewNo(viewNo);
            apiMethod.setNeedOrgAuth(needOrgAuth);

            // 返回值类型及泛型类型
            String returnTypeName = method.getReturnType().getName();
            Type returnType = method.getGenericReturnType();
            if (returnType instanceof ParameterizedType) {
                ParameterizedType pType = (ParameterizedType) returnType;
                returnTypeName = pType.toString();
            } else {
                returnTypeName = method.getReturnType().getCanonicalName();
            }
            apiMethod.setReturnValueTypeName(returnTypeName);

            apiMethod.setRequestSession(requestSession);
            apiMethod.setRequestAuthentication(requestAuthentication);
            apiMethod.setRequestDomainAuthentication(requestDomainAuthentication);
            apiMethod.setRequestVerifyCode(requestVerifyCode);
            apiMethod.setRefreshTime(new Date());
            // 是否对网关开放
            apiMethod.setWebGatewayVisiabled(webGatewayVisiabled);
            // 是否支持网关的泛化调用
            apiMethod.setGenericServiceBoolean(genericServiceBoolean);

            methods.put(methodName, apiMethod);
        }

        log.info("total {} method loaded for provider {}", methods.size(), clz.getName());
    }

    /**
     * 返回当前实例的方法及方法参数信息
     *
     * @param requestHeader 请求参数
     * @return 当前服务的所有对外方法和参数信息
     */
    public final Map<String, ApiMethod> getMethodAndParameters(Class<?> clz, RequestHeader requestHeader) {
        // 没上层接口
        if (clz.getInterfaces().length == 0) {
            return Maps.newHashMap();
        }

        return methods;
    }


    /**
     * 添加call方法前置后置处理接口, order 越大则优先级越高
     * @param serviceBaseOperator
     * @param order
     */
    public static void addServiceBaseAround(ServiceBaseOperator serviceBaseOperator, Integer order) {
        int index = serviceBaseOperatorWrappers.size();
        for (int i = 0; i < serviceBaseOperatorWrappers.size(); i++) {
            ServiceBaseOperatorWrapper serviceBaseAroundWrapper = serviceBaseOperatorWrappers.get(i);
            if (serviceBaseAroundWrapper.getOrder().compareTo(order) < 0) {
                index = i;
                break;
            }
        }

        serviceBaseOperatorWrappers.add(index, new ServiceBaseOperatorWrapper(serviceBaseOperator, order));
        log.warn("当前 callResult 拦截器信息: {}", JSONObject.toJSONString(serviceBaseOperatorWrappers, SerializerFeature.PrettyFormat));
    }

    /**
     * 参数检查
     *
     * @param param      参数对象
     * @param paramIndex 参数所在方法入参的位置
     * @return
     */
    private MessageVO paramCheck(Map<String, Object> param, int paramIndex) {

        MessageVO msg = MessageUtil.buildSuccessMessage();

        if (param == null || param.isEmpty()) {
            msg.setCode("1");
            msg.setMessage("入参[" + paramIndex + "]不能为空。");
            return msg;
        }

        for (String key : param.keySet()) {
            if (param.get(key) instanceof JSONArray) {
                param.put(key, JSONParseUtil.jsonArray2List((JSONArray) param.get(key)));
            } else if (param.get(key) instanceof JSONObject) {
                param.put(key, JSONParseUtil.jsonObject2Map((JSONObject) param.get(key)));
            } else {
                json2JavaObject(param.get(key));
            }
        }

        return msg;

    }

    /**
     * 将json对象转换到java对象
     *
     * @param paramObject 数据对象
     * @return
     */
    @SuppressWarnings("unchecked")
    private void json2JavaObject(Object paramObject) {
        if (paramObject instanceof List<?>) {
            for (Object obj : (List<Object>) paramObject) {
                if (obj instanceof JSONArray) {
                    obj = JSONParseUtil.jsonArray2List((JSONArray) obj);
                } else if (obj instanceof JSONObject) {
                    obj = JSONParseUtil.jsonObject2Map((JSONObject) obj);
                } else {
                    json2JavaObject(obj);
                }
            }
        } else {
            // to do
        }
    }

    /**
     * 获取方法指定入参泛型List或数组的对象类型
     *
     * @param targetMethod 方法对象
     * @param paramIndex   入参位置
     * @return null 表示非集合、数组或获取失败
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    private Class<?> getSubType(Method targetMethod, int paramIndex) throws InstantiationException,
            IllegalAccessException {

        java.lang.reflect.Type[] types = targetMethod.getGenericParameterTypes();

        if (types[paramIndex] instanceof ParameterizedType) {
            ParameterizedType pType = (ParameterizedType) types[paramIndex];
            return (Class<?>) pType.getActualTypeArguments()[0];
        } else if (types[paramIndex] instanceof Class<?> && ((Class<?>) types[paramIndex]).isArray()) {
            return ((Class<?>) types[paramIndex]).getComponentType();
        } else {
            return null;
        }

    }

    /**
     * 基础类型格式化
     *
     * @param paramCls 参数类型信息
     * @param paramObj 参数值
     * @return
     */
    private Object baseTypeParse(Class<?> paramCls, Object paramObj) {

        if (paramCls.getName().equals("java.lang.Long")) {
            return Long.parseLong(paramObj.toString());
        } else if (paramCls.getName().equals("java.lang.Float")) {
            return Float.parseFloat(paramObj.toString());
        } else if (paramCls.getName().equals(Integer.class.getName())) {
            return Integer.parseInt(paramObj.toString());
        } else if (paramCls.getName().equals(Boolean.class.getName())) {
            return Boolean.parseBoolean(paramObj.toString());
        } else if (paramCls.getName().equals(BigDecimal.class.getName())) {
            return new BigDecimal(String.valueOf(paramObj));
        } else {
            return paramObj;
        }
    }
}
