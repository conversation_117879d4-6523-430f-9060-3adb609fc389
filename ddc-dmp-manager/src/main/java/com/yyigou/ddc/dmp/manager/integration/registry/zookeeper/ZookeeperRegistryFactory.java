
package com.yyigou.ddc.dmp.manager.integration.registry.zookeeper;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.common.URL;
import com.alibaba.dubbo.registry.Registry;
import com.alibaba.dubbo.registry.support.AbstractRegistryFactory;
import com.alibaba.dubbo.remoting.zookeeper.ZookeeperTransporter;

/**
 * ZookeeperRegistryFactory.
 * 
 * <AUTHOR>
 */
public class ZookeeperRegistryFactory extends AbstractRegistryFactory {
	
	private ZookeeperTransporter zookeeperTransporter;

    public void setZookeeperTransporter(ZookeeperTransporter zookeeperTransporter) {
		this.zookeeperTransporter = zookeeperTransporter;
	}

	/**
	 * 因为common-dubbo-registry-zookeeper包，开发测试和演练线上环境的包不一致；所以需要提供不同的Registry实现
	 * @param url
	 * @return
	 */
	public Registry createRegistry(URL url) {
		String activeProfile = System.getProperty("spring.profiles.active");
		if(StrUtil.isNotEmpty(activeProfile) && activeProfile.contains("dev")) {
			return new ZookeeperRegistryInDev(url, zookeeperTransporter);
		} else {
			return new ZookeeperRegistry(url, zookeeperTransporter);

		}

    }

}