package com.yyigou.ddc.dmp.manager.integration.number;

import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.exception.PojoErrorCode;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.service.number.center.api.NumberAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Component
public class NumberService {

    @Autowired
    private NumberAPI numberAPI;

    /**
     * 生成单个单号
     *
     * @return
     */
    public String generateNo(String billType) {
        CallResult<String> res = numberAPI.syncCreate(billType);
        if (res == null || res.getData() == null) {
            throw new BusinessException(PojoErrorCode.ADD_FAIL_MSG, "生成编码失败");
        }
        return res.getData();
    }

    public List<String> batchGenerateNoList(String billType, Integer size) {
        CallResult<List<String>> res = numberAPI.createBatch(billType,size);
        if (res == null || res.getData() == null) {
            throw new BusinessException(PojoErrorCode.ADD_FAIL_MSG, "生成编码失败");
        }
        return res.getData();
    }

    public String generateNo(String billType, String parentNo) {
        CallResult<String> res = numberAPI.createTreeNumber(billType, parentNo);
        if (res == null || res.getData() == null) {
            throw new BusinessException(PojoErrorCode.ADD_FAIL_MSG, "生成编码失败");
        }
        return res.getData();
    }

    public List<String> createBatchTreeNumber(String billType, String parentNo, int size) {
        CallResult<List<String>> res = numberAPI.createBatchTreeNumber(billType, parentNo, size);
        if (res == null || res.getData() == null) {
            throw new BusinessException(PojoErrorCode.ADD_FAIL_MSG, "生成编码失败");
        }
        return res.getData();
    }

}
