<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yyigou</groupId>
        <artifactId>service-ddc-dmp</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>ddc-dmp-manager</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-dubbo-registry-zookeeper</artifactId>
            <version>${common-dubbo-registry-zookeeper.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-meta-base</artifactId>
            <version>${common-meta-base.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yyigou.ddc</groupId>
                    <artifactId>common-persistent-mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baidu.unbiz</groupId>
                    <artifactId>fluent-validator-jsr303</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>number-center-api</artifactId>
            <version>${common_base_version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>2.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.yyigou.ddc.services</groupId>
            <artifactId>dlog-api</artifactId>
            <version>${common_base_version}</version>
        </dependency>
        <dependency>
            <groupId>com.yyigou</groupId>
            <artifactId>ddc-dmp-model</artifactId>
        </dependency>

    </dependencies>

</project>