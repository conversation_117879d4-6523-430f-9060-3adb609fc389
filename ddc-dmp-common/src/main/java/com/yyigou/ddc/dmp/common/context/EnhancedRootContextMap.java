package com.yyigou.ddc.dmp.common.context;

import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.Map;

public class EnhancedRootContextMap extends HashMap<String, Object> {

    // 1. 继承HashMap，保留Map的键值存储能力
    public EnhancedRootContextMap() {
        super();
    }

    public EnhancedRootContextMap(Map<String, Object> map) {
        super(map);
    }

    // 2. 添加可直接调用的自定义函数
    public static String ifFunction(boolean condition, String trueValue, String falseValue) {
        return condition ? trueValue : falseValue;
    }

    public static String toUpperCase(String input) {
        return input != null ? input.toUpperCase() : null;
    }

    public static int len(String str) {
        return StrUtil.length(str);
    }

    // 3. 增强方法：安全获取嵌套Map值（避免空指针）
    public Object safeGet(String keyPath) {
        String[] keys = keyPath.split("\\.");
        Map<String, Object> current = this;
        for (int i = 0; i < keys.length - 1; i++) {
            Object next = current.get(keys[i]);
            if (!(next instanceof Map)) return null;
            current = (Map<String, Object>) next;
        }
        return current.get(keys[keys.length - 1]);
    }
}
