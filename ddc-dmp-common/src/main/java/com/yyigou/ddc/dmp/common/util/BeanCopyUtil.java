package com.yyigou.ddc.dmp.common.util;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
public class BeanCopyUtil {

    public static <S, T> T copyFields(S s, Class<T> tClass) {
        BeanCopier beanCopier = BeanCopier.create(s.getClass(), tClass, false);
        T o = null;
        try {
            o = tClass.newInstance();
            beanCopier.copy(s, o, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return o;
    }


    public static <S, T> List<T> copyFieldsList(List<S> sList, Class<T> t) {
        List<T> resultList = ListUtil.list(false);
        if (CollectionUtils.isEmpty(sList)) {
            return resultList;
        }
        S s = sList.get(0);
        BeanCopier beanCopier = BeanCopier.create(s.getClass(), t, false);
        try {
            for (S member : sList) {
                T resultT = t.newInstance();
                beanCopier.copy(member, resultT, null);
                resultList.add(resultT);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }

    public static <S, T> T copyFieldsByJson(S s, Class<T> tClass) {
        String jsonStr = JSONUtil.toJsonStr(s);
        return JSONUtil.toBean(jsonStr, tClass);
    }

}
