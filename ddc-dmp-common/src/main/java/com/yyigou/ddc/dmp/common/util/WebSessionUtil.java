package com.yyigou.ddc.dmp.common.util;

import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.session.SessionUser;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
public class WebSessionUtil {

    private static final ThreadLocal<SessionUser> SESSION_USER_THREAD_LOCAL = ThreadLocal.withInitial(() -> null);
    private static final ThreadLocal<String> FUNC_NO_THREAD_LOCAL = ThreadLocal.withInitial(() -> null);

    public static final String EXT_MAP_BILL_VIEW_KEY = "billViewNo";


    public static void setSessionUser(SessionUser sessionUser) {
        SESSION_USER_THREAD_LOCAL.set(sessionUser);
    }

    public static void setFuncNo(String funcNo) {
        if (funcNo == null) {
            return;
        }
        FUNC_NO_THREAD_LOCAL.set(funcNo);
    }

    /**
     * 获取上下文中的用户信息，获取失败则抛出异常
     * @return
     */
    public static SessionUser getSessionUser() {
        SessionUser sessionUser = SESSION_USER_THREAD_LOCAL.get();
        if (sessionUser == null) {
            throw new BusinessException("SessionUser is null");
        }
        return sessionUser;
    }


    public static String getTenantNo() {
        SessionUser sessionUser = getSessionUser();
        return sessionUser.getTenantNo();
    }


    /**
     * 获取上下文中的用户信息，获取失败则返回null
     * @return
     */
    public static SessionUser getSessionUserIfPresent() {
        return SESSION_USER_THREAD_LOCAL.get();
    }


    public static String getFuncNo() {
        String funcNo = FUNC_NO_THREAD_LOCAL.get();
        if (funcNo == null) {
            throw new BusinessException("funcNo is null");
        }
        return funcNo;
    }


    /**
     * 获取上下文中的用户信息，获取失败则返回null
     * @return
     */
    public static String getFuncNoIfPresent() {
        return FUNC_NO_THREAD_LOCAL.get();
    }

    public static void clearSessionUser() {
        SESSION_USER_THREAD_LOCAL.remove();
    }

    public static void clearFuncNo() {
        FUNC_NO_THREAD_LOCAL.remove();
    }

}
