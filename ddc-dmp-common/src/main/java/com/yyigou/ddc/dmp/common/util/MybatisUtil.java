package com.yyigou.ddc.dmp.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.StaticLog;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.builder.xml.XMLMapperEntityResolver;
import org.apache.ibatis.builder.xml.XMLStatementBuilder;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.parsing.XNode;
import org.apache.ibatis.parsing.XPathParser;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.type.TypeHandlerRegistry;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;

/**
 * Mybatis构建sql工具类
 * Created by GMQ on 2022/8/20 14:39
 */
public class MybatisUtil {

    public static void main(String[] args) {
        String sqlTemplate = """
                # sql
                	select
                            rtc.*,
                            cu.user_name AS created_by_name,
                            uu.user_name AS updated_by_name
                        from ram_tmp_conf rtc
                         LEFT JOIN ${db_dms}.base_user_info cu ON cu.user_id = rtc.created_by
                         LEFT JOIN ${db_dms}.base_user_info uu ON uu.user_id = rtc.updated_by
                        <trim prefix="where" prefixOverrides="and | or">
                            <if test="tableName != null and tableName != ''">
                                AND rtc.table_name like concat('%',#{tableName},'%')
                            </if>
                        </trim>
                """;
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("db_dms","yyigou_dsrp");
        parameter.put("tableName","12213");
        queryList(sqlTemplate,parameter);
    }


    // 如果参数是String，则添加单引号， 如果是日期，则转换为时间格式器并加单引号； 对参数是null和不是null的情况作了处理
    private static String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            value = "'" + DateUtil.formatDateTime((Date) obj) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "";
            }
        }
        return value;
    }

    private static String showSql(Configuration configuration, BoundSql boundSql) {
        // 获取参数
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        // sql语句中多个空格都用一个空格代替
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
        if (CollUtil.isNotEmpty(parameterMappings) && parameterObject != null) {
            // 获取类型处理器注册器，类型处理器的功能是进行java类型和数据库类型的转换
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            // 如果根据parameterObject.getClass(）可以找到对应的类型，则替换
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = sql.replaceFirst("\\?",
                        Matcher.quoteReplacement(getParameterValue(parameterObject)));
            } else {
                // MetaObject主要是封装了originalObject对象，提供了get和set的方法用于获取和设置originalObject的属性值,主要支持对JavaBean、Collection、Map三种类型对象的操作
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                for (ParameterMapping parameterMapping : parameterMappings) {
                    String propertyName = parameterMapping.getProperty();
                    if (metaObject.hasGetter(propertyName)) {
                        Object obj = metaObject.getValue(propertyName);
                        sql = sql.replaceFirst("\\?",
                                Matcher.quoteReplacement(getParameterValue(obj)));
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        // 该分支是动态sql
                        Object obj = boundSql.getAdditionalParameter(propertyName);
                        sql = sql.replaceFirst("\\?",
                                Matcher.quoteReplacement(getParameterValue(obj)));
                    } else {
                        // 打印出缺失，提醒该参数缺失并防止错位
                        sql = sql.replaceFirst("\\?", "缺失");
                    }
                }
            }
        }
        return sql;
    }

    /**
     * 执行sql, 支持mybatis mapper.xml语法
     *
     * @param sql       执行的sql表达式
     * @param parameter 参数
     * @return
     */
    public static List<Map<String, Object>> queryList(String sql, Map<String, Object> parameter) {
        List<Map<String, Object>> result = null;
        try {
            Configuration configuration = new Configuration();
            StaticLog.info("sql:{}", sql);
            String uuid = IdUtil.fastSimpleUUID();
            String xml = StrUtil.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\"><mapper><select id=\"{}\" resultType=\"java.util.Map\">{}</select></mapper>", uuid, sql);
            XPathParser parser = new XPathParser(xml, true, configuration.getVariables(), new XMLMapperEntityResolver());
            XNode node = parser.evalNode("/mapper").evalNodes("select").get(0);
            XMLStatementBuilder xmlStatementBuilder = new XMLStatementBuilder(configuration, new MapperBuilderAssistant(configuration, xml), node, null);
            xmlStatementBuilder.parseStatementNode();
            MappedStatement mappedStatement = configuration.getMappedStatement(uuid);

            StaticLog.info("==> Preparing: {}", mappedStatement.getBoundSql(parameter).getSql());
            StaticLog.info("==> Parameters: {}", mappedStatement.getBoundSql(parameter).getParameterObject());
            String formatSql = showSql(configuration, mappedStatement.getBoundSql(parameter));
            StaticLog.info("==> FormatSql: {}", formatSql);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

//    /**
//     * 执行sql,查询map 支持mybatis mapper.xml语法
//     *
//     * @param sql       执行的sql表达式
//     * @param parameter 参数
//     * @return
//     */
//    public Map<String, Object> queryMap(String sql, Map<String, Object> parameter) {
//        Map<String, Object> result = null;
//        try {
//            Configuration configuration = sqlSessionTemplate.getConfiguration();
//            StaticLog.info("sql:{}", sql);
//            String uuid = IdUtil.fastSimpleUUID();
//            String xml = StrUtil.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\"><mapper><select id=\"{}\" resultType=\"java.util.Map\">{}</select></mapper>", uuid, sql);
//            XPathParser parser = new XPathParser(xml, true, configuration.getVariables(), new XMLMapperEntityResolver());
//            XNode node = parser.evalNode("/mapper").evalNodes("select").get(0);
//            XMLStatementBuilder xmlStatementBuilder = new XMLStatementBuilder(configuration, new MapperBuilderAssistant(configuration, xml), node, null);
//            xmlStatementBuilder.parseStatementNode();
//            MappedStatement mappedStatement = configuration.getMappedStatement(uuid);
//
//            StaticLog.info("==> Preparing: {}", mappedStatement.getBoundSql(parameter).getSql());
//            StaticLog.info("==> Parameters: {}", mappedStatement.getBoundSql(parameter).getParameterObject());
//            String formatSql = showSql(configuration, mappedStatement.getBoundSql(parameter));
//            StaticLog.info("==> FormatSql: {}", formatSql);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result;
//    }



}


