package com.yyigou.ddc.dmp.common.util;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidator;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 对象参数验证工具, 解决非controller层数据校验问题， @Validated、@Valid
 *
 * @author: Moore
 * @date: 2022/10/14 08:52
 * @version: 1.0.0
 */
@Slf4j
public class ValidatorUtil {
    private static final Validator validator;
    private static final Validator nofailFastValidator;

    static {
        validator = Validation.byProvider(HibernateValidator.class).configure()
                // 快速失败
                .failFast(true)
                .buildValidatorFactory().getValidator();
        nofailFastValidator = Validation.byProvider(HibernateValidator.class).configure()
                // 快速失败
                .failFast(false)
                .buildValidatorFactory().getValidator();
        String regex = "\\[this\\.([\\w]+)\\]";
        EL_PATTERN = Pattern.compile(regex);
    }

    // 正则表达式，匹配 {this.xxx}
    public static Pattern EL_PATTERN;

    /**
     * 校验对象入参
     *
     * @param object 待校验对象
     * @throws BusinessException 校验不通过，则报业务异常
     * @return:
     */
    public static void validateParams(Object object) throws BusinessException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object);
        if (!constraintViolations.isEmpty()) {
            String msg = constraintViolations.stream().map(ValidatorUtil::getMessageSupportEl).collect(Collectors.joining("||"));
            throw new BusinessException(msg);
        }
    }

    public static void validateParamsInNoFailFast(Object object, List<String> msgList) throws BusinessException {
        Set<ConstraintViolation<Object>> constraintViolations = nofailFastValidator.validate(object);
        if (!constraintViolations.isEmpty()) {
            List<String> errorMsg = constraintViolations.stream().map(ValidatorUtil::getMessageSupportEl).collect(Collectors.toList());
            msgList.addAll(errorMsg);
        }
    }

    /**
     * 获取校验消息，替换其中的[this.xxx]
     * 如果是this.xxx 则通过反射获取目标对象的属性值然后替换
     *
     * @param constraintViolation
     */
    public static String getMessageSupportEl(ConstraintViolation<Object> constraintViolation) {
        String message = constraintViolation.getMessage();
        if(StrUtil.isNotBlank(message)) {
            try {
                Matcher matcher = EL_PATTERN.matcher(message);
                StringBuffer sb = new StringBuffer();
                while (matcher.find()) {
                    // 获取匹配到的xxx部分
                    Object leafBean = constraintViolation.getLeafBean();
                    String fieldKey = matcher.group(1);
                    String replacement = "";
                    if(leafBean != null) {
                        Object fieldValue = ReflectUtil.getFieldValue(leafBean, fieldKey);
                        if(fieldValue != null) {
                            replacement = fieldValue.toString();
                        }
                    }
                    // 替换匹配到的子串
                    matcher.appendReplacement(sb, replacement);
                }
                matcher.appendTail(sb);
                return sb.toString();
            } catch (Exception e) {
                log.error("动态生成错误消息异常",e);
            }
            return message;
        }

        return message;

    }

    /**
     * 校验分组入参
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     * @throws BusinessException 校验不通过，则报业务异常
     */
    public static void validateParams(Object object, Class<?>... groups) throws BusinessException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            String msg = constraintViolations.stream().map(ValidatorUtil::getMessageSupportEl).collect(Collectors.joining("||"));
            throw new BusinessException(msg);
        }
    }

    /**
     * 校验分组入参 不保存
     *
     * @param t    待校验对象
     * @param failFirst true->只返回第一个异常;false->返回所有异常用||拼接
     * @param groups    待校验的组
     */
    public static <T> String validateParamsNoThrowEx(T t, boolean failFirst, Class<?>... groups){
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(t, groups);
        String msg = null;
        if (!constraintViolations.isEmpty()) {
            if (failFirst) {
                msg= constraintViolations.stream().map(ConstraintViolation::getMessage).findFirst().orElse("");
            } else {
                msg = constraintViolations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining("||"));
            }
        }
        return msg;
    }

    /**
     * 校验参数为空且抛出异常
     *
     * @param target
     * @param errorMessage
     * @return:
     */
    public static void checkEmptyThrowEx(Object target, String errorMessage) {
        if (checkEmpty(target)) {
            throw new BusinessException( errorMessage);
        }
    }


    public static void checkEmptyThrowEx(Object target, String errorMessage, boolean throwEx, List<String> msgList) {
        if (checkEmpty(target)) {
            if(throwEx) {
                throw new BusinessException(errorMessage);
            }
            msgList.add(errorMessage);
        }
    }

    public static void checkEmptyThrowExWithArgs(Object target, String errorMessage, Object... args) {
        if (checkEmpty(target)) {
            String msg = StrUtil.format(errorMessage,args);
            throw new BusinessException( msg);
        }
    }


    /**
     * 校验参数是否为空
     *
     * @param target
     * @return: {@link boolean}
     */
    public static boolean checkEmpty(Object target) {
        if (null == target) {
            return true;
        }
        if (target instanceof String && StrUtil.isEmpty(String.valueOf(target))) {
            return true;
        }
        if (target instanceof Collection && CollectionUtils.isEmpty((Collection) target)) {
            return true;
        }
        return false;
    }


    /**
     * 如果校验为真，抛出异常
     * @param result
     * @param errorMessage
     */
    public static void checkTrueThrowEx(boolean result, String errorMessage) {
        if (result) {
            throw new BusinessException( errorMessage);
        }
    }


    /**
     * 如果校验为真，抛出异常
     * @param result
     * @param errorMessage
     */
    public static void checkTrueThrowEx(boolean result, String errorMessage, boolean throwEx, List<String> msgList) {
        if (result) {
            if(throwEx) {
                throw new BusinessException(errorMessage);
            }
            msgList.add(errorMessage);
        }
    }


}
