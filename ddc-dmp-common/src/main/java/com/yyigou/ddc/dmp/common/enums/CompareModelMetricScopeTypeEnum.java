package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CompareModelMetricScopeTypeEnum {

    ALL(1, "全部"),

    SPECIFY(2, "指定");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompareModelMetricScopeTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompareModelMetricScopeTypeEnum> map = new HashMap<>();

    static {
        for (CompareModelMetricScopeTypeEnum item : CompareModelMetricScopeTypeEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static CompareModelMetricScopeTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }

}
