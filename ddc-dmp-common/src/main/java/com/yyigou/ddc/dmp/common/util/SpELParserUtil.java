package com.yyigou.ddc.dmp.common.util;

import com.yyigou.ddc.dmp.common.context.EnhancedRootContextMap;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.CompositeStringExpression;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.SpelNode;
import org.springframework.expression.spel.ast.CompoundExpression;
import org.springframework.expression.spel.ast.PropertyOrFieldReference;
import org.springframework.expression.spel.ast.VariableReference;
import org.springframework.expression.spel.standard.SpelExpression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.*;

public class SpELParserUtil {

    private static final ExpressionParser expressionParser = new SpelExpressionParser();

    private static final ParserContext templateContext = new TemplateParserContext("${", "}");

    /**
     * 将变量放在root对象中，不是标准的el表达式
     *
     * @param template
     * @param variables
     * @param tClass
     * @param <T>
     * @return
     */
    public static <T> T parse(String template, Map<String, Object> variables, Class<T> tClass) {
        if (variables == null) {
            variables = new HashMap<>();
        }
        StandardEvaluationContext context = new StandardEvaluationContext(new EnhancedRootContextMap(variables));

        context.addPropertyAccessor(new SafeMapAccessor());

        // 2. 注册自定义函数（可选）
        registerCustomFunctions(context);

        // 3. 注入变量
        variables.forEach(context::setVariable);

        // 4. 解析并执行表达式
        Expression exp = expressionParser.parseExpression(template, templateContext);
        return exp.getValue(context, tClass);
    }

    public static String parse(String template, Map<String, Object> variables) {
        return parse(template, variables, String.class);
    }





    // 注册自定义函数到上下文
    private static void registerCustomFunctions(StandardEvaluationContext context) {
        try {
            context.registerFunction("if", EnhancedRootContextMap.class.getDeclaredMethod("ifFunction", boolean.class, String.class, String.class));
            context.registerFunction("len", EnhancedRootContextMap.class.getDeclaredMethod("len", String.class));
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 提取字符串模板中用到了哪些变量
     * @param template
     * @return
     */
    public static Set<String> extractVariables(String template) {
        ExpressionParser parser = new SpelExpressionParser();
        TemplateParserContext context = new TemplateParserContext("${", "}");

        Expression expression = parser.parseExpression(template, context);
        return extractVariablesFromExpression(expression);
    }

    private static Set<String> extractVariablesFromExpression(Expression expr) {
        Set<String> variables = new LinkedHashSet<>();

        // 1. 处理混合模板表达式（CompositeStringExpression）
        if (expr instanceof CompositeStringExpression) {
            CompositeStringExpression compositeExpr = (CompositeStringExpression) expr;
            for (Expression subExpr : compositeExpr.getExpressions()) {
                // 递归解析子表达式
                variables.addAll(extractVariablesFromExpression(subExpr));
            }
        }
        // 2. 处理纯SpEL表达式（SpelExpression）
        else if (expr instanceof SpelExpression) {
            SpelNode rootNode = ((SpelExpression) expr).getAST();
            traverseAST(rootNode, variables, 0);
        }
        return variables;
    }

    // 递归遍历语法树提取变量
    private static void traverseAST(SpelNode node, Set<String> variables, int childrenIndex) {
        if (node instanceof VariableReference) {
            String varName = node.toStringAST().replace("#", "");
            variables.add(varName);
        } else if (node instanceof PropertyOrFieldReference) {
            // 比如${person.className} 中的person和className都算作属性，只是person是root的属性。
            // 其实这两个都不算变量，但是在我们自定义的语义下，我们希望访问到root的属性也算作变量提取出来
            if (childrenIndex == 0) {
                variables.add(node.toStringAST());
            } else {
//                System.out.println(node.toStringAST());
            }
        }
        for (int i = 0; i < node.getChildCount(); i++) {
            if (node instanceof CompoundExpression) {
                traverseAST(node.getChild(i), variables, i);
            } else {
                traverseAST(node.getChild(i), variables, 0);
            }
        }
    }


}
