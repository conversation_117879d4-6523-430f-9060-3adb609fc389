package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CompareModelDimTypeEnum {

    COMPARE_OBJECT(1, "比对对象"),

    COMPARE_DIM(2, "比对维度");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompareModelDimTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompareModelDimTypeEnum> map = new HashMap<>();

    static {
        for (CompareModelDimTypeEnum item : CompareModelDimTypeEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static CompareModelDimTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }

}
