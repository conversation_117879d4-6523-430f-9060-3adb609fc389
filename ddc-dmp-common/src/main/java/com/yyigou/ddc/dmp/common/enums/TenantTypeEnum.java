package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum TenantTypeEnum {
    PERSONAL(0, "个人"),
    ENTERPRISE(1, "独立企业"),
    PLATFORM(2, "平台"),
    GROUP(3, "集团"),
    SUB_ENTERPRISE(4, "子租户");

    private final Integer value;
    private final String name;
    private static final Map<Integer, TenantTypeEnum> maps = new HashMap();

    private TenantTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static TenantTypeEnum getByValue(Integer value) {
        return value == null ? null : (TenantTypeEnum)maps.get(value);
    }

    public Integer getValue() {
        return this.value;
    }

    public String getName() {
        return this.name;
    }

    static {
        for(TenantTypeEnum item : values()) {
            maps.put(item.getValue(), item);
        }

    }
}