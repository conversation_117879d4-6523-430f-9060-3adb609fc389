package com.yyigou.ddc.dmp.common.util;

import org.springframework.context.expression.MapAccessor;
import org.springframework.expression.AccessException;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.TypedValue;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.Map;

public class SafeMapAccessor extends MapAccessor {

    @Override
    public boolean canRead(EvaluationContext context, Object target, String name) {
        // 只要 target 是 Map 类型，就允许读取（无论 Key 是否存在）
        return (target instanceof Map);
    }

    @Override
    public TypedValue read(EvaluationContext context, @Nullable Object target, String name) throws AccessException {
        Assert.state(target instanceof Map, "Target must be of type Map");
        Map<?, ?> map = (Map<?, ?>) target;
        Object value = map.get(name);
//        if (value == null && !map.containsKey(name)) {
//            throw new MapAccessException(name);
//        }
        return new TypedValue(value);
    }
}
