package com.yyigou.ddc.dmp.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.Map;

/**
 * 日志工具类
 * 提供统一的日志记录方法和业务日志封装
 */
public class LogUtil {
    
    /**
     * 记录业务操作日志
     * 
     * @param logger 日志记录器
     * @param operation 操作类型
     * @param description 操作描述
     * @param params 操作参数
     */
    public static void logBusiness(Logger logger, String operation, String description, Object... params) {
        String originalOperation = MDC.get("operation");
        try {
            MDC.put("operation", operation);
            logger.info("[BUSINESS] {} - {}", description, formatParams(params));
        } finally {
            if (originalOperation != null) {
                MDC.put("operation", originalOperation);
            } else {
                MDC.remove("operation");
            }
        }
    }
    
    /**
     * 记录数据库操作日志
     * 
     * @param logger 日志记录器
     * @param table 表名
     * @param operation 操作类型（INSERT/UPDATE/DELETE/SELECT）
     * @param condition 操作条件
     * @param result 操作结果
     */
    public static void logDatabase(Logger logger, String table, String operation, String condition, Object result) {
        String originalOperation = MDC.get("operation");
        try {
            MDC.put("operation", "DB_" + operation);
            MDC.put("table", table);
            logger.info("[DATABASE] {} {} - Condition: {} - Result: {}", operation, table, condition, result);
        } finally {
            if (originalOperation != null) {
                MDC.put("operation", originalOperation);
            } else {
                MDC.remove("operation");
            }
            MDC.remove("table");
        }
    }
    
    /**
     * 记录API调用日志
     * 
     * @param logger 日志记录器
     * @param apiName API名称
     * @param url 请求URL
     * @param method 请求方法
     * @param params 请求参数
     * @param response 响应结果
     * @param duration 耗时（毫秒）
     */
    public static void logApiCall(Logger logger, String apiName, String url, String method, 
                                  Object params, Object response, long duration) {
        String originalOperation = MDC.get("operation");
        try {
            MDC.put("operation", "API_CALL");
            MDC.put("apiName", apiName);
            MDC.put("duration", String.valueOf(duration));
            logger.info("[API_CALL] {} {} {} - Params: {} - Response: {} - Duration: {}ms", 
                    method, url, apiName, formatParams(params), formatResponse(response), duration);
        } finally {
            if (originalOperation != null) {
                MDC.put("operation", originalOperation);
            } else {
                MDC.remove("operation");
            }
            MDC.remove("apiName");
            MDC.remove("duration");
        }
    }
    
    /**
     * 记录性能日志
     * 
     * @param logger 日志记录器
     * @param methodName 方法名
     * @param duration 耗时（毫秒）
     * @param params 方法参数
     */
    public static void logPerformance(Logger logger, String methodName, long duration, Object... params) {
        String originalOperation = MDC.get("operation");
        try {
            MDC.put("operation", "PERFORMANCE");
            MDC.put("duration", String.valueOf(duration));
            
            if (duration > 1000) {
                logger.warn("[PERFORMANCE] SLOW - {} took {}ms - Params: {}", 
                        methodName, duration, formatParams(params));
            } else {
                logger.info("[PERFORMANCE] {} took {}ms - Params: {}", 
                        methodName, duration, formatParams(params));
            }
        } finally {
            if (originalOperation != null) {
                MDC.put("operation", originalOperation);
            } else {
                MDC.remove("operation");
            }
            MDC.remove("duration");
        }
    }
    
    /**
     * 记录错误日志
     * 
     * @param logger 日志记录器
     * @param operation 操作类型
     * @param errorMessage 错误信息
     * @param throwable 异常对象
     * @param params 相关参数
     */
    public static void logError(Logger logger, String operation, String errorMessage, 
                               Throwable throwable, Object... params) {
        String originalOperation = MDC.get("operation");
        try {
            MDC.put("operation", operation);
            MDC.put("errorType", throwable != null ? throwable.getClass().getSimpleName() : "Unknown");
            
            if (throwable != null) {
                logger.error("[ERROR] {} - {} - Params: {}", operation, errorMessage, formatParams(params), throwable);
            } else {
                logger.error("[ERROR] {} - {} - Params: {}", operation, errorMessage, formatParams(params));
            }
        } finally {
            if (originalOperation != null) {
                MDC.put("operation", originalOperation);
            } else {
                MDC.remove("operation");
            }
            MDC.remove("errorType");
        }
    }
    
    /**
     * 添加用户上下文信息
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param userRole 用户角色
     */
    public static void setUserContext(String userId, String userName, String userRole) {
        if (userId != null) {
            MDC.put("userId", userId);
        }
        if (userName != null) {
            MDC.put("userName", userName);
        }
        if (userRole != null) {
            MDC.put("userRole", userRole);
        }
    }
    
    /**
     * 清除用户上下文信息
     */
    public static void clearUserContext() {
        MDC.remove("userId");
        MDC.remove("userName");
        MDC.remove("userRole");
    }
    
    /**
     * 添加业务上下文信息
     * 
     * @param contextMap 上下文信息Map
     */
    public static void setBusinessContext(Map<String, String> contextMap) {
        if (contextMap != null) {
            contextMap.forEach(MDC::put);
        }
    }
    
    /**
     * 获取当前追踪ID
     * 
     * @return 追踪ID
     */
    public static String getTraceId() {
        return MDC.get("traceId");
    }
    
    /**
     * 格式化参数
     */
    private static String formatParams(Object... params) {
        if (params == null || params.length == 0) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(formatSingleParam(params[i]));
        }
        sb.append("]");
        return sb.toString();
    }
    
    /**
     * 格式化单个参数
     */
    private static String formatSingleParam(Object param) {
        if (param == null) {
            return "null";
        }
        
        String str = param.toString();
        // 限制参数长度，避免日志过长
        if (str.length() > 200) {
            return str.substring(0, 200) + "...";
        }
        return str;
    }
    
    /**
     * 格式化响应结果
     */
    private static String formatResponse(Object response) {
        if (response == null) {
            return "null";
        }
        
        String str = response.toString();
        // 限制响应长度，避免日志过长
        if (str.length() > 500) {
            return str.substring(0, 500) + "...";
        }
        return str;
    }
    
    /**
     * 创建带有类名的Logger
     * 
     * @param clazz 类
     * @return Logger实例
     */
    public static Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }
}