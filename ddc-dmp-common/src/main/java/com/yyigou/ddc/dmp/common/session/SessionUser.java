package com.yyigou.ddc.dmp.common.session;

import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.dmp.common.util.WebSessionUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SessionUser extends SessionToken {
    /**
     * 平台用户
     */
    public final static String USER_TYPE_PLATFORM = "1";
    /**
     * 企业用户
     */
    public final static String USER_TYPE_ENTERPRISE = "0";
    /**
     * 个人用户
     */
    public final static String USER_TYPE_PERSONAL = "2";
    /**
     * 企业端（或说用户端）
     */
    public final static String SOURCE_DOMAIN_CLIENT = "CLIENT";
    /**
     * 平台端（或说运营端）
     */
    public final static String SOURCE_DOMAIN_PLATFORM = "PLATFORM";
    /**
     * 简体中文
     */
    public final static String LANGUAGE_CHS = "zh_CN";
    /**
     * 开放平台
     */
    public final static String SOURCE_DOMAIN_OPEN = "OPEN";
    private static final long serialVersionUID = 6608170286967602928L;
    /**
     * 用户类型：1-平台用户、2-非平台用户
     */
    private String userType;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 是否是主账号（企业/租户管理员）
     */
    private Boolean isMaster;
    /**
     * 租户编号
     *  如果想获取企业编号 参考 companyNo
     */
    private String enterpriseNo;
    /**
     * 租户名称
     *  如果想获取企业编号 参考 companyName
     */
    private String enterpriseName;
    /**
     * 企业认证状态
     */
    private String enterpriseStatus;
    /**
     * 企业标志图片
     */
    private String enterpriseLogo;
    /**
     * 用户在不同app中的角色 key:应用名称_角色标识 value:角色名称 如 key:YPT_PlatformUser //云平台平台用户角色
     * value: 平台管理员
     */
    private Map<String, String> appRoles = new HashMap<String, String>();
    /**
     * 用户（有权限操作的企业）列表
     */
    private Map<String, String> authedEnterpriseMaps = new HashMap<String, String>();
    /**
     * 是否虚拟用户，用于手机验证码校验后的临时授权
     */
    private boolean isVirtual = false;
    /**
     * 登录账号
     */
    private String loginAccount;
    /**
     * 当前用户拥有的应用
     */
    private List<JSONObject> appList;
    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 头像
     */
    private String userLogo;

    /**
     * 供应链用户类型
     */
    private String scmUserType;

    /**
     * 是否开通的ERP.oms的标记，true 表示开通
     */
    private Boolean openOmsFlag;

    /**
     * 所属分组
     */
    private Map<String, List<?>> appGroups;

    /**
     * 角色集合
     */
    private Map<String, List<?>> appRoleIds;

    /**
     * 扩展信息
     */
    private Map<String, Object> extInfo;

    /**
     * uapBaseDTO
     */
    private UapBaseDTO uapBaseDTO;

    /**
     * code 条件
     */
    private Map<String, Map<String, Object>> codeQueryConditionMap;

    /**
     * 员工编号 (todo 准确讲, 这里应该是employeeNo..)
     */
    private String employerNo;

    /**
     * 员工工号
     */
    private String employeeCode;

    /**
     * 是否为集团企业   0:独立公司   1:集团公司   2:集团下的子公司
     *
     * @deprecated UIM3多组织改造后不在使用, 后面版本将删除
     */
    private int enterpriseGroupType = 0;

    /**
     * 企业类型：cs:厂商、jxs：经销商，yy:医院
     */
    private String enterpriseType;

    /**
     * 来源域的类型：'PLATFORM' - 平台（运营端）， 'CLIENT' - 企业（客户端）
     */
    private String sourceDomainType = SOURCE_DOMAIN_CLIENT;

    /**
     * 语言文字，默认为简体中文
     */
    private String language = LANGUAGE_CHS;

    /**
     * 当前租户编号 (UIM3新增)
     */
    private String tenantNo;
    /**
     * 当前租户名称 (UIM3新增)
     */
    private String tenantName;
    /**
     * 当前租户状态(租户状态 1.启用 0. 未启用)
     */
    private Integer tenantStatus;
    /**
     * 当前租户类型 (UIM3新增)
     * 租户类型（个人、企业、平台）0、个人 1、企业 2、平台
     */
    private Integer tenantType;
    /**
     * 租户组织标记 (UIM3新增)
     * （个人、单组织、多组织）0.个人 1.单组织 2.多组织
     */
    private Integer tenantOrgFlag;

    /**
     * 租户所属企业编号 (UIM3新增)
     */
    private String companyNo;
    /**
     * 租户所属企业名称  (UIM3新增)
     */
    private String companyName;

    public SessionUser() {
        // 为新建的 sessionUser 放入视图编号
        SessionUser sessionUser = WebSessionUtil.getSessionUserIfPresent();
        if (sessionUser != null && sessionUser.getExtInfo() != null){
            HashMap<String, Object> newExtMap = new HashMap<>();
            if (sessionUser.getExtInfo().containsKey(WebSessionUtil.EXT_MAP_BILL_VIEW_KEY)){
                newExtMap.put(WebSessionUtil.EXT_MAP_BILL_VIEW_KEY, sessionUser.getExtInfo().get(WebSessionUtil.EXT_MAP_BILL_VIEW_KEY));
                this.extInfo = newExtMap;
            }
        }
    }

    public String getTenantNo() {
        return tenantNo;
    }

    public void setTenantNo(String tenantNo) {
        this.tenantNo = tenantNo;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getEnterpriseNo() {
        return enterpriseNo;
    }

    public void setEnterpriseNo(String enterpriseNo) {
        this.enterpriseNo = enterpriseNo;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public Map<String, String> getAppRoles() {
        return appRoles;
    }

    public void setAppRoles(Map<String, String> appRoles) {
        this.appRoles = appRoles;
    }

    public Boolean getIsMaster() {
        return isMaster;
    }

    public void setIsMaster(Boolean isMaster) {
        this.isMaster = isMaster;
    }


    public String getEnterpriseStatus() {
        return enterpriseStatus;
    }

    public void setEnterpriseStatus(String enterpriseStatus) {
        this.enterpriseStatus = enterpriseStatus;
    }

    public String getEnterpriseLogo() {
        return enterpriseLogo;
    }

    public void setEnterpriseLogo(String enterpriseLogo) {
        this.enterpriseLogo = enterpriseLogo;
    }

    public Map<String, String> getAuthedEnterpriseMaps() {
        return authedEnterpriseMaps;
    }

    public void setAuthedEnterpriseMaps(Map<String, String> authedEnterpriseMaps) {
        this.authedEnterpriseMaps = authedEnterpriseMaps;
    }

    public boolean getIsVirtual() {
        return this.isVirtual;
    }

    public void setIsVirtual(boolean isVirtual) {
        this.isVirtual = isVirtual;
    }

    public String getScmUserType() {
        return scmUserType;
    }

    public void setScmUserType(String scmUserType) {
        this.scmUserType = scmUserType;
    }

    public String getLoginAccount() {
        return loginAccount;
    }

    public void setLoginAccount(String loginAccount) {
        this.loginAccount = loginAccount;
    }


    public List<JSONObject> getAppList() {
        return appList;
    }

    public void setAppList(List<JSONObject> appList) {
        this.appList = appList;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getUserLogo() {
        return userLogo;
    }

    public void setUserLogo(String userLogo) {
        this.userLogo = userLogo;
    }

    public Boolean getOpenOmsFlag() {
        return openOmsFlag;
    }

    public void setOpenOmsFlag(Boolean openOmsFlag) {
        this.openOmsFlag = openOmsFlag;
    }

    public Map<String, List<?>> getAppGroups() {
        return appGroups;
    }

    public void setAppGroups(Map<String, List<?>> appGroups) {
        this.appGroups = appGroups;
    }

    public Map<String, List<?>> getAppRoleIds() {
        return appRoleIds;
    }

    public void setAppRoleIds(Map<String, List<?>> appRoleIds) {
        this.appRoleIds = appRoleIds;
    }

    public Map<String, Object> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, Object> extInfo) {
        this.extInfo = extInfo;
    }

    public String getEmployerNo() {
        return employerNo;
    }

    public void setEmployerNo(String employerNo) {
        this.employerNo = employerNo;
    }

    public int getEnterpriseGroupType() {
        return enterpriseGroupType;
    }

    public void setEnterpriseGroupType(int enterpriseGroupType) {
        this.enterpriseGroupType = enterpriseGroupType;
    }

    public String getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public Map<String, Map<String, Object>> getCodeQueryConditionMap() {
        return codeQueryConditionMap;
    }

    public void setCodeQueryConditionMap(Map<String, Map<String, Object>> codeQueryConditionMap) {
        this.codeQueryConditionMap = codeQueryConditionMap;
    }

    public String getSourceDomainType() {
        return sourceDomainType;
    }

    public void setSourceDomainType(String sourceDomainType) {
        this.sourceDomainType = sourceDomainType;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public UapBaseDTO getUapBaseDTO() {
        return uapBaseDTO;
    }

    public void setUapBaseDTO(UapBaseDTO uapBaseDTO) {
        this.uapBaseDTO = uapBaseDTO;
    }

    public Integer getTenantType() {
        return tenantType;
    }

    public void setTenantType(Integer tenantType) {
        this.tenantType = tenantType;
    }

    public Integer getTenantOrgFlag() {
        return tenantOrgFlag;
    }

    public void setTenantOrgFlag(Integer tenantOrgFlag) {
        this.tenantOrgFlag = tenantOrgFlag;
    }

    public Integer getTenantStatus() {
        return tenantStatus;
    }

    public void setTenantStatus(Integer tenantStatus) {
        this.tenantStatus = tenantStatus;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    @Override
    public String toString() {
        return "SessionUser{" +
                "userType='" + userType + '\'' +
                ", nickName='" + nickName + '\'' +
                ", isMaster=" + isMaster +
                ", enterpriseNo='" + enterpriseNo + '\'' +
                ", enterpriseName='" + enterpriseName + '\'' +
                ", enterpriseStatus='" + enterpriseStatus + '\'' +
                ", enterpriseLogo='" + enterpriseLogo + '\'' +
                ", appRoles=" + appRoles +
                ", authedEnterpriseMaps=" + authedEnterpriseMaps +
                ", isVirtual=" + isVirtual +
                ", loginAccount='" + loginAccount + '\'' +
                ", appList=" + appList +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", userLogo='" + userLogo + '\'' +
                ", scmUserType='" + scmUserType + '\'' +
                ", openOmsFlag=" + openOmsFlag +
                ", appGroups=" + appGroups +
                ", appRoleIds=" + appRoleIds +
                ", extInfo=" + extInfo +
                ", employerNo='" + employerNo + '\'' +
                ", enterpriseGroupType=" + enterpriseGroupType +
                ", enterpriseType='" + enterpriseType + '\'' +
                ", sourceDomainType='" + sourceDomainType + '\'' +
                ", language='" + language + '\'' +
                ", tenantNo='" + tenantNo + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", tenantStatus=" + tenantStatus +
                ", tenantType=" + tenantType +
                ", tenantOrgFlag=" + tenantOrgFlag +
                ", companyNo='" + companyNo + '\'' +
                ", companyName='" + companyName + '\'' +
                '}';
    }
}
