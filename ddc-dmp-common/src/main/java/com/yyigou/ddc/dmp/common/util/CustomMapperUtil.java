package com.yyigou.ddc.dmp.common.util;


import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.ParameterMode;
import org.apache.ibatis.parsing.XNode;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.type.TypeHandlerRegistry;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName CustomMapperUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/9 17:05
 */
public class CustomMapperUtil {

    public static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");

    // mybatis xml 文件起始字符串
    public static final String  xmlStartSql="<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n" +
            "<mapper namespace=\"customMapperUtils\">\n   <select id=\"selectData\"  parameterType=\"java.util.Map\">\n";

    // mybatis xml 文件结束字符串
    public static final String xmlEndSql="\n</select>\n </mapper>";

    // 查询语句ID
    public static final String statementSelectId="selectData";

    public static void main(String[] args) {
        String  sourceSql="<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n" +
                "<mapper namespace=\"customMapperUtils\">\n" +
                "    <select id=\"demoPage\"  parameterType=\"java.util.Map\">\n" +
                "        SELECT a.*, cu.username as createPerName\n" +
                "        FROM bdp_alarm a\n" +
                "        LEFT JOIN bdp_alarm_received ar on ar.alarm_id = a.id\n" +
                "        LEFT JOIN sys_user cu on cu.user_id = a.create_per\n" +
                "        <where>\n" +
                "            <if test=\"id != null\">\n" +
                "                and a.id = #{id}\n" +
                "            </if>\n" +
                "            <if test=\"alarmIds != null\">\n" +
                "                and a.id in\n" +
                "                <foreach item=\"item\" index=\"index\" collection=\"alarmIds\" open=\"(\" separator=\",\" close=\")\">\n" +
                "                    #{item}\n" +
                "                </foreach>\n" +
                "            </if>\n" +
                "            <if test=\"name != null and name != ''\">\n" +
                "                and a.name like concat(\"%\",#{name},\"%\")\n" +
                "            </if>\n" +
                "            <if test=\"createPer != null and createPer != ''\">\n" +
                "                and cu.username like concat(\"%\",#{createPer},\"%\")\n" +
                "            </if>\n" +
                "            <if test=\"receivedPer != null\">\n" +
                "                and ar.received_name like concat(\"%\", #{receivedPer} ,\"%\")\n" +
                "            </if>\n" +
                "            <if test=\"triggerCondition != null\">\n" +
                "                and a.trigger_condition = #{triggerCondition}\n" +
                "            </if>\n" +
                "            <if test=\"userIdList!=null and userIdList.size()>0\">\n" +
                "                and a.create_per in\n" +
                "                <foreach collection=\"userIdList\" open=\"(\" close=\")\" separator=\",\" item=\"item\">\n" +
                "                    #{item}\n" +
                "                </foreach>\n" +
                "            </if>\n" +
                "        </where>\n" +
                "        GROUP BY a.id\n" +
                "        ORDER BY a.create_time desc\n" +
                "    </select>\n" +
                "</mapper>";

        String  selectSql="WITH\n" +
                "\n" +
                "-- 公共参数\n" +
                "\n" +
                "common_param AS (\n" +
                "\n" +
                "    SELECT cp_tbl.enterprise_no,\n" +
                "\n" +
                "           cp_tbl.param_code,\n" +
                "\n" +
                "           cp_tbl.param_name,cp_tbl.sort\n" +
                "\n" +
                "    FROM mysql_rds.yyigou_dsrp.bdc_common_param AS cp_tbl\n" +
                "\n" +
                "    WHERE cp_tbl.enterprise_no = '${enterprise_no}'\n" +
                "\n" +
                "      AND cp_tbl.status = 1\n" +
                "\n" +
                "      AND cp_tbl.deleted = 0\n" +
                "\n" +
                "      order by cp_tbl.sort\n" +
                "\n" +
                "), SELECT a.*, cu.username as createPerName\n" +
                "        FROM bdp_alarm a\n" +
                "        LEFT JOIN bdp_alarm_received ar on ar.alarm_id = a.id\n" +
                "        LEFT JOIN sys_user cu on cu.user_id = a.create_per\n" +
                "        <where>\n" +
                "            <if test=\"id != null\">\n" +
                "                and a.id = #{id}\n" +
                "            </if>\n" +
                "            <if test=\"alarmIds != null\">\n" +
                "                and a.id in\n" +
                "                <foreach item=\"item\" index=\"index\" collection=\"alarmIds\" open=\"(\" separator=\",\" close=\")\">\n" +
                "                    #{item}\n" +
                "                </foreach>\n" +
                "            </if>\n" +
                "            <if test=\"name != null and name != ''\">\n" +
                "                and a.name like concat(\"%\",#{name},\"%\")\n" +
                "            </if>\n" +
                "            <if test=\"createPer != null and createPer != ''\">\n" +
                "                and cu.username like concat(\"%\",#{createPer},\"%\")\n" +
                "            </if>\n" +
                "            <if test=\"receivedPer != null\">\n" +
                "                and ar.received_name like concat(\"%\", #{receivedPer} ,\"%\")\n" +
                "            </if>\n" +
                "            <if test=\"triggerCondition != null\">\n" +
                "                and a.trigger_condition = #{triggerCondition}\n" +
                "            </if>\n" +
                "            <if test=\"userIdList!=null and userIdList.size()>0\">\n" +
                "                and a.create_per in\n" +
                "                <foreach collection=\"userIdList\" open=\"(\" close=\")\" separator=\",\" item=\"item\">\n" +
                "                    #{item}\n" +
                "                </foreach>\n" +
                "            </if>\n" +
                "        </where>\n" +
                "        GROUP BY a.id\n" +
                "        ORDER BY a.create_time desc";
        HashMap paramMap = new HashMap();
        paramMap.put("id","ad");
        paramMap.put("name","name1");
        paramMap.put("createPer","createPer1");
        paramMap.put("receivedPer","receivedPer1");
        paramMap.put("triggerCondition","triggerCondition1");
        ArrayList userIdList=new ArrayList();
        userIdList.add(1);
        userIdList.add(2);
        userIdList.add(3);
        paramMap.put("userIdList",userIdList);
        String statementId="selectData";
        String sql= parseMybatisSelectSql(selectSql,paramMap);
        System.out.println("获取到的:statementId="+statementId+"语句的最终执行sql:\n"+sql);

    }


    /**
     * @Description  解析含有mybatis 标签的查询sql,只需要传sql过来就行,方法自动拼接前后mybatis xml文件前后字符
     * <AUTHOR>
     * @Date   2021/6/10 9:37
     */
    public static  String parseMybatisSelectSql(String selectSql,Map paramMap){
        System.out.println("原始查询sql:\n"+selectSql);
        StringBuilder sqlBuilder=new StringBuilder();
        sqlBuilder.append(xmlStartSql).append(selectSql).append(xmlEndSql);
        String sourceSql=sqlBuilder.toString();
        System.out.println("原始mybatis xml sql:\n"+sourceSql);
        //解析
        Configuration configuration=new Configuration();
        InputStream inputStream = new ByteArrayInputStream(sourceSql.getBytes(StandardCharsets.UTF_8));
        XMLMapperBuilder xmlMapperBuilder = new XMLMapperBuilder(inputStream, configuration, sourceSql, new HashMap<String, XNode>());
        xmlMapperBuilder.parse();
        MappedStatement mappedStatement = xmlMapperBuilder.getConfiguration().getMappedStatement(statementSelectId);
        BoundSql boundSql = mappedStatement.getBoundSql(paramMap);
        String  compileSql=boundSql.getSql();
        System.out.println("去掉mybatis标签后compileSql:\n"+compileSql);
        //替换参数值获取最终的sql
        String finalSql=replaceSqlParamValue(configuration,boundSql);
        System.out.println("替换参数值后finalSql:\n"+finalSql);
        return finalSql;
    }


    /**
     * @Description  解析含有mybatis 标签的sql (包含完整的xml文件字符串）
     * <AUTHOR>
     * @Date   2021/6/10 9:37
     */
    public static  String parseMybatisSql(String statementId,String sourceSql,Map paramMap){
        //解析
        Configuration configuration=new Configuration();
        InputStream inputStream = new ByteArrayInputStream(sourceSql.getBytes(StandardCharsets.UTF_8));

        XMLMapperBuilder xmlMapperBuilder = new XMLMapperBuilder(inputStream, configuration, sourceSql, new HashMap<String, XNode>());
        xmlMapperBuilder.parse();
        MappedStatement mappedStatement = xmlMapperBuilder.getConfiguration().getMappedStatement(statementId);
        BoundSql boundSql = mappedStatement.getBoundSql(paramMap);
        String  compileSql=boundSql.getSql();
        System.out.println("去掉mybatis标签后compileSql:\n"+compileSql);
        //替换参数值获取最终的sql
        String finalSql=replaceSqlParamValue(configuration,boundSql);
        System.out.println("替换参数值后finalSql:\n"+finalSql);
        return finalSql;
    }

    /**
     * @Description 替换sql参数值
     * <AUTHOR>
     * @Date   2021/6/10 9:37
     */
    private static String replaceSqlParamValue(Configuration configuration,BoundSql boundSql) {
        String  sql=boundSql.getSql();
        //美化Sql
        sql.replaceAll("[\\s\n]+", " ");
        // 填充占位符， 把传参填进去，使用#｛｝、${} 一样的方式
        Object parameterObject = boundSql.getParameterObject();
        //参数映射列表，有入的（in） 有出的（O），后面只遍历传入的参数，并且把传入的参数赋值成我们代码里的值
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();

        ArrayList<String> parmeters = new ArrayList<String>();
        if (parameterMappings != null) {
            MetaObject metaObject = parameterObject == null ? null : configuration.newMetaObject(parameterObject);
            for (int i = 0; i < parameterMappings.size(); i++) {
                ParameterMapping parameterMapping = parameterMappings.get(i);
                if (parameterMapping.getMode() != ParameterMode.OUT) {
                    //参数值
                    Object value;
                    String propertyName = parameterMapping.getProperty();
                    //获取参数名称
                    if (boundSql.hasAdditionalParameter(propertyName)) {
                        //获取参数值
                        value = boundSql.getAdditionalParameter(propertyName);
                    } else if (parameterObject == null) {
                        value = null;
                    } else if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                        //如果是单个值则直接赋值
                        value = parameterObject;
                    } else {
                        value = metaObject == null ? null : metaObject.getValue(propertyName);
                    }
                    //由于传参类型多种多样，数值型的直接把value的值传参进去即可，如果是字符串的、日期的，要在前后加上单引号‘’，才能到sql里判读语句里使用
                    if (value instanceof Number) {
                        parmeters.add(String.valueOf(value));
                    } else {
                        StringBuilder builder = new StringBuilder();
                        builder.append("'");
                        if (value instanceof Date) {
                            builder.append(simpleDateFormat.format((Date) value));
                        } else if (value instanceof String) {
                            builder.append(value);
                        }
                        builder.append("'");
                        parmeters.add(builder.toString());
                    }
                }
            }
        }
        //sql里的东西处理完了，都返回给我们声明list容器里了，接下来要用我们一开始获取到绑定的sql 来替换赋值，把真正 传参赋值的sql返回给计算引擎，spark或者flink
        for (String value : parmeters) {
            /*
             把占位符问号 逐个 从第一位去替换我们list里获取到的值。如果对ArrayList数组的顺序不放心，可以换成LinkList去实现，不需要考虑性能问题
             毕竟最多10来个参数，如果你写sql还要传入上千个的参数，那么就得好好反思，是不是要改成代码去实现了。
             */
            sql = sql.replaceFirst("\\?", value);
        }
        return sql;

    }
}
