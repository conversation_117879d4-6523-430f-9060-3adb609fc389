package com.yyigou.ddc.dmp.common.session;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
import java.io.Serializable;

public class SessionToken  implements Serializable {

    private static final long serialVersionUID = -5114213941329319191L;

    public static final String PLATFORM_DEFAULT = "DDC";

    /**
     * 会话状态的唯一编号
     */
    protected String token;

    /**
     * 会话对应的用户编码
     */
    protected String userNo;

    /**
     * 用户名称
     */
    protected String userName;

    /**
     * 前端身份编号，用于数据加密
     */
    protected String clientId = java.util.UUID.randomUUID().toString().replace("-", "");

    /**
     * 绑定到指定的IP，多个请用,号分隔，支持向前匹配，如 10.0.103. 表示支持 10.0.103.为前辍的所有IP
     * 此字段应用于内部应用或外部开放平台的静态TOKEN安全校验
     */
    protected String bindIpAddress;

    /**
     * 备用字段 前端生成并绑定到当前sessionId的验证码，一般用于在provider端验证与输入是否一致
     */
    protected String verifyCode;


    /**
     * 验证码流程优化, 是否跳过验证码环节
     */
    protected Boolean skipVerifyCode;

    /**
     * 验证码请求串的签名，签名内容为请求URL数
     */
    protected String verifyCodeRequestSign;

    /**
     * 用户请求的主机名
     */
    protected String host;

    /**
     * 用户请求用的IE类型
     */
    protected String userAgent;

    /**
     * 用户请求的源IP地址
     */
    protected String remoteIpAddr;

    /**
     * 用户请求的源端口
     */
    protected Integer remotePort;

    /**
     * 对应的平台，目前主要是 YPT、BRP、DDC
     */
    protected String platform = "DDC";

    /**
     * 引用URL，对应HTTP头中的 Referer
     */
    protected String referer;


    /**
     * 提示前端引导用户修改密码标记:
     * 	must: 必须引导用户修改密码
     * 	suggest : 提示用户需修改密码
     */
    protected String changePasswordFlag;


    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getBindIpAddress() {
        return bindIpAddress;
    }

    public void setBindIpAddress(String bindIpAddress) {
        this.bindIpAddress = bindIpAddress;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }


    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getVerifyCodeRequestSign() {
        return verifyCodeRequestSign;
    }

    public void setVerifyCodeRequestSign(String verifyCodeRequestSign) {
        this.verifyCodeRequestSign = verifyCodeRequestSign;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getRemoteIpAddr() {
        return remoteIpAddr;
    }

    public void setRemoteIpAddr(String remoteIpAddr) {
        this.remoteIpAddr = remoteIpAddr;
    }

    public Integer getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(Integer remotePort) {
        this.remotePort = remotePort;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    public Boolean getSkipVerifyCode() {
        return skipVerifyCode;
    }

    public void setSkipVerifyCode(Boolean skipVerifyCode) {
        this.skipVerifyCode = skipVerifyCode;
    }

    public String getChangePasswordFlag() {
        return changePasswordFlag;
    }

    public void setChangePasswordFlag(String changePasswordFlag) {
        this.changePasswordFlag = changePasswordFlag;
    }

    @Override
    public String toString() {
        return "SessionToken{" +
                "token='" + token + '\'' +
                ", userNo='" + userNo + '\'' +
                ", userName='" + userName + '\'' +
                ", clientId='" + clientId + '\'' +
                ", bindIpAddress='" + bindIpAddress + '\'' +
                ", verifyCode='" + verifyCode + '\'' +
                ", skipVerifyCode=" + skipVerifyCode +
                ", verifyCodeRequestSign='" + verifyCodeRequestSign + '\'' +
                ", host='" + host + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", remoteIpAddr='" + remoteIpAddr + '\'' +
                ", remotePort=" + remotePort +
                ", platform='" + platform + '\'' +
                ", referer='" + referer + '\'' +
                ", changePasswordFlag='" + changePasswordFlag + '\'' +
                '}';
    }

}
