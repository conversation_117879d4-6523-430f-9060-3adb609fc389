package com.yyigou.ddc.dmp.common.session;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * UAP单据基础DTO类
 */
@Data
public class UapBaseDTO implements Serializable {
    protected String transactionType;

    protected String bizFlowDefId;

    protected String bizFlowDefVersion;

    protected String bizFlowInstanceId;

    protected String queryPlanId;

    protected Map<String, Object> queryPlanConditions = new HashMap<>();

    protected Integer menuOrgFlag;
}
