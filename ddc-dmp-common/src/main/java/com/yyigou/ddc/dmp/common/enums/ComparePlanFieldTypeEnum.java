package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum ComparePlanFieldTypeEnum {

    ROW(1, "行维度"),
    COLUMN(2, "列维度"),
    VALUE(3, "值维度"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    ComparePlanFieldTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, ComparePlanFieldTypeEnum> map = new HashMap<>();

    static {
        for (ComparePlanFieldTypeEnum item : ComparePlanFieldTypeEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static ComparePlanFieldTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }

}
