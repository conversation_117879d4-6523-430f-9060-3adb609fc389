package com.yyigou.ddc.dmp.common.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum SqlJoinTypeEnum {

    INNER_JOIN("INNER_JOIN",1),
    LEFT_JOIN("LEFT_JOIN",2),
    RIGHT_JOIN("RIGHT_JOIN",3),
    ;

    private final String joinType;
    private final Integer joinTypeCode;

    SqlJoinTypeEnum(String joinType, Integer joinTypeCode) {
        this.joinType = joinType;
        this.joinTypeCode = joinTypeCode;
    }


    /**
     * 根据字符串值获取对应的 JOIN 枚举
     */
    public static SqlJoinTypeEnum getByJoinType(String type) {
        for (SqlJoinTypeEnum value : values()) {
            if (value.getJoinType().equalsIgnoreCase(type)) {
                return value;
            }
        }
        return null;
    }

    public static SqlJoinTypeEnum getByJoinTypeCode(Integer joinTypeCode) {
        for (SqlJoinTypeEnum value : values()) {
            if (value.getJoinTypeCode().equals(joinTypeCode)) {
                return value;
            }
        }
        return null;
    }

}
