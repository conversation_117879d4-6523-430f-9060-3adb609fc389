package com.yyigou.ddc.dmp.common.enums;


import lombok.Getter;

/**
 * SQL WHERE 条件支持的操作符枚举
 * <AUTHOR>
 */
@Getter
public enum SqlOperatorEnum {

    EQ("="),
    NE("<>"),
    GT(">"),
    GE(">="),
    LT("<"),
    LE("<="),
    LIKE("LIKE"),
    IN("IN"),
    NOT_IN("NOT IN"),
    IS_NULL("IS NULL"),
    IS_NOT_NULL("IS NOT NULL");

    private final String operator;

    SqlOperatorEnum(String operator) {
        this.operator = operator;
    }

    public static SqlOperatorEnum getByOperator(String operator) {
        for (SqlOperatorEnum value : values()) {
            if (value.operator.equals(operator)) {
                return value;
            }
        }
        return null;
    }

}

