package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CompareModelQueryConditionTypeEnum {

    COMPARE_DATASET(1, "比对数据集"),

    BASELINE_METRIC(2, "基准指标");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompareModelQueryConditionTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompareModelQueryConditionTypeEnum> map = new HashMap<>();

    static {
        for (CompareModelQueryConditionTypeEnum item : CompareModelQueryConditionTypeEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static CompareModelQueryConditionTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }

}
