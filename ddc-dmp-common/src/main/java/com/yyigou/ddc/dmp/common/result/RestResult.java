package com.yyigou.ddc.dmp.common.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一API响应结果包装类
 *
 * @param <T> 数据类型
 */
@Data
public class RestResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求路径
     */
    private String path;

    public RestResult() {
        this.timestamp = System.currentTimeMillis();
    }

    public RestResult(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public RestResult(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> RestResult<T> success() {
        return new RestResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
    }

    /**
     * 成功响应带数据
     */
    public static <T> RestResult<T> success(T data) {
        return new RestResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 成功响应带消息和数据
     */
    public static <T> RestResult<T> success(String message, T data) {
        return new RestResult<>(ResultCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 失败响应
     */
    public static <T> RestResult<T> error() {
        return new RestResult<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), ResultCode.INTERNAL_SERVER_ERROR.getMessage());
    }

    /**
     * 失败响应带消息
     */
    public static <T> RestResult<T> error(String message) {
        return new RestResult<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), message);
    }

    /**
     * 失败响应带错误码和消息
     */
    public static <T> RestResult<T> error(Integer code, String message) {
        return new RestResult<>(code, message);
    }

    /**
     * 失败响应带结果码枚举
     */
    public static <T> RestResult<T> error(ResultCode resultCode) {
        return new RestResult<>(resultCode.getCode(), resultCode.getMessage());
    }

    /**
     * 设置请求路径
     */
    public RestResult<T> path(String path) {
        this.path = path;
        return this;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
}