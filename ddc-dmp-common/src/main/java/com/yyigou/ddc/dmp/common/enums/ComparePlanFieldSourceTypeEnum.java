package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum ComparePlanFieldSourceTypeEnum {

    COMPARE_OBJECT(1, "比对对象"),
    COMPARE_DIM(2, "比对维度"),
    COMPARE_METRIC(3, "比对指标"),
    BASELINE_METRIC(4, "基准指标"),
    CALCULATED_METRIC(5, "计算指标"),
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    ComparePlanFieldSourceTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, ComparePlanFieldSourceTypeEnum> map = new HashMap<>();

    static {
        for (ComparePlanFieldSourceTypeEnum item : ComparePlanFieldSourceTypeEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static ComparePlanFieldSourceTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }

}
