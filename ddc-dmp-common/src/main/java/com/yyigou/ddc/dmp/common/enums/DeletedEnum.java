package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 删除标志枚举
 *
 * <AUTHOR> @date
 */
public enum DeletedEnum {
    DELETED(1, "已删除"),

    UN_DELETE(0, "未删除");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    DeletedEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, DeletedEnum> map = new HashMap<>();

    static {
        for (DeletedEnum item : DeletedEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static DeletedEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }
}
