package com.yyigou.ddc.dmp.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 启用状态枚举：1-启用，2-停用
 *
 * <AUTHOR>
 */
@Getter
public enum EnableStatusEnum {
    ENABLE(1, "启用"),
    DISABLE(2, "停用"),
    ;


    private static Map<Integer, EnableStatusEnum> MAP = new HashMap<>();

    static {
        for (EnableStatusEnum item : EnableStatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    private final Integer value;
    private final String name;

    EnableStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static EnableStatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        EnableStatusEnum goodsEnableStatusEnum = MAP.get(value);
        if(goodsEnableStatusEnum != null) {
            return goodsEnableStatusEnum.getName();
        }
        return "";
    }

}
