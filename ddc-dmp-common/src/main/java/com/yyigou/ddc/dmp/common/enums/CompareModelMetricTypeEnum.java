package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum CompareModelMetricTypeEnum {

    COMPARE_METRIC(1, "比对指标"),

    BASELINE_METRIC(2, "基准指标");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    CompareModelMetricTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, CompareModelMetricTypeEnum> map = new HashMap<>();

    static {
        for (CompareModelMetricTypeEnum item : CompareModelMetricTypeEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static CompareModelMetricTypeEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }

}
