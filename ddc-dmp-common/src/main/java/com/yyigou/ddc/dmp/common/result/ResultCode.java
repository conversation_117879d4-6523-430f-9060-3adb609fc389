package com.yyigou.ddc.dmp.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    REQUEST_TIMEOUT(408, "请求超时"),
    CONFLICT(409, "资源冲突"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误码 (6000-6999)
    BUSINESS_ERROR(6000, "业务处理失败"),
    VALIDATION_ERROR(6001, "参数校验失败"),
    DATA_NOT_FOUND(6002, "数据不存在"),
    DATA_ALREADY_EXISTS(6003, "数据已存在"),
    OPERATION_NOT_ALLOWED(6004, "操作不被允许"),
    DATA_INTEGRITY_VIOLATION(6005, "数据完整性约束违反"),

    // 数据库错误 (7000-7999)
    DATABASE_ERROR(7000, "数据库操作失败"),
    SQL_SYNTAX_ERROR(7001, "SQL语法错误"),
    CONNECTION_ERROR(7002, "数据库连接失败"),
    TRANSACTION_ERROR(7003, "事务处理失败"),

    // 外部服务错误 (8000-8999)
    EXTERNAL_SERVICE_ERROR(8000, "外部服务调用失败"),
    NETWORK_ERROR(8001, "网络连接异常"),
    TIMEOUT_ERROR(8002, "请求超时"),

    // 系统错误 (9000-9999)
    SYSTEM_ERROR(9000, "系统异常"),
    CONFIG_ERROR(9001, "配置错误"),
    FILE_OPERATION_ERROR(9002, "文件操作失败"),
    SERIALIZATION_ERROR(9003, "序列化/反序列化失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
}