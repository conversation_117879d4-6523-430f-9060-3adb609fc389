package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 状态--正常，无效
 *
 * <AUTHOR>
 */
public enum StatusEnum {

    EFFECTIVE(1, "有效"),

    INVALID(0, "无效"),
    HISTORY(2, "历史");
    ;


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    StatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, StatusEnum> MAP = new HashMap<>();

    static {
        for (StatusEnum item : StatusEnum.values()) {
            MAP.put(item.getValue(), item);
        }

    }

    public static StatusEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value);
    }

    public static String getNameByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return MAP.get(value) == null ? null : MAP.get(value).getName();
    }
}
