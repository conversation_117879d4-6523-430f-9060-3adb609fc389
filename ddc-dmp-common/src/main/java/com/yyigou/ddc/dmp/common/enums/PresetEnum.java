package com.yyigou.ddc.dmp.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 预置枚举
 *
 * <AUTHOR> @date
 */
public enum PresetEnum {
    PRESET(1, "平台端预置"),

    UN_PRESET(0, "租户端");


    private final Integer value;
    private final String name;

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    PresetEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    private static Map<Integer, PresetEnum> map = new HashMap<>();

    static {
        for (PresetEnum item : PresetEnum.values()) {
            map.put(item.getValue(), item);
        }

    }

    public static PresetEnum getByValue(final Integer value) {
        if (value == null) {
            return null;
        }
        return map.get(value);
    }
}
