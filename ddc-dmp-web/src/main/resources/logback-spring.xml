<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty name="log.level" source="logger.level" defaultValue="info"/>

    <!-- 设置日志级别 -->
    <root level="${log.level}">
        <appender-ref ref="STDOUT" />
<!--        <appender-ref ref="E" />-->
<!--        <appender-ref ref="W" />-->
<!--        <appender-ref ref="D" />-->
<!--        <appender-ref ref="NETWORK" />-->
    </root>

    <!-- 输出到控制台 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
<!--            <pattern>[%.-5level] [dsrp-gcs] %d{yyyy-MM-dd HH:mm:ss}: %relative [%thread] \( %file:%line \) - %msg%n</pattern>-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 错误日志 -->
    <appender name="E" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/ddc/logs/service-ddc-dmp/error.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/ddc/logs/service-ddc-dmp/error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%.-5level] [dsrp-gcs] %d{yyyy-MM-dd HH:mm:ss}: %relative [%thread] \( %file:%line \) - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 警告日志 -->
    <appender name="W" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/ddc/logs/service-ddc-dmp/warn.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/ddc/logs/service-ddc-dmp/warn.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%.-5level] [dsrp-gcs] %d{yyyy-MM-dd HH:mm:ss}: %relative [%thread] \( %file:%line \) - %msg%n</pattern>
        </encoder>
    </appender>


    <!-- 调试日志 -->
    <appender name="D" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/ddc/logs/service-ddc-dmp/service.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/ddc/logs/service-ddc-dmp/service.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%.-5level] [dsrp-gcs] %d{yyyy-MM-dd HH:mm:ss}: %relative [%thread] \( %file:%line \) - %msg%n</pattern>
        </encoder>
    </appender>


<!--    &lt;!&ndash; 网络日志 &ndash;&gt;-->
<!--    <appender name="NETWORK" class="com.yyigou.ddc.common.appender.TraceSocketAppender">-->
<!--        <threshold>WARN</threshold>-->
<!--        <port>${logstash.port:4561}</port>-->
<!--        <remoteHost>${logstash.ip:127.0.0.1}</remoteHost>-->
<!--        <reconnectionDelay>10000</reconnectionDelay>-->
<!--        <application>service-ddc-dmp</application>-->
<!--    </appender>-->

<!--    &lt;!&ndash; 设置某些类的日志级别 &ndash;&gt;-->
<!--    <logger name="org.apache.activemq.transport.AbstractInactivityMonitor" level="info" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--    </logger>-->
<!--    <logger name="org.apache.activemq.transport" level="INFO" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--    </logger>-->
<!--    <logger name="org.apache.zookeeper" level="INFO" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--    </logger>-->
<!--    <logger name="org.apache.zookeeper.ClientCnxn" level="warn" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--    </logger>-->
<!--    <logger name="org.I0Itec.zkclient" level="INFO" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--    </logger>-->
<!--    <logger name="org.apache.kafka.clients.NetworkClient" level="error" additivity="false" />-->
<!--    <logger name="com.alibaba.dubbo.common.Version" level="fatal" additivity="false" />-->
<!--    <logger name="com.alibaba.dubbo.remoting.transport.AbstractClient" level="error" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--        <appender-ref ref="E" />-->
<!--    </logger>-->
<!--    <logger name="com.alibaba.dubbo.registry.support.AbstractRegistry" level="error" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--        <appender-ref ref="E" />-->
<!--    </logger>-->
<!--    <logger name="com.alibaba.dubbo.rpc.protocol.dubbo.DubboProtocol" level="error" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--        <appender-ref ref="E" />-->
<!--    </logger>-->
<!--    <logger name="com.alibaba.dubbo.remoting.exchange.support.header" level="INFO" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--        <appender-ref ref="E" />-->
<!--    </logger>-->
<!--    <logger name="com.alibaba.dubbo.remoting.exchange.support.DefaultFuture" level="error" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--        <appender-ref ref="E" />-->
<!--    </logger>-->
<!--    <logger name="com.alibaba.dubbo.registry.zookeeper.ZookeeperRegistry" level="WARN" additivity="false">-->
<!--        <appender-ref ref="DUBBO" />-->
<!--        <appender-ref ref="E" />-->
<!--    </logger>-->
<!--    <logger name="org.springframework.jdbc.datasource.DataSourceTransactionManager" level="INFO" additivity="false">-->
<!--        <appender-ref ref="SQL" />-->
<!--    </logger>-->
<!--    <logger name="com.yyigou.ddc.dmp" level="DEBUG" additivity="false">-->
<!--        <appender-ref ref="D" />-->
<!--        <appender-ref ref="I" />-->
<!--        <appender-ref ref="W" />-->
<!--        <appender-ref ref="E" />-->
<!--&lt;!&ndash;        <appender-ref ref="NETWORK" />&ndash;&gt;-->
<!--    </logger>-->
<!--    <logger name="com.yyigou.ddc.services" level="warn" additivity="false">-->
<!--        <appender-ref ref="W" />-->
<!--        <appender-ref ref="E" />-->
<!--&lt;!&ndash;        <appender-ref ref="NETWORK" />&ndash;&gt;-->
<!--    </logger>-->
<!--    <logger name="dao" level="debug" additivity="false">-->
<!--        <appender-ref ref="SQL" />-->
<!--    </logger>-->

</configuration>