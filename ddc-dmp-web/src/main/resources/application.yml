server:
  port: 8080
  servlet:
    context-path: /ddc-dmp

# 日志配置统一在 logback-spring.xml 中管理

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.yyigou.ddc.dmp.dao.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  check-config-location: true

spring:
  application:
    name: service-ddc-dmp
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${service-ddc-dmp.jdbc.host}:${service-ddc-dmp.jdbc.port}/yyigou_ddc_dmp?useUnicode=true&allowMultiQueries=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
    #    url: ${service-ddc-dmp.jdbc.url}
    username: ${service-ddc-dmp.jdbc.username}
    password: ${service-ddc-dmp.jdbc.password}
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
    doris:
      url: jdbc:mysql://${service-ddc-dmp.doris.jdbc.host}:${service-ddc-dmp.doris.jdbc.port}?useUnicode=true&allowMultiQueries=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
    #      url:  ${service-ddc-dmp.doris.jdbc.url}
    meta:
      mysql:
        host: ${service-ddc-dmp.jdbc.host}
        port: ${service-ddc-dmp.jdbc.port}
        username: ${service-ddc-dmp.jdbc.username}
        password: ${service-ddc-dmp.jdbc.password}
      doris:
        host: ${service-ddc-dmp.doris.jdbc.host}
        port: ${service-ddc-dmp.doris.jdbc.port}
        username: ${common.doris.fe.username}
        password: ${common.doris.fe.password}