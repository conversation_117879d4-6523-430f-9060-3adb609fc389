#!/bin/bash
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
SERVER_NAME=`hostname`

JAVA_OPTS="
-Duser.timezone=Asia/Shanghai
-Djava.awt.headless=true
-Djava.net.preferIPv4Stack=true
-Dlog4j.configuratorClass=com.yyigou.ddc.common.zkconfig.EnhancedLog4jPropertyConfigurator
-Dzk.config.url=*************:2181
-Dzk.config.node.path=/ddc-config/common,/ddc-config/service-ddc-dmp
-Dlogstash.ip=*************
-Dlogstash.port=4561
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.math=ALL-UNNAMED
-Dspring.profiles.active=dev
"

PIDS=`ps -ef | grep java | grep ${DEPLOY_DIR} |awk '{print $2}'`
if [ -n "$PIDS" ]; then
    echo "ERROR: The $SERVER_NAME already started!"
    echo "PID: $PIDS"
    exit 1
fi

LOGS_DIR="$DEPLOY_DIR/logs"
if [ ! -d ${LOGS_DIR} ]; then
    mkdir -p ${LOGS_DIR}
fi
STDOUT_FILE=${LOGS_DIR}/`basename ${DEPLOY_DIR}`.log

GC_LOG_DIR=${LOGS_DIR}/gc
GC_LOG_FILE=${GC_LOG_DIR}/gc.log
DUMP_DIR=${LOGS_DIR}/dump
DUMP_FILE=${DUMP_DIR}/heap.hprof

if [ ! -d ${GC_LOG_DIR} ]; then
    mkdir ${GC_LOG_DIR}
fi
if [ ! -d ${DUMP_DIR} ]; then
    mkdir ${DUMP_DIR}
fi

JAVA_DEBUG_OPTS=""
if [ "$1" = "debug" ]; then
    JAVA_DEBUG_OPTS=" -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=8000,server=y,suspend=n "
fi
JAVA_JMX_OPTS=""
if [ "$1" = "jmx" ]; then
    JAVA_JMX_OPTS=" -Dcom.sun.management.jmxremote.port=1099 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false "
fi
JAVA_MEM_OPTS=""
BITS=`java -version 2>&1 | grep -i 64-bit`
if [ -n "$BITS" ]; then
    JAVA_MEM_OPTS=" -server -Xloggc:$GC_LOG_FILE -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=10M -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintTenuringDistribution -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$DUMP_FILE -Xmx200m -Xms200m -Xmn150m -XX:PermSize=64m -Xss256k -XX:+DisableExplicitGC -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSCompactAtFullCollection -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 "
else
    JAVA_MEM_OPTS=" -server -Xloggc:$GC_LOG_FILE -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=10M -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintTenuringDistribution -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$DUMP_FILE -Xms2g -Xmx2g -XX:PermSize=128m -XX:SurvivorRatio=2 -XX:+UseParallelGC "
fi

echo -e "Starting the $SERVER_NAME ...\c"
nohup java ${JAVA_OPTS} ${JAVA_MEM_OPTS} ${JAVA_DEBUG_OPTS} ${JAVA_JMX_OPTS} -jar ${DEPLOY_DIR}/lib/*.jar >/dev/null 2>&1 &

COUNT=0
while [ ${COUNT} -lt 1 ]; do
    echo -e ".\c"
    sleep 1
    COUNT=`ps -f | grep java | grep ${DEPLOY_DIR} | awk '{print $2}' | wc -l`
    if [ ${COUNT} -gt 0 ]; then
        break
    fi
done

echo "OK!"
PIDS=`ps -f | grep java | grep "$DEPLOY_DIR" | awk '{print $2}'`
echo "PID: $PIDS"