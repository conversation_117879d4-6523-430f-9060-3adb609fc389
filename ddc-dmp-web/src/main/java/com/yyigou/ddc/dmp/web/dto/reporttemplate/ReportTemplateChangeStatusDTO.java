package com.yyigou.ddc.dmp.web.dto.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateChangeStatusDTO  implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "报表模板编码")
    @EntityField(name = "报表模板编码")
    private String templateCode;

    /**
     * 启用状态 1-启用，2-禁用
     */
    @Schema(description = "启用状态 1-启用，2-禁用")
    @EntityField(name = "启用状态 1-启用，2-禁用")
    private Integer enableStatus;
}
