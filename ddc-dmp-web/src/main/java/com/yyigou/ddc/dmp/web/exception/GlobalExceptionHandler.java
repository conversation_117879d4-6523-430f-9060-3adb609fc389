package com.yyigou.ddc.dmp.web.exception;

import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.result.RestResult;
import com.yyigou.ddc.dmp.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public RestResult<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        return RestResult.<Void>error(e.getCode(), e.getMessage()).path(request.getRequestURI());
    }

    /**
     * 处理参数校验异常 - @RequestBody
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResult<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数校验失败: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.VALIDATION_ERROR.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理参数校验异常 - @ModelAttribute
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResult<Void> handleBindException(BindException e, HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数绑定失败: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.VALIDATION_ERROR.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理参数校验异常 - @RequestParam
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResult<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String message = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.warn("约束校验失败: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.VALIDATION_ERROR.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResult<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());
        log.warn("缺少请求参数: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.BAD_REQUEST.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResult<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String message = String.format("参数类型不匹配: %s", e.getName());
        log.warn("参数类型不匹配: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.BAD_REQUEST.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理HTTP消息不可读异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResult<Void> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        log.warn("HTTP消息不可读: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        return RestResult.<Void>error(ResultCode.BAD_REQUEST.getCode(), "请求体格式错误").path(request.getRequestURI());
    }

    /**
     * 处理不支持的HTTP方法异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public RestResult<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        String message = String.format("不支持的HTTP方法: %s", e.getMethod());
        log.warn("不支持的HTTP方法: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.METHOD_NOT_ALLOWED.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理不支持的媒体类型异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public RestResult<Void> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        String message = String.format("不支持的媒体类型: %s", e.getContentType());
        log.warn("不支持的媒体类型: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.UNSUPPORTED_MEDIA_TYPE.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理404异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public RestResult<Void> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        String message = String.format("请求的资源不存在: %s %s", e.getHttpMethod(), e.getRequestURL());
        log.warn("404异常: {}, 请求路径: {}", message, request.getRequestURI());
        return RestResult.<Void>error(ResultCode.NOT_FOUND.getCode(), message).path(request.getRequestURI());
    }

    /**
     * 处理数据库异常
     */
    @ExceptionHandler({SQLException.class, DataAccessException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public RestResult<Void> handleDatabaseException(Exception e, HttpServletRequest request) {
        log.error("数据库异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI(), e);
        return RestResult.<Void>error(ResultCode.DATABASE_ERROR).path(request.getRequestURI());
    }

    /**
     * 处理数据完整性约束违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public RestResult<Void> handleDataIntegrityViolationException(DataIntegrityViolationException e, HttpServletRequest request) {
        log.warn("数据完整性约束违反: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        return RestResult.<Void>error(ResultCode.DATA_INTEGRITY_VIOLATION).path(request.getRequestURI());
    }

    /**
     * 处理重复键异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public RestResult<Void> handleDuplicateKeyException(DuplicateKeyException e, HttpServletRequest request) {
        log.warn("数据重复: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        return RestResult.<Void>error(ResultCode.DATA_ALREADY_EXISTS).path(request.getRequestURI());
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public RestResult<Void> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        log.error("空指针异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI(), e);
        return RestResult.<Void>error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "系统内部错误").path(request.getRequestURI());
    }

    /**
     * 处理IllegalArgumentException
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResult<Void> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        log.warn("非法参数异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI());
        return RestResult.<Void>error(ResultCode.BAD_REQUEST.getCode(), e.getMessage()).path(request.getRequestURI());
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public RestResult<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常: {}, 请求路径: {}", e.getMessage(), request.getRequestURI(), e);
        return RestResult.<Void>error(ResultCode.INTERNAL_SERVER_ERROR).path(request.getRequestURI());
    }
}