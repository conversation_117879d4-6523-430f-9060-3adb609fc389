package com.yyigou.ddc.dmp.web.vo.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanPageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案编号
     */
    @EntityField(name = "方案编号")
    private String planNo;

    /**
     * 模型编号
     */
    @EntityField(name = "模型编号")
    private String compareModelNo;

    /**
     * 方案编码
     */
    @EntityField(name = "方案编码")
    private String planCode;

    /**
     * 比价方案名称
     */
    @EntityField(name = "比价方案名称")
    private String planName;

    /**
     * 描述信息
     */
    @EntityField(name = "描述信息")
    private String description;

    /**
     * 启用状态 1-启用，2-禁用
     */
    @EntityField(name = "启用状态 1-启用，2-禁用")
    private Integer enableStatus;


    private String modifyNo;

    /**
     * 操作人名称
     */
    private String modifyName;

    /**
     * 操作时间
     */
    private String modifyTime;
}
