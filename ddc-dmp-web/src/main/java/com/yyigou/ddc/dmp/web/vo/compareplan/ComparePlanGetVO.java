package com.yyigou.ddc.dmp.web.vo.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.dmp.web.vo.comparemodel.CompareModelRefFieldVO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanGetVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 方案编号
     */
    @EntityField(name = "方案编号")
    private String planNo;

    /**
     * 模型编号
     */
    @EntityField(name = "模型编号")
    private String compareModelNo;

    /**
     * 方案编码
     */
    @EntityField(name = "方案编码")
    private String planCode;

    /**
     * 比价方案名称
     */
    @EntityField(name = "比价方案名称")
    private String planName;

    /**
     * 描述信息
     */
    @EntityField(name = "描述信息")
    private String description;

    /**
     * 启用状态 1-启用，2-禁用
     */
    @EntityField(name = "启用状态")
    private Integer enableStatus;

    private String enterpriseNo;

    /**
     * 比价指标
     */
    @EntityField(name = "比价指标")
    private List<ComparePlanRefMetricVO> compareMetricList;

    /**
     * 基准指标
     */
    @EntityField(name = "基准指标")
    private List<ComparePlanRefMetricVO> baselineMetricList;

    /**
     * 计算指标集合
     */
    @EntityField(name = "计算指标集合")
    private List<ComparePlanRefCalculatedMetricVO> calculatedMetricList;

    /**
     * 报表行维度集合
     */
    @EntityField(name = "报表行维度集合")
    private List<ComparePlanRefFieldVO> rowList;

    /**
     * 报表列维度集合
     */
    @EntityField(name = "报表列维度集合")
    private List<ComparePlanRefFieldVO> columnList;

    /**
     * 报表值维度集合
     */
    @EntityField(name = "报表值维度集合")
    private List<ComparePlanRefFieldVO> valueList;


    /**
     * 比对对象字段集合
     */
    @EntityField(name = "比对对象字段集合")
    private List<CompareModelRefFieldVO> compareObjectList;

    /**
     * 比对维度字段集合
     */
    @EntityField(name = "比对维度字段集合")
    private List<CompareModelRefFieldVO> compareDimList;
}
