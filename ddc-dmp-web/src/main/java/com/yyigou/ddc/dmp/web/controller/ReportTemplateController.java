//package com.yyigou.ddc.dmp.web.controller;
//
//import cn.hutool.core.bean.BeanUtil;
//import com.yyigou.ddc.common.page.PageVo;
//import com.yyigou.ddc.dmp.common.result.RestResult;
//import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
//import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
//import com.yyigou.ddc.dmp.common.util.WebSessionUtil;
//import com.yyigou.ddc.dmp.model.bo.reporttemplate.ReportTemplateBizConfigBO;
//import com.yyigou.ddc.dmp.model.req.reporttemplate.*;
//import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateGetRes;
//import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateManagePageRes;
//import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplatePageRes;
//import com.yyigou.ddc.dmp.service.reporttemplate.ReportTemplateService;
//import com.yyigou.ddc.dmp.web.dto.CommonDTO;
//import com.yyigou.ddc.dmp.web.dto.CommonPageDTO;
//import com.yyigou.ddc.dmp.web.dto.PageQueryDTO;
//import com.yyigou.ddc.dmp.web.dto.reporttemplate.*;
//import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
//import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateBizConfigVO;
//import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateGetVO;
//import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateManagePageVO;
//import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplatePageVO;
//import com.yyigou.sourcing.api.registry.annotation.HttpMethod;
//import io.swagger.v3.oas.annotations.Operation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * 报表模板表前端控制器
// */
////@RestController
////@RequestMapping("/reportTemplate")
////@HttpInterface(name = "报表模板接口")
////@Tag(name = "报表模板接口")
//public class ReportTemplateController {
//
//    @Autowired
//    private ReportTemplateService reportTemplateService;
//
//    /**
//     * 获取单个报表模板信息
//     */
//    @PostMapping("/get")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.get",name = "获取单个报表模板信息",processState = 2 ,requestAuthentication = false)
//    public RestResult<ReportTemplateGetVO> get(@RequestBody CommonDTO<ReportTemplateGetDTO> getDTO) {
//        CommonParamsUtil.validateCommonDTO(getDTO);
//        ReportTemplateGetReq getReq = BeanCopyUtil.copyFields(getDTO.getParams(), ReportTemplateGetReq.class);
//        getReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//        ReportTemplateGetRes reportTemplateGetRes = reportTemplateService.get(getReq);
//        ReportTemplateGetVO reportTemplateGetVO = BeanCopyUtil.copyFields(reportTemplateGetRes, ReportTemplateGetVO.class);
//        reportTemplateGetVO.setBizConfigVO(BeanCopyUtil.copyFieldsByJson(reportTemplateGetRes.getBizConfig(), ReportTemplateBizConfigVO.class));
//        return RestResult.success(reportTemplateGetVO);
//    }
//
//    /**
//     * 平台端获取报表模板列表
//     */
//    @PostMapping("/platform/pageQuery")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.platform.pageQuery",name = "平台端获取报表模板列表",processState = 2 ,requestAuthentication = false)
//    public RestResult<PageVo<ReportTemplatePageVO>> pageQuery(@RequestBody CommonPageDTO<ReportTemplatePageQueryDTO, PageQueryDTO> pageQueryDTO) {
//        CommonParamsUtil.validateCommonPageDTO(pageQueryDTO);
//        ReportTemplatePageQueryReq pageReq = BeanCopyUtil.copyFields(pageQueryDTO.getParams(), ReportTemplatePageQueryReq.class);
//        BeanUtil.copyProperties(pageQueryDTO.getPageParams(), pageReq);
//        pageReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//
//        PageVo<ReportTemplatePageRes> pageResPageVo = reportTemplateService.pageQuery(pageReq);
//        PageVo<ReportTemplatePageVO> reportTemplatePageVOPageVo = new PageVo<>(pageResPageVo.getPageIndex(), pageResPageVo.getPageSize(), pageResPageVo.getTotal(), BeanCopyUtil.copyFieldsList(pageResPageVo.getRows(), ReportTemplatePageVO.class));
//        return RestResult.success(reportTemplatePageVOPageVo);
//
//    }
//
//    /**
//     * 新增平台报表模板
//     */
//    @PostMapping("/platform/save")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.platform.save",name = "平台端新增报表模板",processState = 2 ,requestAuthentication = false)
//    @Operation(
//            description = "新增平台报表模板",
//            summary = "ddc.dmp.reportTemplate.platform.save"
//    )
//    public RestResult<ReportTemplateGetVO> platformSave(@RequestBody CommonDTO<ReportTemplateSaveDTO> saveDTO) {
//        CommonParamsUtil.validateCommonDTO(saveDTO);
//        ValidatorUtil.checkEmptyThrowEx(saveDTO.getParams().getBizConfigDTO(), "业务配置不能为空");
//
//        ReportTemplateSaveReq saveReq = BeanCopyUtil.copyFields(saveDTO.getParams(), ReportTemplateSaveReq.class);
//        saveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//        saveReq.setBizConfigBO(BeanCopyUtil.copyFieldsByJson(saveDTO.getParams().getBizConfigDTO(), ReportTemplateBizConfigBO.class));
//        return RestResult.success(BeanCopyUtil.copyFields(reportTemplateService.platformSave(saveReq), ReportTemplateGetVO.class));
//    }
//
//    /**
//     * 更新报表模板
//     */
//    @PostMapping("/update")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.update",name = "修改报表模板",processState = 2 ,requestAuthentication = false)
//    public RestResult<Boolean> update(@RequestBody CommonDTO<ReportTemplateUpdateDTO> updateDTO) {
//        CommonParamsUtil.validateCommonDTO(updateDTO);
//        ReportTemplateUpdateReq updateReq = BeanCopyUtil.copyFields(updateDTO.getParams(), ReportTemplateUpdateReq.class);
//        updateReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//        updateReq.setBizConfigBO(BeanCopyUtil.copyFieldsByJson(updateDTO.getParams().getBizConfigDTO(), ReportTemplateBizConfigBO.class));
//
//        return RestResult.success(reportTemplateService.update(updateReq));
//    }
//
//    /**
//     * 启用/禁用报表模板
//     */
//    @PostMapping("/platform/changeStatus")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.platform.changeStatus",name = "启用/禁用报表模板",processState = 2 ,requestAuthentication = false)
//    public RestResult<Boolean> platformChangeStatus(@RequestBody CommonDTO<ReportTemplateChangeStatusDTO> updateDTO) {
//        CommonParamsUtil.validateCommonDTO(updateDTO);
//        ReportTemplateChangeStatusReq changeStatusReq = BeanCopyUtil.copyFields(updateDTO.getParams(), ReportTemplateChangeStatusReq.class);
//        changeStatusReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//        return RestResult.success(reportTemplateService.platformChangeStatus(changeStatusReq));
//    }
//
//    /**
//     * 删除报表模板
//     */
//    @PostMapping("/delete")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.delete",name = "删除报表模板",processState = 2 ,requestAuthentication = false)
//    public RestResult<Boolean> delete(@RequestBody CommonDTO<ReportTemplateDeleteDTO> deleteDTO) {
//        CommonParamsUtil.validateCommonDTO(deleteDTO);
//        ReportTemplateDeleteReq deleteReq = BeanCopyUtil.copyFields(deleteDTO.getParams(), ReportTemplateDeleteReq.class);
//        deleteReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//
//        return RestResult.success(reportTemplateService.delete(deleteReq));
//    }
//
//
//    /**
//     * 租户端-报表管理分页查询
//     * 数据可能来自两块：1-当前租户自己的报表模板；2-平台共享的模板
//     */
//    @PostMapping("/manage/pageQuery")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.manage.pageQuery",name = " 租户端-报表管理分页查询",processState = 1 ,requestAuthentication = false)
//    public RestResult<PageVo<ReportTemplateManagePageVO>> managePageQuery(@RequestBody CommonPageDTO<ReportTemplatePageQueryDTO, PageQueryDTO> pageQueryDTO) {
//        CommonParamsUtil.validateCommonPageDTO(pageQueryDTO);
//
//        ReportTemplatePageQueryReq pageReq = BeanCopyUtil.copyFields(pageQueryDTO.getParams(), ReportTemplatePageQueryReq.class);
//        BeanUtil.copyProperties(pageQueryDTO.getPageParams(), pageReq);
//        pageReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//
//        PageVo<ReportTemplateManagePageRes> pageResPageVo = reportTemplateService.managePageQuery(pageReq);
//        return RestResult.success(new PageVo<>(pageResPageVo.getPageIndex(), pageResPageVo.getPageSize(), pageResPageVo.getTotal(), BeanCopyUtil.copyFieldsList(pageResPageVo.getRows(), ReportTemplateManagePageVO.class)));
//
//    }
//
//
//    /**
//     * 租户端-报表管理启用/停用报表模板，支持：首次使用平台模板
//     *
//     */
//    @PostMapping("/manage/changeStatus")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.manage.changeStatus",name = "租户端-报表管理启用/停用报表模板",processState = 2 ,requestAuthentication = false)
//    public RestResult<Boolean> manageChangeStatus(@RequestBody CommonDTO<ReportTemplateChangeStatusDTO> changeStatusDTO) {
//        CommonParamsUtil.validateCommonDTO(changeStatusDTO);
//
//        ReportTemplateChangeStatusReq changeStatusReq = BeanCopyUtil.copyFields(changeStatusDTO.getParams(), ReportTemplateChangeStatusReq.class);
//        changeStatusReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//        return RestResult.success(reportTemplateService.manageChangeStatus(changeStatusReq));
//    }
//
//
//    /**
//     * 租户端-报表管理模块授权
//     * @param authDTO
//     * @return
//     */
//    @PostMapping("/manage/auth")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.manage.auth",name = "租户端-报表管理模块授权",processState = 1 ,requestAuthentication = false)
//    public RestResult<Boolean> manageAuth(@RequestBody CommonDTO<ReportTemplateAuthDTO> authDTO) {
//        CommonParamsUtil.validateCommonDTO(authDTO);
//
//        ReportTemplateAuthReq authReq = BeanCopyUtil.copyFields(authDTO.getParams(), ReportTemplateAuthReq.class);
//        authReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//        return RestResult.success(reportTemplateService.manageAuth(authReq));
//    }
//
//    /**
//     * 租户端-查询报表数据
//     *
//     */
//    @PostMapping("/use/queryData")
//    @HttpMethod(aliasName = "ddc.dmp.reportTemplate.use.queryData",name = "租户端-报表管理启用/停用报表模板",processState = 1 ,requestAuthentication = false)
//    public RestResult<List<Map<String, Object>>> useQueryData(@RequestBody CommonDTO<ReportTemplateQueryDataDTO> queryDataDTO) {
//        CommonParamsUtil.validateCommonDTO(queryDataDTO);
//
//        ReportTemplateQueryDataReq queryDataReq = BeanCopyUtil.copyFields(queryDataDTO.getParams(), ReportTemplateQueryDataReq.class);
//        queryDataReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
//        return RestResult.success(reportTemplateService.useQueryData(queryDataReq));
//    }
//
//
//
//    // todo-zyc 报表复制。来源no改为code
//
////    /**
////     * 租户端-报表管理 复制报表模板
////     *
////     */
////    @PostMapping("/manage/copy")
////    public Boolean manageCopy(ReportTemplateCopyDTO copyDTO) {
////        ReportTemplateManageCopyReq copyReq = BeanCopyUtil.copyFields(copyDTO, ReportTemplateChangeStatusReq.class);
////        copyReq.setEnterpriseNo(SessionUtil.getTenantNo());
////        return reportTemplateService.manageCopy(copyReq);
////    }
//
//    // todo-zyc 报表使用端分页、详情、数据获取、授权待定
//}