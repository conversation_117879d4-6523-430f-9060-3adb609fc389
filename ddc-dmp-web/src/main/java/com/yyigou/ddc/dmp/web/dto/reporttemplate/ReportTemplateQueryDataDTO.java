package com.yyigou.ddc.dmp.web.dto.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateQueryDataDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "报表模板编码")
    private String templateCode;

    /**
     * 查询条件
     */
    @EntityField(name = "查询条件变量值")
    private Map<String, Object> variableMap;

}
