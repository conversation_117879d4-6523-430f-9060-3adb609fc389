package com.yyigou.ddc.dmp.web.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * 日志拦截器
 * 用于在请求处理过程中添加MDC上下文信息
 */
@Component
public class LoggingInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(LoggingInterceptor.class);
    
    private static final String TRACE_ID = "traceId";
    private static final String USER_ID = "userId";
    private static final String IP = "ip";
    private static final String URI = "uri";
    private static final String METHOD = "method";
    private static final String USER_AGENT = "userAgent";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 生成追踪ID
        String traceId = generateTraceId();
        MDC.put(TRACE_ID, traceId);
        
        // 获取客户端IP
        String clientIp = getClientIpAddress(request);
        MDC.put(IP, clientIp);
        
        // 请求URI和方法
        MDC.put(URI, request.getRequestURI());
        MDC.put(METHOD, request.getMethod());
        
        // 用户代理
        String userAgent = request.getHeader("User-Agent");
        MDC.put(USER_AGENT, userAgent != null ? userAgent : "unknown");
        
        // 用户ID（从请求头或session中获取，这里先设置为默认值）
        String userId = getUserId(request);
        MDC.put(USER_ID, userId);
        
        // 记录请求开始
        long startTime = System.currentTimeMillis();
        request.setAttribute("startTime", startTime);
        
        logger.info("Request started: {} {} from {}", request.getMethod(), request.getRequestURI(), clientIp);
        
        return true;
    }
    
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 可以在这里添加响应处理逻辑
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // 计算请求处理时间
            Long startTime = (Long) request.getAttribute("startTime");
            if (startTime != null) {
                long duration = System.currentTimeMillis() - startTime;
                
                // 记录请求完成
                if (ex != null) {
                    logger.error("Request completed with error: {} {} - Status: {} - Duration: {}ms - Error: {}", 
                            request.getMethod(), request.getRequestURI(), response.getStatus(), duration, ex.getMessage(), ex);
                } else {
                    logger.info("Request completed: {} {} - Status: {} - Duration: {}ms", 
                            request.getMethod(), request.getRequestURI(), response.getStatus(), duration);
                }
            }
        } finally {
            // 清理MDC上下文
            MDC.clear();
        }
    }
    
    /**
     * 生成追踪ID
     */
    private String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (proxyClientIp != null && !proxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }
        
        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIp != null && !wlProxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 获取用户ID
     * 这里可以根据实际业务需求从请求头、JWT token或session中获取
     */
    private String getUserId(HttpServletRequest request) {
        // 从请求头获取用户ID
        String userId = request.getHeader("X-User-Id");
        if (userId != null && !userId.isEmpty()) {
            return userId;
        }
        
        // 从JWT token中解析用户ID（需要根据实际JWT实现）
        String authorization = request.getHeader("Authorization");
        if (authorization != null && authorization.startsWith("Bearer ")) {
            // TODO: 解析JWT token获取用户ID
            // String token = authorization.substring(7);
            // return parseUserIdFromToken(token);
        }
        
        // 从session中获取用户ID
        Object sessionUserId = request.getSession(false) != null ? 
                request.getSession().getAttribute("userId") : null;
        if (sessionUserId != null) {
            return sessionUserId.toString();
        }
        
        return "anonymous";
    }
}