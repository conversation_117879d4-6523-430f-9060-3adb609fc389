package com.yyigou.ddc.dmp.web.provider.compare;

import cn.hutool.core.bean.BeanUtil;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanChangeStatusReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanGetReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanPageQueryReq;
import com.yyigou.ddc.dmp.model.req.compareplan.ComparePlanSaveReq;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanGetRes;
import com.yyigou.ddc.dmp.model.res.compareplan.ComparePlanPageRes;
import com.yyigou.ddc.dmp.service.compareplan.ComparePlanService;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;
import com.yyigou.ddc.dmp.web.api.compare.ComparePlanAPI;
import com.yyigou.ddc.dmp.web.dto.PageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanChangeStatusDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanGetDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanPageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanSaveDTO;
import com.yyigou.ddc.dmp.web.vo.compareplan.ComparePlanGetVO;
import com.yyigou.ddc.dmp.web.vo.compareplan.ComparePlanPageVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 平台端比对模型
 * <AUTHOR>
 * @date 2025/08/20
 */
@Interface(name = "比价方案")
@Component("comparePlanProvider")
public class ComparePlanProvider extends ServiceBaseAbstract implements ComparePlanAPI {

    @Resource
    private ComparePlanService comparePlanService;

    /**
     * 新增比价方案
     */
    @Method(aliasName = "ddc.dmp.comparePlan.save", name = "新增比价方案", processState = 1, requestAuthentication = false, methodType = MethodType.ADD)
    @Override
    public CallResult<ComparePlanPageVO> save(ComparePlanSaveDTO params) {
        try {
            // todo-zyc 分布式锁，避免并发处理
            ComparePlanSaveReq comparePlanSaveReq = BeanCopyUtil.copyFieldsByJson(params, ComparePlanSaveReq.class);
            comparePlanSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            ComparePlanPageVO comparePlanPageVO = BeanCopyUtil.copyFields(comparePlanService.save(comparePlanSaveReq), ComparePlanPageVO.class);
            return CallResult.success(comparePlanPageVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 修改比价方案
     */
    @Method(aliasName = "ddc.dmp.comparePlan.update", name = "修改比价方案", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> update(ComparePlanSaveDTO params) {
        try {
            // todo-zyc 分布式锁，避免并发处理
            ComparePlanSaveReq comparePlanSaveReq = BeanCopyUtil.copyFieldsByJson(params, ComparePlanSaveReq.class);
            comparePlanSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            return CallResult.success(comparePlanService.update(comparePlanSaveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取单个比价方案信息
     */
    @Method(aliasName = "ddc.dmp.comparePlan.get", name = "查询比价方案详情", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<ComparePlanGetVO> get(ComparePlanGetDTO params) {
        try {
            ComparePlanGetReq getReq = BeanCopyUtil.copyFields(params, ComparePlanGetReq.class);
            getReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            ComparePlanGetRes comparePlanGetRes = comparePlanService.get(getReq);
            ComparePlanGetVO comparePlanGetVO = BeanCopyUtil.copyFields(comparePlanGetRes, ComparePlanGetVO.class);
            return CallResult.success(comparePlanGetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 平台端获取比价方案列表
     */
    @Method(aliasName = "ddc.dmp.comparePlan.pageQuery", name = "分页查询比价方案", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<PageVo<ComparePlanPageVO>> pageQuery(ComparePlanPageQueryDTO params, PageQueryDTO pageParams) {
        try {
            ComparePlanPageQueryReq pageReq = BeanCopyUtil.copyFields(params, ComparePlanPageQueryReq.class);
            BeanUtil.copyProperties(pageParams, pageReq);
            pageReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            PageVo<ComparePlanPageRes> pageResPageVo = comparePlanService.pageQuery(pageReq);
            PageVo<ComparePlanPageVO> comparePlanPageVOPageVo = new PageVo<>(pageResPageVo.getPageIndex(), pageResPageVo.getPageSize(), pageResPageVo.getTotal(), BeanCopyUtil.copyFieldsList(pageResPageVo.getRows(), ComparePlanPageVO.class));
            return CallResult.success(comparePlanPageVOPageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 启用/禁用比价方案
     */
    @Method(aliasName = "ddc.dmp.comparePlan.changeStatus", name = "启用/禁用比价方案", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> changeStatus(ComparePlanChangeStatusDTO params) {
        try {
            ComparePlanChangeStatusReq changeStatusReq = BeanCopyUtil.copyFields(params, ComparePlanChangeStatusReq.class);
            changeStatusReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            return CallResult.success(comparePlanService.changeStatus(changeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 删除比价方案
     */
    @Method(aliasName = "ddc.dmp.comparePlan.delete", name = "删除比价方案", processState = 1, requestAuthentication = false, methodType = MethodType.DELETE)
    @Override
    public CallResult<Boolean> delete(ComparePlanGetDTO params) {
        try {
            ComparePlanGetReq deleteReq = BeanCopyUtil.copyFields(params, ComparePlanGetReq.class);
            deleteReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            return CallResult.success(comparePlanService.delete(deleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
