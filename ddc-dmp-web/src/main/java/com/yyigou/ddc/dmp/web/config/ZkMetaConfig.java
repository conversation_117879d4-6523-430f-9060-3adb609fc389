package com.yyigou.ddc.dmp.web.config;

import com.yyigou.ddc.common.zkconfig.ConfigServerPlaceholderConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Configuration
public class ZkMetaConfig {

    @Bean("configServerPlaceholderConfigurer")
    public static ConfigServerPlaceholderConfigurer properties() {
        final ConfigServerPlaceholderConfigurer ppc = new ConfigServerPlaceholderConfigurer();
        ppc.setIgnoreResourceNotFound(true);
        ppc.setIgnoreUnresolvablePlaceholders(true);
        final List<Resource> resourceList = new ArrayList<>();
        resourceList.add(new ClassPathResource("*.properties"));
        ppc.setLocations(resourceList.toArray(new Resource[]{}));

        ppc.setZkConfigNodePath(System.getProperty("zk.config.node.path"));
        ppc.setZkUrl(System.getProperty("zk.config.url"));
        // spring boot 会从system里加载配置
        ppc.setWritePropsToSystem(true);
        return ppc;
    }

}
