package com.yyigou.ddc.dmp.web.vo.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefMetricVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @EntityField(name = "唯一编号，用于更新")
    private String compareModelMetricNo;



    @EntityField(name = "指标编号")
    private String metricNo;


    @EntityField(name = "指标编号")
    private String metricName;

    // todo-zyc 更多指标信息

}
