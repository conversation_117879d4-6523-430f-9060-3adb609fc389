package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.result.RestResult;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * API接口示例Controller
 * 演示统一响应结果封装
 */
@RestController
@RequestMapping("/api")
public class ApiController {

    /**
     * 返回简单字符串 - 会被自动封装为RestResult
     */
    @GetMapping("/simple")
    public String getSimpleData() {
        return "这是一个简单的字符串数据";
    }

    /**
     * 返回对象数据 - 会被自动封装为RestResult
     */
    @GetMapping("/object")
    public Map<String, Object> getObjectData() {
        Map<String, Object> data = new HashMap<>();
        data.put("id", 1);
        data.put("name", "测试数据");
        data.put("status", "active");
        return data;
    }

    /**
     * 手动返回RestResult - 不会被重复封装
     */
    @GetMapping("/manual")
    public RestResult<String> getManualResult() {
        return RestResult.success("手动封装的数据", "这是手动使用RestResult封装的数据");
    }

    /**
     * POST接口示例 - 会被自动封装为RestResult
     */
    @PostMapping("/create")
    public Map<String, Object> createData(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        response.put("id", System.currentTimeMillis());
        response.put("message", "数据创建成功");
        response.put("input", request);
        return response;
    }

    /**
     * 返回null - 会被自动封装为RestResult
     */
    @GetMapping("/null")
    public Object getNullData() {
        return null;
    }

    /**
     * 返回数组数据 - 会被自动封装为RestResult
     */
    @GetMapping("/array")
    public String[] getArrayData() {
        return new String[]{"item1", "item2", "item3"};
    }
}