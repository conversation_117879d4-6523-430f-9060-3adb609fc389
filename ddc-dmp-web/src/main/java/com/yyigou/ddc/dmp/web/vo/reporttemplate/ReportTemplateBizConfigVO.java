package com.yyigou.ddc.dmp.web.vo.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
public class ReportTemplateBizConfigVO implements Serializable {

    /**
     * 0- 不用rowTree，1- 使用rowTree，2-使用columnTree
     */
    @Schema(description = "0- 不用rowTree，1- 使用rowTree，2-使用columnTree")
    @EntityField(name = "0- 不用rowTree，1- 使用rowTree，2-使用columnTree")
    private Integer treeType;

    /**
     * 行维度
     */
    @Schema(description = "行维度")
    @EntityField(name ="行维度")
    private List<ReportTemplateFieldVO> rowFieldList;

    /**
     * 列维度
     */
    @Schema(description = "列维度")
    @EntityField(name = "列维度")
    private List<ReportTemplateFieldVO> columnFieldList;

    /**
     * 值维度
     */
    @Schema(description = "值维度")
    @EntityField(name = "值维度")
    private List<ReportTemplateFieldVO> valueFieldList;

    /**
     * 指标虚拟维度列展示成列维度，还是行维度。antv配置
     */
    @Schema(description = "指标虚拟维度列展示成列维度，还是行维度。antv配置")
    @EntityField(name = "指标虚拟维度列展示成列维度，还是行维度。antv配置")
    private Boolean valueInCols;

    /**
     * 自定义指标维度在行列头中的层级顺序 （即 values 的 顺序，从 0 开始）
     */
    @Schema(description = "自定义指标维度在行列头中的层级顺序 （即 values 的 顺序，从 0 开始）")
    @EntityField(name = "自定义指标维度在行列头中的层级顺序 （即 values 的 顺序，从 0 开始）")
    private Integer customValueOrder;

    /**
     * 用于行维度固定的场景
     */
    @Schema(description = "用于行维度固定的场景")
    @EntityField(name = "用于行维度固定的场景")
    private List<ReportTemplateCustomTreeVO> rowTree;

    /**
     * 用于列维度固定的场景
     */
    @Schema(description = "用于列维度固定的场景")
    @EntityField(name = "用于列维度固定的场景")
    private List<ReportTemplateCustomTreeVO> columnTree;



}
