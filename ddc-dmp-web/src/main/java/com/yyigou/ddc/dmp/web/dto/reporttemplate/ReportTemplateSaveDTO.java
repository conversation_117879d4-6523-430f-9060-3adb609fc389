package com.yyigou.ddc.dmp.web.dto.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
@Schema(title = "新增报表模板DTO")
public class ReportTemplateSaveDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 报表名称
     */
    @Schema(description = "报表模板名称")
    @EntityField(name = "报表模板名称")
    private String templateName;

    /**
     * 数据集编号
     */
    @Schema(description = "数据集编号")
    @EntityField(name = "数据集编号")
    private String datasetNo;

    /**
     * 比价方案编号
     */
    @Schema(description = "比价方案编号")
    @EntityField(name = "比价方案编号")
    private String planNo;

    @Schema(description = "报表业务配置")
    @EntityField(name = "报表业务配置")
    private ReportTemplateBizConfigDTO bizConfigDTO;

    /**
     * 数据筛选条件
     */
    @Schema(description = "数据筛选条件")
    @EntityField(name = "数据筛选条件")
    private String filterConfig;

    /**
     * antv渲染配置信息
     */
    @Schema(description = "antv渲染配置信息")
    @EntityField(name = "antv渲染配置信息")
    private String configJson;

    /**
     * 描述信息
     */
    @Schema(description = "描述信息")
    @EntityField(name = "描述信息")
    private String description;

    /**
     * 启用状态 1-启用，2-禁用
     */
    @Schema(description = "启用状态 1-启用，2-禁用")
    @EntityField(name = "启用状态 1-启用，2-禁用")
    private Integer enableStatus;


}
