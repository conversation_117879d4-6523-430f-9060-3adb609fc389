package com.yyigou.ddc.dmp.web.vo.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
public class ReportTemplateManagePageVO extends ReportTemplatePageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long refId;

    /**
     * 引用的租户编号
     */
    @EntityField(name = "引用的租户编号")
    private String refEnterpriseNo;


    /**
     * 租户引用状态
     */
    @EntityField(name = "引用模板的启用状态")
    private String refEnableStatus;



}
