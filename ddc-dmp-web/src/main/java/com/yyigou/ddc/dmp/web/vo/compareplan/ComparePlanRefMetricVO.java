package com.yyigou.ddc.dmp.web.vo.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanRefMetricVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    @EntityField(name = "唯一编号,用于更新")
    private String comparePlanMetricNo;

    /**
     * 比对模型的指标编号
     */
    @EntityField(name = "比对模型的指标编号")
    private String compareModelMetricNo;

    /**
     * 是否选中
     */
    @EntityField(name = "是否选中")
    private Boolean selected;


    @EntityField(name = "指标名称")
    private String metricName;

    @EntityField(name = "指标编码")
    private String metricCode;
}
