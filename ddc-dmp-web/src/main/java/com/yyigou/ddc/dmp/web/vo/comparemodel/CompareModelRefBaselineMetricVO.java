package com.yyigou.ddc.dmp.web.vo.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefBaselineMetricVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "唯一编号，用于更新")
    private String compareModelMetricNo;

    @EntityField(name = "基准指标编号")
    private String metricNo;

    @EntityField(name = "基准指标名称")
    private String metricName;

    @EntityField(name = "基准指标所属数据集编号")
    private String datasetNo;

    @EntityField(name = "基准指标所属数据集名称")
    private String datasetName;

    @EntityField(name = "基准指标所属数据集查询条件集合")
    private List<CompareModelRefQueryConditionVO> datasetQueryConditionList;
}
