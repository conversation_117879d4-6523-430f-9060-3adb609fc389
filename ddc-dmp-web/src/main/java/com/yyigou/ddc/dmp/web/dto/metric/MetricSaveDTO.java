package com.yyigou.ddc.dmp.web.dto.metric;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MetricSaveDTO implements Serializable {
    private String metricNo;
    private String metricCode;
    private String metricName;
    private String unit;
    private String numPrecision;
    private String displayFormat;
    private String description;
    private String datasetNo;
    private String datasetFieldNo;
    private String aggType;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;
}
