package com.yyigou.ddc.dmp.web.interceptor;


import cn.hutool.json.JSONUtil;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.session.SessionUser;
import com.yyigou.ddc.dmp.common.util.WebSessionUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
@Component
public class WebSessionInterceptor implements HandlerInterceptor {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String attachedSession = request.getHeader("attachedSession");
        if (attachedSession != null){
            attachedSession = new String(Base64.getDecoder().decode(attachedSession), StandardCharsets.UTF_8);
            SessionUser currentUser = JSONUtil.toBean(attachedSession, SessionUser.class); // 得到会话对象: 其中包含用户, 租户, 组织信息

            if (currentUser==null){
                throw new BusinessException("用户信息异常");
            }
            if (currentUser.getEnterpriseNo()==null){
                throw new BusinessException("enterpriseNo为空");
            }
            if (currentUser.getEmployerNo()==null){
                throw new BusinessException("employerNo为空");
            }
            WebSessionUtil.setSessionUser(currentUser);
        } else {
            WebSessionUtil.clearSessionUser();
        }

        String funcNo = request.getHeader("funcNo");
        WebSessionUtil.setFuncNo(funcNo);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        WebSessionUtil.clearSessionUser();
        WebSessionUtil.clearFuncNo();

    }
}
