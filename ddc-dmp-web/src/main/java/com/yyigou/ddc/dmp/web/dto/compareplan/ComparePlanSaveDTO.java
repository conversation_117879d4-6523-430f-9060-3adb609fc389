package com.yyigou.ddc.dmp.web.dto.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanSaveDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 方案编号
     */
    @EntityField(name = "方案编号。编辑场景使用")
    private String planNo;


    /**
     * 模型编号
     */
    @EntityField(name = "模型编号")
    private String compareModelNo;

    /**
     * 方案编码
     */
    @EntityField(name = "方案编码")
    private String planCode;

    /**
     * 比价方案名称
     */
    @EntityField(name = "比价方案名称")
    private String planName;

    /**
     * 描述信息
     */
    @EntityField(name = "描述信息")
    private String description;

    /**
     * 启用状态 1-启用，2-禁用
     */
    @EntityField(name = "启用状态")
    private Integer enableStatus;


    // todo-zyc 比价模型查询条件，基准指标公共查询条件，单个基准指标的查询条件

    @EntityField(name = "比价指标")
    private List<ComparePlanRefMetricDTO> compareMetricList;

    @EntityField(name = "基准指标")
    private List<ComparePlanRefMetricDTO> baselineMetricList;

    @EntityField(name = "计算指标集合")
    private List<ComparePlanRefCalculatedMetricDTO> calculatedMetricList;


    @EntityField(name = "报表行维度集合")
    private List<ComparePlanRefFieldDTO> rowList;

    @EntityField(name = "报表列维度集合")
    private List<ComparePlanRefFieldDTO> columnList;


    @EntityField(name = "报表值维度集合")
    private List<ComparePlanRefFieldDTO> valueList;



}
