package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.model.req.metric.MetricGetReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricQueryReq;
import com.yyigou.ddc.dmp.model.req.metric.MetricSaveReq;
import com.yyigou.ddc.dmp.model.res.metric.MetricQueryRes;
import com.yyigou.ddc.dmp.service.metric.MetricService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricGetDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricQueryDTO;
import com.yyigou.ddc.dmp.web.dto.metric.MetricSaveDTO;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.metric.MetricVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 指标前端控制器
 */
@RestController
@RequestMapping("/metric")
public class MetricController {
    @Resource
    private MetricService metricService;

    /**
     * 保存指标
     */
    @PostMapping("/save")
    public MetricVO save(@RequestBody CommonDTO<MetricSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        MetricSaveReq metricSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), MetricSaveReq.class);
//        metricSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        metricSaveReq.setEnterpriseNo("2000002");
        String metricNo = metricService.saveMetric(metricSaveReq);

        MetricVO metricVO = new MetricVO();
        metricVO.setMetricNo(metricNo);
        return metricVO;
    }

    /**
     * 查询指标
     */
    @GetMapping("/get")
    public MetricVO get(@RequestBody CommonDTO<MetricGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);

        MetricGetReq metricGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), MetricGetReq.class);
//        metricGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        metricGetReq.setEnterpriseNo("2000002");
        MetricQueryRes metricQueryRes = metricService.getMetric(metricGetReq);

        MetricVO metricVO = BeanCopyUtil.copyFields(metricQueryRes, MetricVO.class);
        return metricVO;
    }

    /**
     * 查询指标
     */
    @GetMapping("/query")
    public List<MetricVO> query(@RequestBody CommonDTO<MetricQueryDTO> queryDto) {
        CommonParamsUtil.validateCommonDTO(queryDto);

        MetricQueryReq metricQueryReq = BeanCopyUtil.copyFieldsByJson(queryDto.getParams(), MetricQueryReq.class);
//        metricQueryReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        metricQueryReq.setEnterpriseNo("2000002");
        List<MetricQueryRes> metricQueryResList = metricService.queryMetric(metricQueryReq);

        List<MetricVO> metricVOList = BeanCopyUtil.copyFieldsList(metricQueryResList, MetricVO.class);

        return metricVOList;
    }
}