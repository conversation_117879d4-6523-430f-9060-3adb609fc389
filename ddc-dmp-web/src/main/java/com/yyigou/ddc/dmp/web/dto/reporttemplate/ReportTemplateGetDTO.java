package com.yyigou.ddc.dmp.web.dto.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateGetDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 报表模板编码
     */
    @EntityField(name = "报表模板编码")
    private String templateCode;


}
