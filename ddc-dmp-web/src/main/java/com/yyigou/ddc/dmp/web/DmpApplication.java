package com.yyigou.ddc.dmp.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

@SpringBootApplication
@ImportResource(value = {"classpath:applicationContext-dmp-web.xml"})
@ComponentScan(basePackages = {"com.yyigou.ddc.dmp"})
@MapperScan("com.yyigou.ddc.dmp.dao.**.mapper")
//@Import(value = {ZkApiRegistryProcessor.class, HttpRegistryZkUtil.class})
public class DmpApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(DmpApplication.class, args);
    }
}