package com.yyigou.ddc.dmp.web.vo.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefFieldVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    @EntityField(name = "唯一编号，用于更新")
    private String compareModelDimNo;


    @EntityField(name = "字段编码")
    private String fieldCode;

    @EntityField(name = "字段名称")
    private String fieldName;

}
