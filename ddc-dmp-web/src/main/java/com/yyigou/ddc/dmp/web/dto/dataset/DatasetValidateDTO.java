package com.yyigou.ddc.dmp.web.dto.dataset;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class DatasetValidateDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据集唯一标识
     */
    private String datasetNo;

    /**
     * 驱动表schema名称
     */
    private String drivingSchemaName;

    /**
     * 驱动表名称
     */
    private String drivingTableName;

    /**
     * 关联表配置列表
     */
    private List<JoinTableConfig> joinTables;

    @Data
    public static class JoinTableConfig implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 关联表schema名称
         */
        private String schemaName;

        /**
         * 关联表名称
         */
        private String tableName;

        /**
         * 关联类型：innerJoin, leftJoin, rightJoin
         */
        private String joinType;

        /**
         * 关联条件列表
         */
        private List<JoinCondition> joinConditions;
    }

    @Data
    public static class JoinCondition implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 左表字段
         */
        private String leftField;

        /**
         * 右表字段
         */
        private String rightField;

        /**
         * 操作符：=, !=, >, >=, <, <=
         */
        private String operator;
    }
}
