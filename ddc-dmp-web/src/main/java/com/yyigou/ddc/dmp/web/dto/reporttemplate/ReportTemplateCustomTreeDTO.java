package com.yyigou.ddc.dmp.web.dto.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
@Schema(description = "这样定义行列头分组结构")
public class ReportTemplateCustomTreeDTO extends ReportTemplateFieldDTO {

    /**
     * 子节点
     */
    @Schema(description = "子节点",implementation = ReportTemplateCustomTreeDTO.class)
    @EntityField(name = "子节点")
    private List<ReportTemplateCustomTreeDTO> children;


}
