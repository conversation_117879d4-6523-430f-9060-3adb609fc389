package com.yyigou.ddc.dmp.web.dto.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanGetDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 方案编号
     */
    @EntityField(name = "方案编号。编辑场景使用")
    private String planNo;




}
