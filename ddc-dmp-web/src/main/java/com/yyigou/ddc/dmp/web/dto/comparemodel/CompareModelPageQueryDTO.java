package com.yyigou.ddc.dmp.web.dto.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import com.yyigou.ddc.dmp.model.req.PageQueryReq;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelPageQueryDTO  implements Serializable {

    /**
     * 模型编码
     */
    @EntityField(name = "模型编码")
    private String modelCode;

    /**
     * 模型名称
     */
    @EntityField(name = "模型名称")
    private String modelName;

    /**
     * 启用状态
     */
    @EntityField(name = "启用状态。1-启用，2-禁用")
    private Integer enableStatus;

}
