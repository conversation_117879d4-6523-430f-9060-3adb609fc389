package com.yyigou.ddc.dmp.web.dto.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefDatasetDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @EntityField(name = "id，更新用到该字段")
    private Long id;


    @EntityField(name = "数据集编号")
    private String datasetNo;



    @EntityField(name = "数据集名称")
    private String datasetName;


    /**
     * 关联类型
     * 1-INNER JOIN
     * 2-LEFT JOIN
     * 3-RIGHT JOIN
     */
    @EntityField(name = "关联类型。1-INNER JOIN，2-LEFT JOIN， 3-RIGHT JOIN")
    private Integer joinType;


}
