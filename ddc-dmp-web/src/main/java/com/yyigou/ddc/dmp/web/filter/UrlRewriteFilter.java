package com.yyigou.ddc.dmp.web.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;

import java.io.IOException;

@Slf4j
public class UrlRewriteFilter implements Filter, Ordered {


    @Value("#{ (@environment['server.servlet.context-path'] ?: '') + '/call' }")
    private String callPath;

    private String apiCodePrefix = "ddc.dmp.";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
//        log.info("UrlRewriteFilter log:{}", request.getRequest);
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String requestURI = httpRequest.getRequestURI();
        if (requestURI.startsWith(callPath)) {
            String path = httpRequest.getHeader("path");
            //解决查询方案
            if (path == null){
                path = httpRequest.getParameter("apiCode");
                if (path.startsWith(apiCodePrefix)) {
                    path = path.substring(apiCodePrefix.length() - 1);
                    path = path.replace(".", "/");
                    wrapRequest(path,httpRequest, response,chain);
                }
            }else {
                wrapRequest(path,httpRequest, response,chain);
            }

        } else {
            chain.doFilter(request, response);
        }
    }
    private void wrapRequest(String path,HttpServletRequest httpRequest, ServletResponse response,FilterChain chain) throws ServletException, IOException {
        // 使用自定义的 request wrapper 修改 path
        String finalPath = path;
        HttpServletRequest wrappedRequest = new HttpServletRequestWrapper(httpRequest) {
            @Override
            public String getRequestURI() {
                return httpRequest.getContextPath() + finalPath;
            }

            @Override
            public StringBuffer getRequestURL() {
                return new StringBuffer(super.getRequestURL().toString().replace("/call", finalPath));
            }
        };
        chain.doFilter(wrappedRequest, response);
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
