package com.yyigou.ddc.dmp.web.api.compare;

import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.PageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelChangeStatusDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelGetDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelPageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelSaveDTO;
import com.yyigou.ddc.dmp.web.vo.comparemodel.CompareModelGetVO;
import com.yyigou.ddc.dmp.web.vo.comparemodel.CompareModelPageVO;

/**
 * 平台端比对模型
 * <AUTHOR>
 * @date 2025/08/20
 */
public interface CompareModelAPI extends ServiceBase {
    CallResult<CompareModelPageVO> save(CompareModelSaveDTO params);

    CallResult<Boolean> update(CompareModelSaveDTO params);

    CallResult<CompareModelGetVO> get(CompareModelGetDTO params);

    CallResult<PageVo<CompareModelPageVO>> pageQuery(CompareModelPageQueryDTO params, PageQueryDTO pageParams);

    CallResult<Boolean> changeStatus(CompareModelChangeStatusDTO params);

    CallResult<Boolean> delete(CompareModelGetDTO params);
}
