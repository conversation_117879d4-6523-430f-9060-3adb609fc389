package com.yyigou.ddc.dmp.web.vo.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanRefFieldVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字段来源类型。1-比对对象，2-比对维度，3-比对指标，4-基准指标，5-计算指标
     */
    @EntityField(name = "字段来源类型。1-比对对象，2-比对维度，3-比对指标，4-基准指标，5-计算指标")
    private Integer sourceType;

    /**
     * 比价方案指标唯一标识
     */
    @EntityField(name = "比价方案指标唯一标识")
    private String comparePlanMetricNo;

    /**
     * 比价方案计算指标唯一标识
     */
    @EntityField(name = "比价方案计算指标唯一标识")
    private String comparePlanCalMetricNo;

    /**
     * 模型字段唯一标识
     */
    @EntityField(name = "模型字段唯一标识")
    private String compareModelDimNo;
}
