package com.yyigou.ddc.dmp.web.vo.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelPageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "模型名称")
    private String modelName;

    @EntityField(name = "模型编码")
    private String modelCode;

    @EntityField(name = "描述信息")
    private String description;

    @EntityField(name = "启用状态")
    private Integer enableStatus;

    @EntityField(name = "比对数据集编号")
    private String compareDatasetNo;

    @EntityField(name = "模型编号")
    private String modelNo;


}
