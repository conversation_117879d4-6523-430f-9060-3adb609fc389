package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.result.RestResult;
import com.yyigou.ddc.dmp.common.result.ResultCode;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 首页控制器
 */
@RestController
public class IndexController {

    /**
     * 首页
     */
    @GetMapping("/")
    public String index() {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <title>DDC-DMP 数据管理平台</title>\n" +
                "    <meta charset='UTF-8'>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }\n" +
                "        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n" +
                "        h1 { color: #333; text-align: center; margin-bottom: 30px; }\n" +
                "        .api-list { list-style: none; padding: 0; }\n" +
                "        .api-item { margin: 10px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 4px; }\n" +
                "        .api-item a { text-decoration: none; color: #007bff; font-weight: bold; }\n" +
                "        .api-item a:hover { color: #0056b3; }\n" +
                "        .description { color: #666; margin-top: 5px; font-size: 14px; }\n" +
                "        .status { color: #28a745; font-weight: bold; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class='container'>\n" +
                "        <h1>🚀 DDC-DMP 数据管理平台</h1>\n" +
                "        <p class='status'>✅ 系统运行正常</p>\n" +
                "        <h3>📋 可用接口列表：</h3>\n" +
                "        <ul class='api-list'>\n" +
                "            <li class='api-item'>\n" +
                "                <a href='/ddc-dmp/health'>健康检查</a>\n" +
                "                <div class='description'>检查系统运行状态</div>\n" +
                "            </li>\n" +
                "            <li class='api-item'>\n" +
                "                <a href='/ddc-dmp/druid/'>Druid监控</a>\n" +
                "                <div class='description'>数据库连接池监控 (用户名: admin, 密码: 123456)</div>\n" +
                "            </li>\n" +
                "            <li class='api-item'>\n" +
                "                <a href='/ddc-dmp/test-exception?type=business'>测试异常处理</a>\n" +
                "                <div class='description'>测试全局异常处理机制 (支持参数: business, validation, system)</div>\n" +
                "            </li>\n" +
                "            <li class='api-item'>\n" +
                "                <a href='/ddc-dmp/api/analysis-subject/page'>分析主题分页</a>\n" +
                "                <div class='description'>GET - 获取分析主题分页数据</div>\n" +
                "            </li>\n" +
                "            <li class='api-item'>\n" +
                "                <a href='/ddc-dmp/api/data-model/page'>数据模型分页</a>\n" +
                "                <div class='description'>GET - 获取数据模型分页数据</div>\n" +
                "            </li>\n" +
                "            <li class='api-item'>\n" +
                "                <a href='/ddc-dmp/api/dataset/page'>数据集分页</a>\n" +
                "                <div class='description'>GET - 获取数据集分页数据</div>\n" +
                "            </li>\n" +
                "            <li class='api-item'>\n" +
                "                <a href='/ddc-dmp/api/report-template/page'>报表模板分页</a>\n" +
                "                <div class='description'>GET - 获取报表模板分页数据</div>\n" +
                "            </li>\n" +
                "        </ul>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public RestResult<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", System.currentTimeMillis());
        healthInfo.put("application", "ddc-dmp");
        healthInfo.put("version", "1.0.0-SNAPSHOT");
        return RestResult.success("系统运行正常", healthInfo);
    }

    /**
     * 测试异常处理
     */
    @GetMapping("/test-exception")
    public RestResult<String> testException(@RequestParam(defaultValue = "business") String type) {
        switch (type.toLowerCase()) {
            case "business":
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "这是一个测试业务异常");
            case "validation":
                throw new BusinessException(ResultCode.VALIDATION_ERROR, "这是一个测试参数校验异常");
            case "system":
                throw new RuntimeException("这是一个测试系统异常");
            case "null":
                String nullStr = null;
                return RestResult.success(nullStr.toString()); // 故意触发空指针异常
            default:
                return RestResult.success("异常测试完成，类型: " + type);
        }
    }
}