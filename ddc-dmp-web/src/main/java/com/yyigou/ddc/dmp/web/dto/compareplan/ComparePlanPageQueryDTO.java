package com.yyigou.ddc.dmp.web.dto.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class ComparePlanPageQueryDTO implements Serializable {

    /**
     * 模型编码
     */
    @EntityField(name = "方案编码")
    private String planCode;

    /**
     * 模型名称
     */
    @EntityField(name = "方案名称")
    private String planName;

    /**
     * 启用状态
     */
    @EntityField(name = "启用状态。1-启用，2-禁用")
    private Integer enableStatus;

}
