package com.yyigou.ddc.dmp.web.dto.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class ComparePlanRefCalculatedMetricDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "比价方案计算指标编号。用于编辑场景")
    private String comparePlanCalMetricNo;

    @EntityField(name = "比对模型计算指标编号")
    private String compareModelCalMetricNo;

    @EntityField(name = "基准指标编码")
    private String baselineMetricNo;

    @EntityField(name = "比对指标编码")
    private String compareMetricNo;

    @EntityField(name = "是否选中")
    private Boolean selected;

}
