package com.yyigou.ddc.dmp.web.config;

import com.yyigou.ddc.dmp.common.result.RestResult;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局响应结果统一封装
 * 自动将Controller返回的数据封装为RestResult格式
 */
@RestControllerAdvice(basePackages = "com.yyigou.ddc.dmp.web.controller")
public class GlobalResponseAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 如果返回类型已经是RestResult，则不需要再次封装
        if (RestResult.class.isAssignableFrom(returnType.getParameterType())) {
            return false;
        }
        
        // 获取方法名和类名
        String methodName = returnType.getMethod() != null ? returnType.getMethod().getName() : "";
        String className = returnType.getContainingClass().getSimpleName();
        
        // 排除首页HTML内容
        if ("index".equals(methodName) && "IndexController".equals(className)) {
            return false;
        }
        
        // 对所有Controller进行封装（除了特殊情况）
        return className.endsWith("Controller");
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        
        // 如果body已经是RestResult类型，直接返回
        if (body instanceof RestResult) {
            return body;
        }
        
        // 将返回结果封装为RestResult
        return RestResult.success(body);
    }
}