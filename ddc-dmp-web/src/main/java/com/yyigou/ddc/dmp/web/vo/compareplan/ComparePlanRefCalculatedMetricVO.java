package com.yyigou.ddc.dmp.web.vo.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/22
 */
@Data
public class ComparePlanRefCalculatedMetricVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一编号，用于更新
     */
    @EntityField(name = "唯一编号,用于更新")
    private String comparePlanCalMetricNo;

    /**
     * 计算指标编号
     */
    @EntityField(name = "计算指标编号")
    private String compareModelCalMetricNo;

    /**
     * 是否选中
     */
    @EntityField(name = "是否选中")
    private Boolean selected;

    /**
     * 计算指标编码
     */
    @EntityField(name = "计算指标编码")
    private String calMetricCode;

    /**
     * 计算指标名称
     */
    @EntityField(name = "计算指标名称")
    private String calMetricName;

    /**
     * 描述信息
     */
    @EntityField(name = "描述信息")
    private String description;

    /**
     * 基准指标编号
     */
    @EntityField(name = "基准指标编号")
    private String baselineMetricNo;


    /**
     * 比对指标编号
     */
    @EntityField(name = "比对指标编号")
    private String compareMetricNo;

    /**
     * 表达式
     */
    @EntityField(name = "表达式")
    private String expression;

    /**
     * 单位编码。NONE-无格式，YUAN-元
     */
    @EntityField(name = "单位编码。NONE-无格式，YUAN-元")
    private String unitCode;
}
