package com.yyigou.ddc.dmp.web.provider;

import cn.hutool.core.bean.BeanUtil;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.bo.reporttemplate.ReportTemplateBizConfigBO;
import com.yyigou.ddc.dmp.model.req.reporttemplate.*;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateGetRes;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplateManagePageRes;
import com.yyigou.ddc.dmp.model.res.reporttemplate.ReportTemplatePageRes;
import com.yyigou.ddc.dmp.service.reporttemplate.ReportTemplateService;
import com.yyigou.ddc.dmp.web.api.ReportTemplateAPI;
import com.yyigou.ddc.dmp.web.dto.PageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.reporttemplate.*;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;
import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateBizConfigVO;
import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateGetVO;
import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateManagePageVO;
import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplatePageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Interface(name = "产品档案")
@Component("reportTemplateProvider")
public class ReportTemplateProvider extends ServiceBaseAbstract implements ReportTemplateAPI {

    @Autowired
    private ReportTemplateService reportTemplateService;



    /**
     * 获取单个报表模板信息
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.get", name = "获取单个报表模板信息", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<ReportTemplateGetVO> get(ReportTemplateGetDTO params) {
        try {
            ReportTemplateGetReq getReq = BeanCopyUtil.copyFields(params, ReportTemplateGetReq.class);
            getReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            ReportTemplateGetRes reportTemplateGetRes = reportTemplateService.get(getReq);
            ReportTemplateGetVO reportTemplateGetVO = BeanCopyUtil.copyFields(reportTemplateGetRes, ReportTemplateGetVO.class);
            reportTemplateGetVO.setBizConfigVO(BeanCopyUtil.copyFieldsByJson(reportTemplateGetRes.getBizConfig(), ReportTemplateBizConfigVO.class));
            return CallResult.success(reportTemplateGetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 平台端获取报表模板列表
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.platform.pageQuery", name = "平台端获取报表模板列表", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<PageVo<ReportTemplatePageVO>> pageQuery(ReportTemplatePageQueryDTO params, PageQueryDTO pageParams) {
        try {
            ReportTemplatePageQueryReq pageReq = BeanCopyUtil.copyFields(params, ReportTemplatePageQueryReq.class);
            BeanUtil.copyProperties(pageParams, pageReq);
            pageReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            PageVo<ReportTemplatePageRes> pageResPageVo = reportTemplateService.pageQuery(pageReq);
            PageVo<ReportTemplatePageVO> reportTemplatePageVOPageVo = new PageVo<>(pageResPageVo.getPageIndex(), pageResPageVo.getPageSize(), pageResPageVo.getTotal(), BeanCopyUtil.copyFieldsList(pageResPageVo.getRows(), ReportTemplatePageVO.class));
            return CallResult.success(reportTemplatePageVOPageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }

    }

    /**
     * 新增平台报表模板
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.platform.save", name = "平台端新增报表模板", processState = 1, requestAuthentication = false, methodType = MethodType.ADD)
    @Override
    public CallResult<ReportTemplateGetVO> platformSave(ReportTemplateSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params.getBizConfigDTO(), "业务配置不能为空");

            ReportTemplateSaveReq saveReq = BeanCopyUtil.copyFields(params, ReportTemplateSaveReq.class);
            saveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            saveReq.setBizConfigBO(BeanCopyUtil.copyFieldsByJson(params.getBizConfigDTO(), ReportTemplateBizConfigBO.class));
            return CallResult.success(BeanCopyUtil.copyFields(reportTemplateService.platformSave(saveReq), ReportTemplateGetVO.class));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 更新报表模板
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.update", name = "修改报表模板", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> update(ReportTemplateUpdateDTO params) {
        try {
            ReportTemplateUpdateReq updateReq = BeanCopyUtil.copyFields(params, ReportTemplateUpdateReq.class);
            updateReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            updateReq.setBizConfigBO(BeanCopyUtil.copyFieldsByJson(params.getBizConfigDTO(), ReportTemplateBizConfigBO.class));

            return CallResult.success(reportTemplateService.update(updateReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 启用/禁用报表模板
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.platform.changeStatus", name = "启用/禁用报表模板", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> platformChangeStatus(ReportTemplateChangeStatusDTO params) {
        try {
            ReportTemplateChangeStatusReq changeStatusReq = BeanCopyUtil.copyFields(params, ReportTemplateChangeStatusReq.class);
            changeStatusReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            return CallResult.success(reportTemplateService.platformChangeStatus(changeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 删除报表模板
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.delete", name = "删除报表模板", processState = 1, requestAuthentication = false, methodType = MethodType.DELETE)
    @Override
    public CallResult<Boolean> delete(ReportTemplateDeleteDTO params) {
        try {
            ReportTemplateDeleteReq deleteReq = BeanCopyUtil.copyFields(params, ReportTemplateDeleteReq.class);
            deleteReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            return CallResult.success(reportTemplateService.delete(deleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    /**
     * 租户端-报表管理分页查询
     * 数据可能来自两块：1-当前租户自己的报表模板；2-平台共享的模板
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.manage.pageQuery", name = " 租户端-报表管理分页查询", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<PageVo<ReportTemplateManagePageVO>> managePageQuery(ReportTemplatePageQueryDTO params, PageQueryDTO pageParams) {

        try {
            ReportTemplatePageQueryReq pageReq = BeanCopyUtil.copyFields(params, ReportTemplatePageQueryReq.class);
            BeanUtil.copyProperties(pageParams, pageReq);
            pageReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            PageVo<ReportTemplateManagePageRes> pageResPageVo = reportTemplateService.managePageQuery(pageReq);
            return CallResult.success(new PageVo<>(pageResPageVo.getPageIndex(), pageResPageVo.getPageSize(), pageResPageVo.getTotal(), BeanCopyUtil.copyFieldsList(pageResPageVo.getRows(), ReportTemplateManagePageVO.class)));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }

    }


    /**
     * 租户端-报表管理启用/停用报表模板，支持：首次使用平台模板
     *
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.manage.changeStatus", name = "租户端-报表管理启用/停用报表模板", processState = 2, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> manageChangeStatus(ReportTemplateChangeStatusDTO params) {

        try {
            ReportTemplateChangeStatusReq changeStatusReq = BeanCopyUtil.copyFields(params, ReportTemplateChangeStatusReq.class);
            changeStatusReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            return CallResult.success(reportTemplateService.manageChangeStatus(changeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    /**
     * 租户端-报表管理模块授权
     * @param params
     * @return
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.manage.auth", name = "租户端-报表管理模块授权", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> manageAuth(ReportTemplateAuthDTO params) {
        try {
            ReportTemplateAuthReq authReq = BeanCopyUtil.copyFields(params, ReportTemplateAuthReq.class);
            authReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            return CallResult.success(reportTemplateService.manageAuth(authReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 租户端-查询报表数据
     *
     */
    @Method(aliasName = "ddc.dmp.reportTemplate.use.queryData", name = "租户端-查询报表数据", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<List<Map<String, Object>>> useQueryData(ReportTemplateQueryDataDTO params) {

        try {
            ReportTemplateQueryDataReq queryDataReq = BeanCopyUtil.copyFields(params, ReportTemplateQueryDataReq.class);
            queryDataReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            return CallResult.success(reportTemplateService.useQueryData(queryDataReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }



    // todo-zyc 报表复制。来源no改为code

//    /**
//     * 租户端-报表管理 复制报表模板
//     *
//     */
//    @PostMapping("/manage/copy")
//    public Boolean manageCopy(ReportTemplateCopyDTO copyDTO) {
//        ReportTemplateManageCopyReq copyReq = BeanCopyUtil.copyFields(copyDTO, ReportTemplateChangeStatusReq.class);
//        copyReq.setEnterpriseNo(SessionUtil.getTenantNo());
//        return reportTemplateService.manageCopy(copyReq);
//    }

    // todo-zyc 报表使用端分页、详情、数据获取、授权待定

}
