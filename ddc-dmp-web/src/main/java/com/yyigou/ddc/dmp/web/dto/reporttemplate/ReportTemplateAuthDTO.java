package com.yyigou.ddc.dmp.web.dto.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class ReportTemplateAuthDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "模板编码")
    private String templateCode;


    /**
     * 被授权人的用户编号
     */
    @EntityField(name = "被授权人的用户编号")
    private String authToUserNo;

    @EntityField(name = "被授权人的用户名称")
    private String authToUserName;

    /**
     * 被授权的组织编号集合
     */
    @EntityField(name = "被授权的组织编号集合")
    private List<String> authOrgNoList;
}
