package com.yyigou.ddc.dmp.web.config;

import com.yyigou.ddc.dmp.web.filter.UrlRewriteFilter;
import com.yyigou.ddc.dmp.web.interceptor.LoggingInterceptor;
import com.yyigou.ddc.dmp.web.interceptor.WebSessionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 配置拦截器、静态资源等
 */
@Configuration
@Slf4j
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private LoggingInterceptor loggingInterceptor;

    @Autowired
    private WebSessionInterceptor webSessionInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册日志拦截器
        registry.addInterceptor(loggingInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                        "/static/**",     // 排除静态资源
                        "/css/**",       // 排除CSS文件
                        "/js/**",        // 排除JS文件
                        "/images/**",    // 排除图片文件
                        "/favicon.ico",  // 排除网站图标
                        "/actuator/**"   // 排除监控端点
                );

        registry.addInterceptor(webSessionInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                        "/static/**",     // 排除静态资源
                        "/css/**",       // 排除CSS文件
                        "/js/**",        // 排除JS文件
                        "/images/**",    // 排除图片文件
                        "/favicon.ico",  // 排除网站图标
                        "/actuator/**"   // 排除监控端点
                );
    }

    @Bean
    public UrlRewriteFilter urlRewriteFilter() {
        log.info("urlRewriteFilter init");
        return new UrlRewriteFilter();
    }
}