package com.yyigou.ddc.dmp.web.api;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectDatasetRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectSaveDTO;
import com.yyigou.ddc.dmp.web.vo.subject.AnalysisSubjectVO;
import com.yyigou.ddc.dmp.web.vo.subject.SubjectDatasetRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.subject.SubjectVO;

public interface AnalysisSubjectAPI extends ServiceBase {
    /**
     * 保存分析主题
     *
     * @param params
     * @return
     */
    CallResult<AnalysisSubjectVO> save(SubjectSaveDTO params);

    /**
     * 获取分析主题
     *
     * @param params
     * @return
     */
    CallResult<SubjectVO> get(SubjectGetDTO params);

    /**
     * 保存分析主题和数据集关系
     *
     * @param params
     * @return
     */
    CallResult<SubjectDatasetRefSaveVO> saveSubjectDatasetRef(SubjectDatasetRefSaveDTO params);
}
