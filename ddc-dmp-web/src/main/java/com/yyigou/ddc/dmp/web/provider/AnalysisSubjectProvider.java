package com.yyigou.ddc.dmp.web.provider;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectDatasetRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.SubjectRes;
import com.yyigou.ddc.dmp.service.subject.AnalysisSubjectService;
import com.yyigou.ddc.dmp.web.api.AnalysisSubjectAPI;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectDatasetRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectSaveDTO;
import com.yyigou.ddc.dmp.web.vo.subject.AnalysisSubjectVO;
import com.yyigou.ddc.dmp.web.vo.subject.SubjectDatasetRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.subject.SubjectVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Interface(name = "分析主题")
@Component("analysisSubjectProvider")
public class AnalysisSubjectProvider extends ServiceBaseAbstract implements AnalysisSubjectAPI {
    @Resource
    private AnalysisSubjectService analysisSubjectService;

    /**
     * 保存分析主题
     */
    @Override
    @Method(aliasName = "ddc.dmp.subject.save", name = "保存分析主题", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<AnalysisSubjectVO> save(SubjectSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            SubjectSaveReq subjectSaveReq = BeanCopyUtil.copyFieldsByJson(params, SubjectSaveReq.class);
//        subjectSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            subjectSaveReq.setEnterpriseNo("2000002");
            String subjectNo = analysisSubjectService.saveSubject(subjectSaveReq);

            AnalysisSubjectVO analysisSubjectVO = new AnalysisSubjectVO();
            analysisSubjectVO.setSubjectNo(subjectNo);
            return CallResult.success(analysisSubjectVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查看分析主题
     */
    @Override
    @Method(aliasName = "ddc.dmp.subject.get", name = "查看分析主题", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<SubjectVO> get(SubjectGetDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getSubjectNo(), "分析主题唯一标识不能为空");

            SubjectGetReq subjectGetReq = BeanCopyUtil.copyFieldsByJson(params, SubjectGetReq.class);
//        subjectGetReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            subjectGetReq.setEnterpriseNo("2000002");
            SubjectRes subjectRes = analysisSubjectService.getSubject(subjectGetReq);

            if (null == subjectRes) {
                throw new BusinessException("分析主题不存在");
            }

            SubjectVO subjectVO = BeanCopyUtil.copyFields(subjectRes, SubjectVO.class);
            return CallResult.success(subjectVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 保存分析主题和数据集关系
     */
    @Override
    @Method(aliasName = "ddc.dmp.subject.ref.save", name = "保存分析主题和数据集关系", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<SubjectDatasetRefSaveVO> saveSubjectDatasetRef(SubjectDatasetRefSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            SubjectDatasetRefSaveReq subjectDatasetRefSaveReq = BeanCopyUtil.copyFieldsByJson(params, SubjectDatasetRefSaveReq.class);
//        subjectDatasetRefSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            subjectDatasetRefSaveReq.setEnterpriseNo("2000002");
            Long subjectDatasetRefId = analysisSubjectService.saveSubjectDatasetRef(subjectDatasetRefSaveReq);

            SubjectDatasetRefSaveVO subjectDatasetRefSaveVO = new SubjectDatasetRefSaveVO();
            subjectDatasetRefSaveVO.setSubjectDatasetRefId(subjectDatasetRefId);
            return CallResult.success(subjectDatasetRefSaveVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

}
