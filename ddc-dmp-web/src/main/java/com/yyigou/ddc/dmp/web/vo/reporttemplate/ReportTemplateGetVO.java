package com.yyigou.ddc.dmp.web.vo.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
public class ReportTemplateGetVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 唯一编号
     */
    @Schema(description = "唯一编号")
    @EntityField(name = "唯一编号")
    private String templateNo;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @EntityField(name = "租户id")
    private String enterpriseNo;

    /**
     * 租户记录平台复制时的来源报表编号
     */
    private String sourceTemplateNo;

    /**
     * 报表编码，系统生成，升级版本时该字段不变
     */
    @Schema(description = "报表编码，系统生成，升级版本时该字段不变")
    @EntityField(name = "报表编码，系统生成，升级版本时该字段不变")
    private String templateCode;

    /**
     * 报表名称
     */
    @Schema(description = "报表名称")
    @EntityField(name = "报表名称")
    private String templateName;

    /**
     * 报表来源模式。1-独立报表，2-比价方案
     */
    @Schema(description = "报表来源模式。1-独立报表，2-比价方案")
    @EntityField(name = "报表来源模式。1-独立报表，2-比价方案")
    private Integer templateMode;

    /**
     * 数据集编号
     */
    @Schema(description = "数据集编号")
    @EntityField(name = "数据集编号")
    private String datasetNo;

    /**
     * 比价方案编号
     */
    @Schema(description = "比价方案编号")
    @EntityField(name = "比价方案编号")
    private String planNo;

    /**
     * 行配置
     */
    @Schema(description = "行维度")
    @EntityField(name = "行维度")
    private String rowFields;

    /**
     * 列配置
     */
    @Schema(description = "列维度")
    @EntityField(name = "列维度")
    private String columnFields;

    /**
     * 指标配置
     */
    @Schema(description = "指标配置")
    @EntityField(name = "指标配置")
    private String valueFields;


    @Schema(description = "报表业务配置")
    @EntityField(name = "报表业务配置")
    private ReportTemplateBizConfigVO bizConfigVO;

    /**
     * 数据筛选条件
     */
    @Schema(description = "数据筛选条件 JSON")
    @EntityField(name = "数据筛选条件 JSON")
    private String filterConfig;

    /**
     * antv渲染配置信息
     */
    @Schema(description = "antv渲染配置信息 JSON")
    @EntityField(name = "antv渲染配置信息 JSON")
    private String configJson;

    /**
     * 描述信息
     */
    @Schema(description = "描述信息")
    @EntityField(name = "描述信息")
    private String description;

    /**
     * 数据是否有效：0-无效，1-有效
     */
    private Integer status;

    /**
     * 启用状态 1-启用，2-禁用
     */
    private Integer enableStatus;

    /**
     * 删除标志 0：未删除 1：已删除
     */
    private Integer deleted;

    /**
     * 创建人编号
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 操作人编号
     */
    private String modifyNo;

    /**
     * 操作人名称
     */
    private String modifyName;

    /**
     * 操作时间
     */
    private String modifyTime;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否平台预置 0-否，1-是
     */
    @Schema(description = "是否平台预置 0-否，1-是")
    @EntityField(name = "是否平台预置 0-否，1-是")
    private Integer preset;


}
