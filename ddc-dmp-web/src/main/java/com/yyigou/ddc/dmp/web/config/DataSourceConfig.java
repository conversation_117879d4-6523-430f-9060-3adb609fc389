package com.yyigou.ddc.dmp.web.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2025/07/22
 */
@Configuration
public class DataSourceConfig {

    @Primary // 关键！确保 MySQL 是主数据源
    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource primaryDataSource() {
        return DruidDataSourceBuilder.create().build();
    }


    @Value("${spring.datasource.doris.url}")
    private String dorisUrl;

    @Value("${common.doris.fe.username}")
    private String dorisUsername;


    @Value("${common.doris.fe.password}")
    private String dorisPassword;


    @Bean("dorisDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid") // 复用 druid 配置
    public DataSource dorisDataSource() {
        DruidDataSource dorisDataSource = DruidDataSourceBuilder.create().build();
        dorisDataSource.setUsername(dorisUsername);
        dorisDataSource.setPassword(dorisPassword);
        dorisDataSource.setUrl(dorisUrl);
        dorisDataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        return dorisDataSource;
    }

    @Bean("dorisJdbcTemplate")
    public JdbcTemplate dorisJdbcTemplate(
            @Qualifier("dorisDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

}
