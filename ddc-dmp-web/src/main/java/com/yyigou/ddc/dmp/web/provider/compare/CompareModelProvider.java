package com.yyigou.ddc.dmp.web.provider.compare;

import cn.hutool.core.bean.BeanUtil;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelChangeStatusReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelGetReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelPageQueryReq;
import com.yyigou.ddc.dmp.model.req.comparemodel.CompareModelSaveReq;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelGetRes;
import com.yyigou.ddc.dmp.model.res.comparemodel.CompareModelPageRes;
import com.yyigou.ddc.dmp.service.comparemodel.CompareModelService;
import com.yyigou.ddc.dmp.service.util.UserHandleUtils;
import com.yyigou.ddc.dmp.web.api.compare.CompareModelAPI;
import com.yyigou.ddc.dmp.web.dto.PageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelChangeStatusDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelGetDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelPageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.comparemodel.CompareModelSaveDTO;
import com.yyigou.ddc.dmp.web.vo.comparemodel.CompareModelGetVO;
import com.yyigou.ddc.dmp.web.vo.comparemodel.CompareModelPageVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 平台端比对模型
 * <AUTHOR>
 * @date 2025/08/20
 */
@Interface(name = "比对模型")
@Component("compareModelProvider")
public class CompareModelProvider extends ServiceBaseAbstract implements CompareModelAPI {

    @Resource
    private CompareModelService compareModelService;

    @Method(aliasName = "ddc.dmp.compareModel.save", name = "新增比对模型", processState = 1, requestAuthentication = false, methodType = MethodType.ADD)
    @Override
    public CallResult<CompareModelPageVO> save(CompareModelSaveDTO params) {
        try {
            // todo-zyc 分布式锁，避免并发处理
            CompareModelSaveReq compareModelSaveReq = BeanCopyUtil.copyFieldsByJson(params, CompareModelSaveReq.class);
            compareModelSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            CompareModelPageVO compareModelPageVO = BeanCopyUtil.copyFields(compareModelService.save(compareModelSaveReq), CompareModelPageVO.class);
            return CallResult.success(compareModelPageVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    @Method(aliasName = "ddc.dmp.compareModel.update", name = "修改比对模型", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> update(CompareModelSaveDTO params) {
        try {
            // todo-zyc 分布式锁，避免并发处理
            CompareModelSaveReq compareModelSaveReq = BeanCopyUtil.copyFieldsByJson(params, CompareModelSaveReq.class);
            compareModelSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            return CallResult.success(compareModelService.update(compareModelSaveReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 获取单个比对模型信息
     */
    @Method(aliasName = "ddc.dmp.compareModel.get", name = "查询比对模型详情", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<CompareModelGetVO> get(CompareModelGetDTO params) {
        try {
            CompareModelGetReq getReq = BeanCopyUtil.copyFields(params, CompareModelGetReq.class);
            getReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            CompareModelGetRes compareModelGetRes = compareModelService.get(getReq);
            CompareModelGetVO compareModelGetVO = BeanCopyUtil.copyFields(compareModelGetRes, CompareModelGetVO.class);
            return CallResult.success(compareModelGetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 平台端获取比对模型列表
     */
    @Method(aliasName = "ddc.dmp.compareModel.pageQuery", name = "分页查询比对模型", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    @Override
    public CallResult<PageVo<CompareModelPageVO>> pageQuery(CompareModelPageQueryDTO params, PageQueryDTO pageParams) {
        try {
            CompareModelPageQueryReq pageReq = BeanCopyUtil.copyFields(params, CompareModelPageQueryReq.class);
            BeanUtil.copyProperties(pageParams, pageReq);
            pageReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            PageVo<CompareModelPageRes> pageResPageVo = compareModelService.pageQuery(pageReq);
            PageVo<CompareModelPageVO> compareModelPageVOPageVo = new PageVo<>(pageResPageVo.getPageIndex(), pageResPageVo.getPageSize(), pageResPageVo.getTotal(), BeanCopyUtil.copyFieldsList(pageResPageVo.getRows(), CompareModelPageVO.class));
            return CallResult.success(compareModelPageVOPageVo);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 启用/禁用比对模型
     */
    @Method(aliasName = "ddc.dmp.compareModel.changeStatus", name = "启用/禁用比对模型", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    @Override
    public CallResult<Boolean> changeStatus(CompareModelChangeStatusDTO params) {
        try {
            CompareModelChangeStatusReq changeStatusReq = BeanCopyUtil.copyFields(params, CompareModelChangeStatusReq.class);
            changeStatusReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            return CallResult.success(compareModelService.changeStatus(changeStatusReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 删除比对模型
     */
    @Method(aliasName = "ddc.dmp.compareModel.delete", name = "删除比对模型", processState = 1, requestAuthentication = false, methodType = MethodType.DELETE)
    @Override
    public CallResult<Boolean> delete(CompareModelGetDTO params) {
        try {
            CompareModelGetReq deleteReq = BeanCopyUtil.copyFields(params, CompareModelGetReq.class);
            deleteReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());

            return CallResult.success(compareModelService.delete(deleteReq));
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


}
