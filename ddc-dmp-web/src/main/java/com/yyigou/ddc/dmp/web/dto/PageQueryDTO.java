package com.yyigou.ddc.dmp.web.dto;

import com.yyigou.ddc.common.service.annotation.EntityField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
@Schema(description = "分页请求参数对象")
public class PageQueryDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "页码")
    private int pageIndex = 1;

    @EntityField(name = "页大小")
    private int pageSize = 20;

    @EntityField(name = "排序字段")
    private String orderBy;

}
