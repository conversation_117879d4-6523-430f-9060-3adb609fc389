package com.yyigou.ddc.dmp.web.api;

import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.PageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.reporttemplate.*;
import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateGetVO;
import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplateManagePageVO;
import com.yyigou.ddc.dmp.web.vo.reporttemplate.ReportTemplatePageVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface ReportTemplateAPI extends ServiceBase {
    CallResult<ReportTemplateGetVO> get(ReportTemplateGetDTO params);

    CallResult<PageVo<ReportTemplatePageVO>> pageQuery(ReportTemplatePageQueryDTO params, PageQueryDTO pageParams);

    CallResult<ReportTemplateGetVO> platformSave(ReportTemplateSaveDTO params);

    CallResult<Boolean> update(ReportTemplateUpdateDTO params);

    CallResult<Boolean> platformChangeStatus(ReportTemplateChangeStatusDTO params);

    CallResult<Boolean> delete(ReportTemplateDeleteDTO params);

    CallResult<PageVo<ReportTemplateManagePageVO>> managePageQuery(ReportTemplatePageQueryDTO params, PageQueryDTO pageParams);

    CallResult<Boolean> manageChangeStatus(ReportTemplateChangeStatusDTO params);

    CallResult<Boolean> manageAuth(ReportTemplateAuthDTO params);

    CallResult<List<Map<String, Object>>> useQueryData(ReportTemplateQueryDataDTO params);
}
