package com.yyigou.ddc.dmp.web.vo.reporttemplate;

import com.yyigou.ddc.common.service.annotation.EntityField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
public class ReportTemplateCustomTreeVO extends ReportTemplateFieldVO implements Serializable {

    /**
     * 子节点
     */
    @Schema(description = "子节点",implementation = ReportTemplateCustomTreeVO.class)
    @EntityField(name = "子节点")
    private List<ReportTemplateCustomTreeVO> children;


}
