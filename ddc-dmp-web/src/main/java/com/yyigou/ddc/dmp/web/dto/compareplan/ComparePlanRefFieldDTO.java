package com.yyigou.ddc.dmp.web.dto.compareplan;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class ComparePlanRefFieldDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @EntityField(name = "字段来源类型。1-比对对象，2-比对维度，3-比对指标，4-基准指标，5-计算指标")
    private Integer sourceType;

    @EntityField(name = "比价方案指标（比对指标/基准指标）唯一标识")
    private String comparePlanMetricNo;

    @EntityField(name = "比价方案计算指标唯一标识")
    private String comparePlanCalMetricNo;

    @EntityField(name = "模型（比对对象/比对维度）字段唯一标识")
    private String compareModelDimNo;


}
