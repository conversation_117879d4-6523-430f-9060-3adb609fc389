package com.yyigou.ddc.dmp.web.dto.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/21
 */
@Data
public class CompareModelChangeStatusDTO implements Serializable {

    /**
     * 模型编号
     */
    @EntityField(name = "模型编号")
    private String modelNo;

    /**
     * 启用状态
     */
    @EntityField(name = "启用状态。1-启用，2-禁用")
    private Integer enableStatus;


}
