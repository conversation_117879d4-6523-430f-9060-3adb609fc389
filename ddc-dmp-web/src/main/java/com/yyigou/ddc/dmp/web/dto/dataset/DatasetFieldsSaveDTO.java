package com.yyigou.ddc.dmp.web.dto.dataset;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class DatasetFieldsSaveDTO implements Serializable {
    private String enterpriseNo;
    private String datasetNo;
    private String datasetFieldNo;
    private String catalogName;
    private String schemaName;
    private String tableName;
    private String fieldCode;
    private String fieldName;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private Timestamp opTimestamp;
}
