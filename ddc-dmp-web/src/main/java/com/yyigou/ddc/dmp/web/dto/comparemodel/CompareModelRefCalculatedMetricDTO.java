package com.yyigou.ddc.dmp.web.dto.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefCalculatedMetricDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @EntityField(name = "计算指标编号。用于编辑场景")
    private String calMetricNo;

    @EntityField(name = "计算指标编码")
    private String calMetricCode;

    @EntityField(name = "计算指标名称")
    private String calMetricName;

    @EntityField(name = "描述信息")
    private String description;

    @EntityField(name = "绑定的基准指标范围类型.1-全部，2-指定")
    private Integer baselineMetricScopeType;

    @EntityField(name = "指定的基准指标")
    private List<String> baselineMetricScope;

    @EntityField(name = "绑定的比对指标范围类型.1-全部，2-指定")
    private Integer compareMetricScopeType;

    @EntityField(name = "指定的比对指标")
    private List<String> compareMetricScope;


    @EntityField(name = "表达式")
    private String expression;

    @EntityField(name = "格式化配置")
    private CalculatedMetricFormatConfigDTO formatConfig;



}
