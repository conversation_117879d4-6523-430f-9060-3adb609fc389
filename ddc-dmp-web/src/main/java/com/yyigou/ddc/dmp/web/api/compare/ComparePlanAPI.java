package com.yyigou.ddc.dmp.web.api.compare;

import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.PageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanChangeStatusDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanGetDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanPageQueryDTO;
import com.yyigou.ddc.dmp.web.dto.compareplan.ComparePlanSaveDTO;
import com.yyigou.ddc.dmp.web.vo.compareplan.ComparePlanGetVO;
import com.yyigou.ddc.dmp.web.vo.compareplan.ComparePlanPageVO;

/**
 * 平台端比对模型
 * <AUTHOR>
 * @date 2025/08/20
 */
public interface ComparePlanAPI extends ServiceBase {

    CallResult<ComparePlanPageVO> save(ComparePlanSaveDTO params);

    CallResult<Boolean> update(ComparePlanSaveDTO params);

    CallResult<ComparePlanGetVO> get(ComparePlanGetDTO params);

    CallResult<PageVo<ComparePlanPageVO>> pageQuery(ComparePlanPageQueryDTO params, PageQueryDTO pageParams);

    CallResult<Boolean> changeStatus(ComparePlanChangeStatusDTO params);

    CallResult<Boolean> delete(ComparePlanGetDTO params);
}
