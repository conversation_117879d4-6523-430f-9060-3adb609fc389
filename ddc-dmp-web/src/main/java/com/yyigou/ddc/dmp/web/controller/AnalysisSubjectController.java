package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectDatasetRefSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.SubjectSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.SubjectRes;
import com.yyigou.ddc.dmp.service.subject.AnalysisSubjectService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectDatasetRefSaveDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectGetDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.SubjectSaveDTO;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.subject.AnalysisSubjectVO;
import com.yyigou.ddc.dmp.web.vo.subject.SubjectDatasetRefSaveVO;
import com.yyigou.ddc.dmp.web.vo.subject.SubjectVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 分析主题前端控制器
 */
@RestController
@RequestMapping("/subject")
public class AnalysisSubjectController {
    @Resource
    private AnalysisSubjectService analysisSubjectService;

    /**
     * 保存分析主题
     */
    @PostMapping("/save")
    public AnalysisSubjectVO save(@RequestBody CommonDTO<SubjectSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        SubjectSaveReq subjectSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), SubjectSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        subjectSaveReq.setEnterpriseNo("2000002");
        String subjectNo = analysisSubjectService.saveSubject(subjectSaveReq);

        AnalysisSubjectVO analysisSubjectVO = new AnalysisSubjectVO();
        analysisSubjectVO.setSubjectNo(subjectNo);
        return analysisSubjectVO;
    }

    /**
     * 查看分析主题
     */
    @GetMapping("/get")
    public SubjectVO get(@RequestBody CommonDTO<SubjectGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);
        ValidatorUtil.checkEmptyThrowEx(getDto.getParams().getSubjectNo(), "分析主题唯一标识不能为空");

        SubjectGetReq subjectGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), SubjectGetReq.class);
//        detailGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        subjectGetReq.setEnterpriseNo("2000002");
        SubjectRes subjectRes = analysisSubjectService.getSubject(subjectGetReq);

        if (null == subjectRes) {
            throw new BusinessException("分析主题不存在");
        }

        SubjectVO datasetVO = BeanCopyUtil.copyFields(subjectRes, SubjectVO.class);
        return datasetVO;
    }

    /**
     * 保存分析主题和数据集关系
     */
    @PostMapping("/ref/save")
    public SubjectDatasetRefSaveVO saveSubjectDatasetRef(@RequestBody CommonDTO<SubjectDatasetRefSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        SubjectDatasetRefSaveReq subjectDatasetRefSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), SubjectDatasetRefSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        subjectDatasetRefSaveReq.setEnterpriseNo("2000002");
        Long subjectDatasetRefId = analysisSubjectService.saveSubjectDatasetRef(subjectDatasetRefSaveReq);

        SubjectDatasetRefSaveVO subjectDatasetRefSaveVO = new SubjectDatasetRefSaveVO();
        subjectDatasetRefSaveVO.setSubjectDatasetRefId(subjectDatasetRefId);
        return subjectDatasetRefSaveVO;
    }
}