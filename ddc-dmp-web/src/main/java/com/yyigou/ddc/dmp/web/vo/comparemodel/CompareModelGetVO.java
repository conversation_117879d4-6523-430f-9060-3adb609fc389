package com.yyigou.ddc.dmp.web.vo.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelGetVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @EntityField(name = "模型唯一编号.更新场景必传")
    private String modelNo;

    @EntityField(name = "模型名称")
    private String modelName;

    @EntityField(name = "模型编码")
    private String modelCode;

    @EntityField(name = "描述信息")
    private String description;

    @EntityField(name = "启用状态")
    private Integer enableStatus;

    @EntityField(name = "比对数据集编号")
    private String compareDatasetNo;

    @EntityField(name = "比对数据集名称")
    private String compareDatasetName;


    @EntityField(name = "基准数据集集合")
    private List<CompareModelRefDatasetVO> baselineDatasetList;

    @EntityField(name = "比对对象字段集合")
    private List<CompareModelRefFieldVO> compareObjectList;

    @EntityField(name = "比对维度字段集合")
    private List<CompareModelRefFieldVO> compareDimList;

    @EntityField(name = "比对指标集合")
    private List<CompareModelRefMetricVO> compareMetricList;

    @EntityField(name = "比对数据集查询条件集合")
    private List<CompareModelRefQueryConditionVO> datasetQueryConditionList;

    @EntityField(name = "基准指标集合")
    private List<CompareModelRefBaselineMetricVO> baselineMetricList;

    @EntityField(name = "计算指标集合")
    private List<CompareModelRefCalculatedMetricVO> calculatedMetricList;



}
