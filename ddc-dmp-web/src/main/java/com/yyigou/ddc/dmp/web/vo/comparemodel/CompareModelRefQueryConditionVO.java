package com.yyigou.ddc.dmp.web.vo.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CompareModelRefQueryConditionVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 唯一标识，用于更新
     */
    @EntityField(name = "唯一标识，用于更新")
    private String compareModelQueryConditionNo;


    @EntityField(name = "查询条件")
    private String datasetQueryConditionNo;

    @EntityField(name = "查询条件名称")
    private String datasetQueryConditionName;

    /**
     * 是否是基准指标的公共查询条件
     */
    @EntityField(name = "是否是基准指标的公共查询条件。1-是，0-否（默认）")
    private Integer isCommon = 0;





}
