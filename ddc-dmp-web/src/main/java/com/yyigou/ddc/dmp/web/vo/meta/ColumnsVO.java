package com.yyigou.ddc.dmp.web.vo.meta;

import lombok.Data;

import java.io.Serializable;

@Data
public class ColumnsVO implements Serializable {
    private String tableCatalog;
    private String tableSchema;
    private String tableName;
    private String columnName;
    private Long ordinalPosition;
    private String columnDefault;
    private String isNullable;
    private String dataType;
    private Long characterMaximumLength;
    private Long characterOctetLength;
    private Long numericPrecision;
    private Long numericScale;
    private Long datetimePrecision;
    private String characterSetName;
    private String collationName;
    private String columnType;
    private String columnKey;
    private String extra;
    private String privileges;
    private String columnComment;
    private Long columnSize;
    private Long decimalDigits;
    private String generationExpression;
    private Long srsId;
}
