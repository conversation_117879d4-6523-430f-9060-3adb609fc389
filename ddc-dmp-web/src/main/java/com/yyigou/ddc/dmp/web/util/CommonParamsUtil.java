package com.yyigou.ddc.dmp.web.util;

import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.CommonPageDTO;

/**
 * <AUTHOR>
 * @date 2025/07/21
 */
public class CommonParamsUtil {

    public static void validateCommonPageDTO(CommonPageDTO<?,?> commonPageDTO) {
        ValidatorUtil.checkEmptyThrowEx(commonPageDTO, "参数不能为空");
        ValidatorUtil.checkEmptyThrowEx(commonPageDTO.getParams(), "参数不能为空");
        ValidatorUtil.checkEmptyThrowEx(commonPageDTO.getPageParams(), "分页参数不能为空");
    }

    public static void validateCommonDTO(CommonDTO<?> commonDTO) {
        ValidatorUtil.checkEmptyThrowEx(commonDTO, "参数不能为空");
        ValidatorUtil.checkEmptyThrowEx(commonDTO.getParams(), "参数不能为空");
    }


}
