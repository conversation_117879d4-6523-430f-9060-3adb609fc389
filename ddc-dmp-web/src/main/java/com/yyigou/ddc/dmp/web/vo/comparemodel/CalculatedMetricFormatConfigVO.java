package com.yyigou.ddc.dmp.web.vo.comparemodel;

import com.yyigou.ddc.common.service.annotation.EntityField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/08/20
 */
@Data
public class CalculatedMetricFormatConfigVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @EntityField(name = "单位编码。NONE-无格式，YUAN-元")
    private String unitCode;

    @EntityField(name = "小数位")
    private Integer decimalPlaces;

    @EntityField(name = "格式类型。PERCENTAGE-百分比，THOUSAND_SEPARATOR-千分位，NONE-无格式")
    private String formatType;


}
