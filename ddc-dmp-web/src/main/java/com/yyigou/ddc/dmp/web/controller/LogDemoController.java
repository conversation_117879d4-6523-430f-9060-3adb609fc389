package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.result.RestResult;
import com.yyigou.ddc.dmp.common.util.LogUtil;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志演示Controller
 * 展示统一日志格式的使用方法
 */
@RestController
@RequestMapping("/api/log-demo")
public class LogDemoController {
    
    private static final Logger logger = LogUtil.getLogger(LogDemoController.class);
    
    /**
     * 演示业务日志记录
     */
    @GetMapping("/business")
    public RestResult<String> businessLogDemo(@RequestParam(defaultValue = "test") String param) {
        // 记录业务操作日志
        LogUtil.logBusiness(logger, "USER_QUERY", "用户查询业务数据", param);
        
        // 模拟业务处理
        try {
            Thread.sleep(100); // 模拟处理时间
            
            LogUtil.logBusiness(logger, "USER_QUERY", "业务数据查询成功", "result", "success");
            return RestResult.success("业务操作完成");
            
        } catch (Exception e) {
            LogUtil.logError(logger, "USER_QUERY", "业务操作失败", e, param);
            return RestResult.error("业务操作失败");
        }
    }
    
    /**
     * 演示数据库操作日志
     */
    @PostMapping("/database")
    public RestResult<Map<String, Object>> databaseLogDemo(@RequestBody Map<String, Object> data) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 模拟数据库查询
            LogUtil.logDatabase(logger, "user_info", "SELECT", "id = " + data.get("id"), "1 record found");
            
            // 模拟数据库更新
            LogUtil.logDatabase(logger, "user_info", "UPDATE", "id = " + data.get("id"), "1 record updated");
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", data.get("id"));
            result.put("status", "updated");
            result.put("timestamp", System.currentTimeMillis());
            
            return RestResult.success(result);
            
        } finally {
            // 记录性能日志
            long duration = System.currentTimeMillis() - startTime;
            LogUtil.logPerformance(logger, "databaseLogDemo", duration, data);
        }
    }
    
    /**
     * 演示API调用日志
     */
    @GetMapping("/api-call")
    public RestResult<String> apiCallDemo(@RequestParam(defaultValue = "external-api") String apiName) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 模拟外部API调用
            Thread.sleep(200); // 模拟网络延迟
            
            String mockResponse = "{\"status\": \"success\", \"data\": \"mock data\"}";
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录API调用日志
            LogUtil.logApiCall(logger, apiName, "https://api.example.com/data", "GET", 
                    Map.of("param", "value"), mockResponse, duration);
            
            return RestResult.success("API调用成功");
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            LogUtil.logError(logger, "API_CALL", "外部API调用失败", e, apiName);
            return RestResult.error("API调用失败");
        }
    }
    
    /**
     * 演示性能日志记录
     */
    @GetMapping("/performance")
    public RestResult<String> performanceLogDemo(@RequestParam(defaultValue = "100") int sleepTime) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 模拟耗时操作
            Thread.sleep(sleepTime);
            
            return RestResult.success("操作完成");
            
        } catch (Exception e) {
            LogUtil.logError(logger, "PERFORMANCE_TEST", "性能测试失败", e, sleepTime);
            return RestResult.error("操作失败");
            
        } finally {
            // 记录性能日志
            long duration = System.currentTimeMillis() - startTime;
            LogUtil.logPerformance(logger, "performanceLogDemo", duration, sleepTime);
        }
    }
    
    /**
     * 演示错误日志记录
     */
    @GetMapping("/error")
    public RestResult<String> errorLogDemo(@RequestParam(defaultValue = "false") boolean throwError) {
        try {
            if (throwError) {
                throw new RuntimeException("这是一个模拟的错误");
            }
            
            logger.info("正常执行，没有错误");
            return RestResult.success("执行成功");
            
        } catch (Exception e) {
            LogUtil.logError(logger, "ERROR_DEMO", "演示错误日志记录", e, throwError);
            return RestResult.error("执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示用户上下文日志
     */
    @PostMapping("/user-context")
    public RestResult<String> userContextDemo(@RequestBody Map<String, String> userInfo) {
        try {
            // 设置用户上下文
            LogUtil.setUserContext(
                    userInfo.get("userId"), 
                    userInfo.get("userName"), 
                    userInfo.get("userRole")
            );
            
            // 记录业务操作
            LogUtil.logBusiness(logger, "USER_OPERATION", "用户执行业务操作", userInfo);
            
            // 模拟业务处理
            Thread.sleep(50);
            
            logger.info("用户上下文日志演示完成");
            return RestResult.success("用户上下文设置成功");
            
        } catch (Exception e) {
            LogUtil.logError(logger, "USER_CONTEXT", "用户上下文设置失败", e, userInfo);
            return RestResult.error("操作失败");
            
        } finally {
            // 清除用户上下文（通常在拦截器中处理）
            LogUtil.clearUserContext();
        }
    }
}